# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*



# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache



# Playwright
test-results/
playwright-report/
playwright/.cache/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Testing
/coverage

# Misc

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Large source documents
sources_docs/*.pdf
sources_docs/*.PDF
*.pdf
*.PDF

# Reference images (screenshots and demo images)
ref_images/
*.png
*.jpg
*.jpeg
*.gif

# Documentation and database folders
docs/
database/ 
