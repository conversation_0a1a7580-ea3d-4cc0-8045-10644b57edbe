# Design Document

## Overview

This design document outlines the implementation approach for enhancing the quiz system in the USMLE Trivia App. The quiz system will provide three distinct quiz modes (Quick Quiz, Timed Test, and Custom Quiz), each with appropriate configurations and user experiences. The system will fetch questions from the Supabase database, track user performance, provide immediate feedback, and integrate with the achievement and leaderboard systems. The design focuses on creating an engaging, educational, and personalized quiz experience while ensuring performance and reliability.

## Architecture

The quiz system follows a client-server architecture where:

1. The React client handles UI rendering, user interactions, and state management
2. Supabase provides database storage, authentication, and real-time updates
3. Custom hooks and utility functions manage quiz logic and data processing

### Quiz Flow

```mermaid
sequenceDiagram
    participant User
    participant UI as Quiz UI
    participant Hooks as Quiz Hooks
    participant Utils as Database Utils
    participant DB as Supabase Database
    
    %% Quiz Setup
    User->>UI: Select quiz type
    alt Quick Quiz
        UI->>User: Start quiz immediately
    else Timed Test
        UI->>User: Show brief instructions
        User->>UI: Confirm start
    else Custom Quiz
        UI->>User: Show configuration options
        User->>UI: Configure and start quiz
    end
    
    %% Question Fetching
    UI->>Hooks: Request questions
    Hooks->>Utils: fetchQuizQuestions()
    Utils->>DB: Query questions based on criteria
    DB-->>Utils: Return matching questions
    Utils->>Hooks: Format and shuffle questions
    Hooks-->>UI: Provide questions for display
    
    %% Quiz Interaction
    loop For each question
        UI->>User: Display question and options
        alt Timed question
            UI->>User: Show countdown timer
        end
        User->>UI: Select answer
        UI->>Hooks: Process answer
        Hooks-->>UI: Provide feedback
        UI->>User: Show correct/incorrect and explanation
        User->>UI: Next question
    end
    
    %% Results Processing
    UI->>Hooks: Complete quiz
    Hooks->>Utils: saveQuizResults()
    Utils->>DB: Store results in leaderboard
    Utils->>DB: Update user statistics
    Utils->>DB: Check for achievements
    DB-->>Utils: Return updated data
    Utils-->>Hooks: Provide results summary
    Hooks-->>UI: Display results and achievements
    UI->>User: Show quiz results
```

## Components and Interfaces

### Quiz Components

1. **QuizSelector**
   - Displays available quiz types
   - Handles quiz type selection
   - Routes to appropriate quiz setup

2. **QuizSetup**
   - Configures quiz parameters based on type
   - Handles custom quiz configuration
   - Validates configuration and starts quiz

3. **QuizPage**
   - Main quiz interface for all quiz types
   - Displays questions and options
   - Handles answer selection and validation
   - Shows feedback and explanations
   - Manages quiz navigation and completion

4. **QuizProgressBar**
   - Displays progress through quiz
   - Shows question number and total
   - Visualizes time remaining for timed quizzes

5. **ResultsPage**
   - Displays quiz score and statistics
   - Shows performance breakdown
   - Displays earned achievements
   - Provides options to retry or share results

### Quiz Hooks

```jsx
// useQuizData.js
const useQuizData = (quizType, options = {}) => {
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [quizSettings, setQuizSettings] = useState({
    quizType,
    ...options
  });
  
  // Fetch questions based on quiz type and options
  const fetchQuestions = useCallback(async () => {
    try {
      setLoading(true);
      
      const fetchedQuestions = await fetchQuizQuestions({
        quizType,
        categoryId: options.categoryId,
        difficulty: options.difficulty,
        questionCount: options.questionCount,
        selectedTags: options.selectedTags
      });
      
      setQuestions(fetchedQuestions);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [quizType, options]);
  
  // Fetch questions on mount
  useEffect(() => {
    fetchQuestions();
  }, [fetchQuestions]);
  
  return {
    questions,
    loading,
    error,
    quizSettings,
    updateQuizSettings: (newSettings) => setQuizSettings(prev => ({...prev, ...newSettings})),
    refetch: fetchQuestions
  };
};

// useCustomQuizSetup.js
const useCustomQuizSetup = () => {
  // State for quiz configuration
  const [isSimpleMode, setIsSimpleMode] = useState(true);
  const [categories, setCategories] = useState({ subjects: [], systems: [], topics: [] });
  const [selectedSubject, setSelectedSubject] = useState('');
  const [selectedSystem, setSelectedSystem] = useState('');
  const [selectedTopic, setSelectedTopic] = useState('');
  const [difficulty, setDifficulty] = useState('');
  const [questionCount, setQuestionCount] = useState(10);
  const [timing, setTiming] = useState('timed');
  
  // Fetch categories and calculate available questions
  // ...
  
  return {
    // Mode
    isSimpleMode,
    setIsSimpleMode,
    
    // Category data
    categories,
    simpleCategories,
    
    // State setters and getters
    selectedSubject,
    setSelectedSubject,
    // ...other state
    
    // Derived data
    availableQuestions,
    canStart,
    
    // Actions
    createNavigationState,
    createCustomQuiz
  };
};
```

## Data Models

### Quiz Configuration

```typescript
interface QuizConfig {
  quizType: 'quick' | 'timed' | 'custom';
  questionCount: number;
  timePerQuestion?: number | null;
  showExplanations: boolean;
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  categoryId?: string | null;
  selectedTags?: string[];
  timing?: 'timed' | 'self';
}
```

### Question Model

```typescript
interface Question {
  id: string;
  text: string;
  options: string[];
  correctOptionId: number;
  explanation?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  tags: {
    id: string;
    name: string;
    type: 'subject' | 'system' | 'topic';
  }[];
}
```

### Quiz Result Model

```typescript
interface QuizResult {
  quizType: 'quick' | 'timed' | 'custom';
  score: number;
  maxScore: number;
  timeTaken: number;
  categoryId?: string | null;
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  questionCount: number;
  answers: {
    questionId: string;
    selectedOption: number | null;
    isCorrect: boolean;
    timedOut: boolean;
    timeSpent: number | null;
  }[];
  config?: QuizConfig;
}
```

## Database Schema

The quiz system relies on the following database tables:

### questions

```sql
CREATE TABLE public.questions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  question_text TEXT NOT NULL,
  options TEXT[] NOT NULL,
  correct_option_id INTEGER NOT NULL,
  explanation TEXT,
  difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### tags

```sql
CREATE TABLE public.tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  type TEXT CHECK (type IN ('subject', 'system', 'topic')),
  UNIQUE(name, type)
);
```

### question_tags

```sql
CREATE TABLE public.question_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  question_id UUID NOT NULL REFERENCES public.questions(id) ON DELETE CASCADE,
  tag_id UUID NOT NULL REFERENCES public.tags(id) ON DELETE CASCADE,
  UNIQUE(question_id, tag_id)
);
```

### user_seen_questions

```sql
CREATE TABLE public.user_seen_questions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  question_id UUID NOT NULL REFERENCES public.questions(id) ON DELETE CASCADE,
  seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, question_id)
);
```

### leaderboard

```sql
CREATE TABLE public.leaderboard (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  quiz_type TEXT NOT NULL CHECK (quiz_type IN ('quick', 'timed', 'custom')),
  score INTEGER NOT NULL,
  max_score INTEGER NOT NULL,
  accuracy DECIMAL(5,2) NOT NULL,
  time_taken INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  category_id UUID REFERENCES public.tags(id),
  difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard', 'mixed')),
  question_count INTEGER,
  CONSTRAINT valid_score CHECK (score <= max_score)
);
```

## Quiz Logic

### Question Selection Algorithm

1. Determine the number of questions needed based on quiz type
2. Fetch user's previously seen questions
3. Apply filters based on quiz configuration (category, difficulty)
4. Prioritize unseen questions
5. If not enough unseen questions, include previously seen questions
6. Shuffle questions to randomize order
7. Mark selected questions as seen in the database

```javascript
export const fetchQuizQuestions = async (options = {}) => {
  try {
    const {
      quizType = 'quick',
      categoryId = null,
      difficulty = 'mixed',
      questionCount = null,
      selectedTags = []
    } = options;
    
    // Determine question count based on quiz type
    let count = questionCount;
    if (!count) {
      switch (quizType) {
        case 'quick': count = 10; break;
        case 'timed': count = 20; break;
        case 'custom': count = 10; break;
        default: count = 10;
      }
    }
    
    // Get user's previously seen questions
    const { data: { user } } = await supabase.auth.getUser();
    let seenQuestionIds = [];
    if (user) {
      const { data: seenData } = await supabase
        .from('user_seen_questions')
        .select('question_id')
        .eq('user_id', user.id)
        .limit(500);
      
      if (seenData) {
        seenQuestionIds = seenData.map(item => item.question_id);
      }
    }
    
    // Build query with filters
    let query = supabase.from('questions').select(`
      id, question_text, options, correct_option_id, explanation, difficulty,
      question_tags(tag_id, tags(id, name, type))
    `);
    
    // Apply filters
    if (categoryId) {
      query = query.eq('question_tags.tag_id', categoryId);
    }
    
    if (difficulty && difficulty !== 'mixed') {
      query = query.eq('difficulty', difficulty);
    }
    
    if (selectedTags && selectedTags.length > 0) {
      query = query.or(selectedTags.map(tagId => `question_tags.tag_id.eq.${tagId}`).join(','));
    }
    
    // Exclude seen questions if possible
    if (seenQuestionIds.length > 0 && seenQuestionIds.length < 400) {
      query = query.not('id', 'in', `(${seenQuestionIds.join(',')})`);
    }
    
    // Execute query
    const { data, error } = await query.order('id', { ascending: false }).limit(count);
    
    if (error) throw error;
    
    // If not enough unseen questions, fetch any questions
    if (data.length < count) {
      // Fallback query logic
      // ...
    }
    
    // Format and shuffle questions
    const formattedQuestions = data.map(question => ({
      id: question.id,
      text: question.question_text,
      options: question.options,
      correctOptionId: question.correct_option_id,
      explanation: question.explanation,
      difficulty: question.difficulty,
      tags: question.question_tags.map(tag => ({
        id: tag.tags?.id,
        name: tag.tags?.name,
        type: tag.tags?.type
      })).filter(tag => tag.id)
    }));
    
    const shuffledQuestions = formattedQuestions.sort(() => Math.random() - 0.5);
    
    // Mark questions as seen
    if (user) {
      const seenQuestions = shuffledQuestions.map(q => ({
        user_id: user.id,
        question_id: q.id,
        seen_at: new Date().toISOString()
      }));
      
      await supabase
        .from('user_seen_questions')
        .upsert(seenQuestions, { onConflict: ['user_id', 'question_id'] });
    }
    
    return shuffledQuestions;
  } catch (error) {
    throw error;
  }
};
```

### Quiz Result Processing

1. Calculate score and accuracy
2. Record results in leaderboard table
3. Update user statistics (overall and by category)
4. Check for achievements
5. Update user's position on leaderboards

## User Interface Design

### Quiz Selection Screen

The quiz selection screen will display the three quiz types with visual cards:

1. **Quick Quiz**: Emphasize speed and convenience
2. **Timed Test**: Highlight exam simulation aspect
3. **Custom Quiz**: Show configuration options

### Quiz Interface

The quiz interface will be consistent across quiz types with the following elements:

1. **Progress Bar**: Shows current question and total
2. **Timer**: Displays time remaining for timed quizzes
3. **Question Display**: Shows question text clearly
4. **Options**: Displays answer options with clear selection states
5. **Feedback**: Shows correct/incorrect indicators after answering
6. **Explanation**: Displays explanation text when appropriate
7. **Navigation**: Provides next/previous buttons as needed

### Results Screen

The results screen will display:

1. **Score Circle**: Visual representation of score
2. **Accuracy**: Percentage of correct answers
3. **Time Taken**: Total time spent on quiz
4. **Category Breakdown**: Performance by subject area
5. **Achievements**: Any new achievements earned
6. **Action Buttons**: Options to retry or return home

## Error Handling

### Question Fetching Errors

1. Display error message with option to retry
2. Provide fallback questions if possible
3. Log error details for debugging

### Answer Submission Errors

1. Retry submission with exponential backoff
2. Store answers locally until submission succeeds
3. Provide feedback about connection issues

### Result Saving Errors

1. Retry saving with exponential backoff
2. Cache results locally until saving succeeds
3. Allow user to continue while retrying in background

## Performance Considerations

1. **Efficient Question Loading**:
   - Fetch questions in batches
   - Implement caching for frequently accessed questions
   - Preload next question while user answers current one

2. **Optimized Database Queries**:
   - Use proper indexing for question filtering
   - Limit result sets to necessary fields
   - Use pagination for large result sets

3. **UI Performance**:
   - Implement virtualization for long option lists
   - Use React.memo for pure components
   - Optimize animations for smooth transitions

## Testing Strategy

### Unit Tests

1. Test question selection algorithm
2. Validate scoring calculations
3. Test timer functionality
4. Verify achievement triggers

### Integration Tests

1. Test complete quiz flows for each quiz type
2. Validate result saving and statistics updates
3. Test leaderboard integration
4. Verify achievement awarding

### End-to-End Tests

1. Complete user journeys through different quiz types
2. Test error scenarios and recovery
3. Verify mobile and desktop responsiveness

## Future Enhancements

1. **Spaced Repetition**: Implement algorithm to optimize question repetition based on user performance
2. **Question Difficulty Adaptation**: Dynamically adjust question difficulty based on user performance
3. **Study Groups**: Allow users to create and share custom quizzes
4. **Offline Mode**: Enable quiz taking without internet connection
5. **Question Bookmarking**: Allow users to save questions for later review