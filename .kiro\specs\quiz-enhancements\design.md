# Design Document

## Overview

This design document outlines the architecture and implementation details for enhancing the USMLE Trivia App's quiz functionality. The focus is on improving the user experience, adding personalized features, optimizing performance, and ensuring the application is production-ready.

## Architecture

The USMLE Trivia App follows a client-side architecture with React for the frontend and Supabase as the backend-as-a-service. The enhancements will build upon this existing architecture while adding new components and functionality.

### High-Level Architecture

```mermaid
graph TD
    Client[Client Browser] --> React[React Application]
    React --> SupabaseClient[Supabase Client]
    SupabaseClient --> SupabaseAPI[Supabase REST API]
    SupabaseClient --> SupabaseRealtime[Supabase Realtime]
    SupabaseAPI --> SupabaseDB[(Supabase Database)]
    SupabaseRealtime --> SupabaseDB
    
    React --> QuizComponents[Quiz Components]
    React --> ProfileComponents[Profile Components]
    React --> RecommendationComponents[Recommendation Components]
    
    QuizComponents --> QuizHooks[Quiz Hooks]
    ProfileComponents --> <PERSON>Hooks[Profile Hooks]
    RecommendationComponents --> RecommendationHooks[Recommendation Hooks]
    
    QuizHooks --> SupabaseClient
    ProfileHooks --> SupabaseClient
    RecommendationHooks --> SupabaseClient
    
    subgraph "Enhanced Quiz Features"
        QuizComponents
        QuizHooks
        RecommendationComponents
        RecommendationHooks
        OfflineSupport[Offline Support]
        PerformanceOptimization[Performance Optimization]
    end
```

### Database Schema Extensions

The quiz enhancements will require the following additions to the database schema but you shd use always supabase mcp tools to get live schema and run migrations, ditch the local sql migrations because they are behind:

1. **User Preferences Table**
   ```sql
   CREATE TABLE public.user_preferences (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
     quiz_preferences JSONB DEFAULT '{}'::jsonb,
     ui_preferences JSONB DEFAULT '{}'::jsonb,
     study_goals JSONB DEFAULT '[]'::jsonb,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     
     UNIQUE(user_id)
   );
   
   -- Index for faster queries
   CREATE INDEX user_preferences_user_id_idx ON public.user_preferences(user_id);
   ```

2. **Quiz Templates Table**
   ```sql
   CREATE TABLE public.quiz_templates (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
     name TEXT NOT NULL,
     description TEXT,
     configuration JSONB NOT NULL,
     is_favorite BOOLEAN DEFAULT false,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   
   -- Index for faster queries
   CREATE INDEX quiz_templates_user_id_idx ON public.quiz_templates(user_id);
   ```

3. **Bookmarked Explanations Table**
   ```sql
   CREATE TABLE public.bookmarked_explanations (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
     question_id UUID NOT NULL REFERENCES public.questions(id) ON DELETE CASCADE,
     notes TEXT,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     
     UNIQUE(user_id, question_id)
   );
   
   -- Indexes for faster queries
   CREATE INDEX bookmarked_explanations_user_id_idx ON public.bookmarked_explanations(user_id);
   CREATE INDEX bookmarked_explanations_question_id_idx ON public.bookmarked_explanations(question_id);
   ```

4. **Performance Trends View**
   ```sql
   CREATE OR REPLACE VIEW public.user_performance_trends AS
   WITH user_quiz_results AS (
     SELECT
       l.user_id,
       l.quiz_type,
       l.category_id,
       l.difficulty,
       l.accuracy,
       l.created_at,
       DATE_TRUNC('day', l.created_at) AS quiz_date
     FROM
       public.leaderboard l
   ),
   daily_averages AS (
     SELECT
       user_id,
       quiz_date,
       category_id,
       AVG(accuracy) AS avg_accuracy,
       COUNT(*) AS quiz_count
     FROM
       user_quiz_results
     GROUP BY
       user_id, quiz_date, category_id
   )
   SELECT
     user_id,
     quiz_date,
     category_id,
     avg_accuracy,
     quiz_count,
     AVG(avg_accuracy) OVER (
       PARTITION BY user_id, category_id
       ORDER BY quiz_date
       ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
     ) AS seven_day_trend,
     CASE
       WHEN LAG(avg_accuracy) OVER (PARTITION BY user_id, category_id ORDER BY quiz_date) IS NULL THEN 0
       ELSE avg_accuracy - LAG(avg_accuracy) OVER (PARTITION BY user_id, category_id ORDER BY quiz_date)
     END AS daily_change
   FROM
     daily_averages
   ORDER BY
     user_id, category_id, quiz_date;
   ```

5. **Quiz Recommendations View**
   ```sql
   CREATE OR REPLACE VIEW public.quiz_recommendations AS
   WITH user_category_performance AS (
     SELECT
       l.user_id,
       qt.tag_id AS category_id,
       t.name AS category_name,
       t.type AS category_type,
       AVG(l.accuracy) AS avg_accuracy,
       COUNT(DISTINCT l.id) AS attempt_count,
       MAX(l.created_at) AS last_attempt
     FROM
       public.leaderboard l
     JOIN
       public.questions q ON q.id = ANY(l.questions)
     JOIN
       public.question_tags qt ON qt.question_id = q.id
     JOIN
       public.tags t ON t.id = qt.tag_id
     GROUP BY
       l.user_id, qt.tag_id, t.name, t.type
   ),
   user_weak_categories AS (
     SELECT
       user_id,
       category_id,
       category_name,
       category_type,
       avg_accuracy,
       attempt_count,
       last_attempt,
       CASE
         WHEN avg_accuracy < 60 THEN 'high'
         WHEN avg_accuracy < 80 THEN 'medium'
         ELSE 'low'
       END AS priority
     FROM
       user_category_performance
     WHERE
       attempt_count > 0
   ),
   user_missing_categories AS (
     SELECT
       u.id AS user_id,
       t.id AS category_id,
       t.name AS category_name,
       t.type AS category_type,
       0 AS avg_accuracy,
       0 AS attempt_count,
       NULL AS last_attempt,
       'explore' AS priority
     FROM
       auth.users u
     CROSS JOIN
       public.tags t
     LEFT JOIN
       user_category_performance ucp ON u.id = ucp.user_id AND t.id = ucp.category_id
     WHERE
       ucp.user_id IS NULL
   )
   SELECT * FROM user_weak_categories
   UNION ALL
   SELECT * FROM user_missing_categories
   ORDER BY priority, avg_accuracy, last_attempt;
   ```

## Components and Interfaces

### Core Components

1. **Enhanced Quiz Selection**
   - `QuizRecommendations` - Personalized quiz recommendations
   - `QuizHistory` - History of completed quizzes with performance metrics
   - `QuizTemplates` - User-created quiz templates
   - `QuickStartOptions` - Frequently used quiz configurations

2. **Improved Quiz Experience**
   - `EnhancedQuestionCard` - Improved question display with better formatting
   - `DetailedExplanation` - Enhanced explanation with related concepts
   - `RelatedQuestions` - Suggestions for related questions
   - `BookmarkButton` - Option to bookmark explanations

3. **Performance Tracking**
   - `PerformanceCharts` - Visual representation of performance trends
   - `CategoryBreakdown` - Performance by category
   - `ImprovementMetrics` - Metrics showing improvement over time
   - `AchievementNotifications` - Notifications for reaching milestones

4. **Responsive Optimization**
   - `AdaptiveLayout` - Layout that adapts to different screen sizes
   - `ProgressiveLazyLoading` - Progressive loading of quiz content
   - `OfflineStateManager` - Management of offline state and synchronization
   - `NetworkAwareComponent` - Components that adapt to network conditions

5. **Customization Options**
   - `QuizPreferences` - Interface for setting quiz preferences
   - `QuizTemplateCreator` - Interface for creating quiz templates
   - `StudyGoalSetting` - Interface for setting study goals
   - `CustomizationPanel` - Panel for customizing quiz experience

### Data Hooks

1. **Quiz Enhancement Hooks**
   - `useQuizRecommendations` - Get personalized quiz recommendations
   - `useQuizHistory` - Fetch and manage quiz history
   - `useQuizTemplates` - Manage user-created quiz templates
   - `useBookmarkedExplanations` - Manage bookmarked explanations

2. **Performance Tracking Hooks**
   - `usePerformanceTrends` - Get performance trend data
   - `useImprovementMetrics` - Calculate improvement metrics
   - `useAchievements` - Track and manage achievements
   - `useCategoryPerformance` - Get performance by category

3. **Customization Hooks**
   - `useQuizPreferences` - Manage quiz preferences
   - `useStudyGoals` - Track and manage study goals
   - `useUIPreferences` - Manage UI preferences
   - `useCustomTemplates` - Create and manage custom templates

4. **Optimization Hooks**
   - `useNetworkStatus` - Track network status
   - `useOfflineSupport` - Manage offline functionality
   - `useProgressiveLoading` - Implement progressive loading
   - `useSynchronization` - Manage data synchronization

## Data Models

### User Preferences Model

```typescript
interface UserPreferences {
  id: string;
  userId: string;
  quizPreferences: {
    defaultQuizType: 'quick' | 'timed' | 'custom';
    defaultQuestionCount: number;
    defaultDifficulty: 'easy' | 'medium' | 'hard' | 'mixed';
    showExplanations: boolean;
    autoAdvance: boolean;
    soundEnabled: boolean;
    timerEnabled: boolean;
    defaultTimePerQuestion: number;
  };
  uiPreferences: {
    theme: 'dark' | 'light';
    fontSize: 'small' | 'medium' | 'large';
    animationsEnabled: boolean;
    highContrastMode: boolean;
  };
  studyGoals: Array<{
    id: string;
    name: string;
    targetCategory?: string;
    targetAccuracy: number;
    deadline?: string;
    progress: number;
  }>;
  createdAt: string;
  updatedAt: string;
}
```

### Quiz Template Model

```typescript
interface QuizTemplate {
  id: string;
  userId: string;
  name: string;
  description?: string;
  configuration: {
    quizType: 'quick' | 'timed' | 'custom';
    questionCount: number;
    difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
    categories?: string[];
    systems?: string[];
    topics?: string[];
    timePerQuestion?: number;
    showExplanations: boolean;
  };
  isFavorite: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### Bookmarked Explanation Model

```typescript
interface BookmarkedExplanation {
  id: string;
  userId: string;
  questionId: string;
  notes?: string;
  createdAt: string;
  question?: {
    id: string;
    text: string;
    explanation: string;
    tags: Array<{
      id: string;
      name: string;
      type: string;
    }>;
  };
}
```

### Performance Trend Model

```typescript
interface PerformanceTrend {
  userId: string;
  quizDate: string;
  categoryId?: string;
  avgAccuracy: number;
  quizCount: number;
  sevenDayTrend: number;
  dailyChange: number;
  categoryName?: string;
  categoryType?: string;
}
```

### Quiz Recommendation Model

```typescript
interface QuizRecommendation {
  userId: string;
  categoryId: string;
  categoryName: string;
  categoryType: string;
  avgAccuracy: number;
  attemptCount: number;
  lastAttempt?: string;
  priority: 'high' | 'medium' | 'low' | 'explore';
  recommendationReason: string;
}
```

## User Interface Design

### Enhanced Quiz Selection

The Enhanced Quiz Selection screen provides personalized recommendations and quick access to frequently used quiz configurations.

```mermaid
graph TD
    QuizSelection[Quiz Selection Screen] --> RecommendationsSection[Recommendations]
    QuizSelection --> QuickStartSection[Quick Start]
    QuizSelection --> TemplatesSection[My Templates]
    QuizSelection --> HistorySection[Recent Quizzes]
    
    RecommendationsSection --> WeakAreaRec[Weak Area Recommendations]
    RecommendationsSection --> ExploreRec[Explore New Topics]
    
    QuickStartSection --> QuickQuiz[Quick Quiz]
    QuickStartSection --> TimedTest[Timed Test]
    QuickStartSection --> CustomQuiz[Custom Quiz]
    
    TemplatesSection --> SavedTemplate1[Saved Template 1]
    TemplatesSection --> SavedTemplate2[Saved Template 2]
    TemplatesSection --> CreateTemplate[Create New Template]
    
    HistorySection --> RecentQuiz1[Recent Quiz 1]
    HistorySection --> RecentQuiz2[Recent Quiz 2]
    HistorySection --> ViewAllHistory[View All History]
```

### Improved Quiz Experience

The Improved Quiz Experience enhances the existing quiz interface with better explanations and related content.

```mermaid
graph TD
    QuizPage[Quiz Page] --> QuestionSection[Question Section]
    QuizPage --> OptionsSection[Options Section]
    QuizPage --> ExplanationSection[Explanation Section]
    QuizPage --> NavigationSection[Navigation Section]
    
    QuestionSection --> QuestionText[Question Text]
    QuestionSection --> QuestionMedia[Question Media]
    
    OptionsSection --> OptionA[Option A]
    OptionsSection --> OptionB[Option B]
    OptionsSection --> OptionC[Option C]
    OptionsSection --> OptionD[Option D]
    
    ExplanationSection --> CorrectAnswer[Correct Answer]
    ExplanationSection --> DetailedExplanation[Detailed Explanation]
    ExplanationSection --> RelatedConcepts[Related Concepts]
    ExplanationSection --> BookmarkButton[Bookmark Button]
    
    NavigationSection --> PreviousButton[Previous]
    NavigationSection --> NextButton[Next]
    NavigationSection --> ProgressIndicator[Progress Indicator]
```

### Performance Tracking

The Performance Tracking interface provides visual representations of user performance and improvement over time.

```mermaid
graph TD
    PerformancePage[Performance Page] --> OverviewSection[Overview]
    PerformancePage --> TrendsSection[Performance Trends]
    PerformancePage --> CategorySection[Category Breakdown]
    PerformancePage --> AchievementsSection[Achievements]
    
    OverviewSection --> AccuracyMetric[Overall Accuracy]
    OverviewSection --> CompletedQuizzes[Completed Quizzes]
    OverviewSection --> StreakIndicator[Current Streak]
    
    TrendsSection --> AccuracyTrend[Accuracy Trend]
    TrendsSection --> QuizCountTrend[Quiz Count Trend]
    TrendsSection --> ImprovementRate[Improvement Rate]
    
    CategorySection --> StrongCategories[Strong Categories]
    CategorySection --> WeakCategories[Weak Categories]
    CategorySection --> CategoryComparison[Category Comparison]
    
    AchievementsSection --> EarnedAchievements[Earned Achievements]
    AchievementsSection --> UpcomingAchievements[Upcoming Achievements]
    AchievementsSection --> AchievementProgress[Achievement Progress]
```

### Customization Options

The Customization Options interface allows users to personalize their quiz experience.

```mermaid
graph TD
    CustomizationPage[Customization Page] --> PreferencesSection[Quiz Preferences]
    CustomizationPage --> TemplatesSection[Quiz Templates]
    CustomizationPage --> GoalsSection[Study Goals]
    CustomizationPage --> UISection[UI Preferences]
    
    PreferencesSection --> QuizTypePreference[Default Quiz Type]
    PreferencesSection --> QuestionCountPreference[Default Question Count]
    PreferencesSection --> DifficultyPreference[Default Difficulty]
    PreferencesSection --> TimerPreference[Timer Settings]
    
    TemplatesSection --> TemplateList[Template List]
    TemplatesSection --> TemplateEditor[Template Editor]
    TemplatesSection --> TemplateSharing[Template Sharing]
    
    GoalsSection --> GoalList[Goal List]
    GoalsSection --> GoalCreator[Goal Creator]
    GoalsSection --> GoalProgress[Goal Progress]
    
    UISection --> ThemeSettings[Theme Settings]
    UISection --> FontSettings[Font Settings]
    UISection --> AnimationSettings[Animation Settings]
    UISection --> AccessibilitySettings[Accessibility Settings]
```

## Error Handling

### Error Types

1. **Quiz Loading Errors**
   - Failed to fetch questions
   - Insufficient questions for criteria
   - Question format errors

2. **User Preference Errors**
   - Failed to save preferences
   - Failed to load preferences
   - Preference synchronization errors

3. **Performance Tracking Errors**
   - Failed to fetch performance data
   - Calculation errors
   - Data inconsistency errors

4. **Network and Synchronization Errors**
   - Offline mode errors
   - Synchronization conflicts
   - Data loss during synchronization

### Error Handling Strategy

1. **Graceful Degradation**
   - Fall back to default settings when preferences can't be loaded
   - Use cached questions when new ones can't be fetched
   - Provide offline functionality when network is unavailable

2. **User Feedback**
   - Clear error messages with suggested actions
   - Visual indicators for sync status
   - Progress saving confirmations

3. **Error Recovery**
   - Automatic retry for transient errors
   - Manual retry options for persistent errors
   - Data recovery mechanisms for interrupted sessions

4. **Logging and Monitoring**
   - Detailed client-side error logging
   - Performance monitoring for quiz loading
   - Usage analytics for feature improvement

## Testing Strategy

### Unit Testing

1. **Component Tests**
   - Test individual UI components
   - Verify component rendering with different data
   - Test component interactions and state changes

2. **Hook Tests**
   - Test custom hooks for data fetching
   - Verify state management
   - Test calculation functions

3. **Utility Tests**
   - Test helper functions
   - Verify data transformations
   - Test preference management functions

### Integration Testing

1. **Feature Tests**
   - Test complete quiz flow with recommendations
   - Verify performance tracking across sessions
   - Test preference application to quiz experience

2. **API Integration Tests**
   - Test quiz data fetching with preferences
   - Verify performance data saving
   - Test recommendation engine

3. **Cross-Feature Tests**
   - Test integration between quiz and performance tracking
   - Verify preference application across features
   - Test offline mode with synchronization

### End-to-End Testing

1. **User Flow Tests**
   - Test complete quiz session flow with preferences
   - Verify performance tracking and visualization
   - Test recommendation-based quiz selection

2. **Performance Tests**
   - Test loading times with preferences applied
   - Verify responsiveness during quiz sessions
   - Test under different network conditions

3. **Offline Capability Tests**
   - Test offline quiz capabilities
   - Verify data synchronization after reconnection
   - Test preference persistence in offline mode

## Performance Optimization

### Quiz Loading Optimization

1. **Progressive Loading**
   - Load visible content first
   - Lazy load images and media in questions
   - Prefetch next questions based on navigation patterns

2. **Content Caching**
   - Cache frequently accessed questions
   - Implement service worker for offline access
   - Use IndexedDB for local storage of quiz data

3. **Query Optimization**
   - Optimize database queries for quiz questions
   - Implement pagination for large sets of questions
   - Use efficient filtering based on preferences

### Rendering Optimization

1. **Component Optimization**
   - Memoize expensive components
   - Optimize re-renders with React.memo and useMemo
   - Use virtualization for long lists

2. **Animation Performance**
   - Use CSS transitions where possible
   - Optimize JavaScript animations
   - Disable animations on low-end devices

3. **Responsive Design**
   - Adapt quiz layout for different screen sizes
   - Optimize touch interactions for mobile
   - Reduce content complexity on small screens

### Data Management Optimization

1. **Preference Management**
   - Store preferences locally for quick access
   - Batch preference updates to reduce API calls
   - Implement debouncing for frequent preference changes

2. **Background Synchronization**
   - Update quiz results in background
   - Queue updates for offline mode
   - Batch synchronization for multiple changes

3. **Prefetching Strategy**
   - Prefetch recommended quiz questions
   - Preload frequently used quiz templates
   - Cache related content based on user patterns

## Implementation Approach

The quiz enhancements will be implemented using an incremental approach, focusing on core functionality first and then expanding to more advanced features. The implementation will follow these phases:

1. **Phase 1: Core Enhancements**
   - Implement personalized recommendations
   - Enhance quiz explanations
   - Add basic performance tracking
   - Implement preference saving

2. **Phase 2: Advanced Features**
   - Create quiz template system
   - Implement detailed performance analytics
   - Add bookmarking functionality
   - Enhance offline support

3. **Phase 3: Optimization**
   - Optimize performance
   - Enhance responsive design
   - Implement advanced caching
   - Add progressive loading

4. **Phase 4: Polish**
   - Refine user interface
   - Add animations and transitions
   - Enhance accessibility
   - Implement final optimizations

This phased approach allows for early delivery of value while managing complexity and ensuring quality.