# Using Supabase Publishable Keys in USMLE Trivia App

This guide explains how to use Supabase publishable keys in the USMLE Trivia App for secure client-side authentication.

## What are Supabase Publishable Keys?

Supabase publishable keys (previously called "anon keys") are designed to be used in client-side code. They are safe to expose in your frontend code and have restricted permissions based on your Row Level Security (RLS) policies.

## Key Types in Supabase

1. **Publishable Key (Client-Side)**: 
   - Format: `sb_publishable_xxxxxxxx`
   - Used in browser/client code
   - Restricted by Row Level Security (RLS)
   - Safe to expose in frontend code

2. **Service Role Key (Server-Side Only)**:
   - Format: JWT token
   - Has admin privileges
   - Bypasses RLS
   - Must NEVER be exposed in client-side code

3. **Access Token (API/MCP)**:
   - Format: `sbp_xxxxxxxx`
   - Used for API access and MCP servers
   - Has admin privileges
   - Must NEVER be exposed in client-side code

## How We Use Keys in the App

### 1. Client-Side Authentication (Browser)

In `src/lib/supabase.js`, we use the publishable key for client-side operations:

```javascript
// Prioritize publishable key over anon key
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabasePublishableKey = import.meta.env.VITE_SUPABASE_PUBLISHABLE_KEY
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// Use publishable key if available, fallback to anon key
const supabaseKey = supabasePublishableKey || supabaseAnonKey

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})
```

### 2. MCP Server (Server-Side)

For the Supabase MCP server, we use the access token:

```javascript
// MCP configuration (in .kiro/settings/mcp.json)
{
  "mcpServers": {
    "supabase": {
      "command": "npx",
      "args": ["-y", "@supabase/mcp-server-supabase@latest", "--project-ref=bkuowoowlmwranfoliea", "--read-only"],
      "env": {
        "SUPABASE_ACCESS_TOKEN": "********************************************"
      },
      "disabled": false,
      "autoApprove": []
    }
  }
}
```

## Environment Variables

Set up your environment variables in `.env` or `.env.local`:

```
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_PUBLISHABLE_KEY=sb_publishable_your-key

# Legacy key (for backward compatibility)
VITE_SUPABASE_ANON_KEY=your-anon-key
```

## Security Best Practices

1. **NEVER** expose service role keys or access tokens in client-side code
2. Use Row Level Security (RLS) policies to restrict data access
3. Validate all user inputs server-side
4. Use the publishable key for all client-side operations
5. Keep access tokens secure for server-side operations only

## Testing Your Configuration

Run the test script to verify your publishable key is working correctly:

```
node test-app-publishable-key.js
```

This will test basic database queries and authentication capabilities using the publishable key.