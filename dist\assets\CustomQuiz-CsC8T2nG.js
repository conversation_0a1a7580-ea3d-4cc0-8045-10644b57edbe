import{f as e,u as t,r as s,j as i,m as a,A as n,aP as r,H as o}from"./vendor-DfmD63KD.js";import{f as l,c as d,r as c,d as u,Q as x,a as m,b as g}from"./QuizError-Mp16REcC.js";import{u as h}from"./index-gGBCxdYu.js";const b=["🅰️","🅱️","🇨","🇩"],f=({show:e})=>e?i.jsx("div",{className:"absolute inset-0 pointer-events-none z-40 flex items-center justify-center",children:i.jsx("span",{role:"img","aria-label":"confetti",className:"text-5xl animate-bounce",children:"🎉"})}):null,y=({timeLeft:e,total:t})=>i.jsxs("div",{className:"sticky top-0 z-20 mb-4",children:[i.jsx(a.div,{className:"h-3 w-full bg-gray-200 rounded overflow-hidden",children:i.jsx(a.div,{className:"h-3 rounded "+(e<10?"bg-red-500":e<t/3?"bg-yellow-400":"bg-blue-500"),initial:{width:"100%"},animate:{width:e/t*100+"%"},transition:{duration:.5}})}),i.jsx("div",{className:"flex justify-between text-xs mt-1",children:i.jsxs("span",{children:["Time Left: ",i.jsxs("b",{children:[Math.floor(e/60),":",(e%60).toString().padStart(2,"0")]})]})})]}),p=()=>{const p=e(),j=t(),{user:v}=h(),N=p.state,[w,I]=s.useState([]),[S,C]=s.useState(!0),[k,_]=s.useState(null),[q,E]=s.useState(0),[O,Q]=s.useState(null),[z,R]=s.useState(!1),[T,F]=s.useState(!1),[G,H]=s.useState([]),[$,L]=s.useState(null),[M,P]=s.useState(!1),[U,A]=s.useState("timed"===N?.timing?60:null),[B,D]=s.useState(()=>"true"===localStorage.getItem("quizMuted")),[J,K]=s.useState(!1),[V,W]=s.useState(0),X=s.useRef();s.useEffect(()=>{if(!N)return _({code:"NO_CONFIG",message:"No quiz configuration found."}),void C(!1);C(!0);const e=N.topicId||N.systemId||N.subjectId||"mixed";l({userId:v.id,categoryId:e,questionCount:N.questionCount,difficulty:N.difficulty}).then(async e=>{if(!e||e.length<N.questionCount)return _({code:"INSUFFICIENT_QUESTIONS_ERROR",message:`Not enough questions available for your selection. Only ${e?.length||0} found.`}),I(e||[]),void C(!1);I(e),C(!1);const t=await d({userId:v.id,sessionType:"custom",totalQuestions:e.length,settings:N});L(t.id)}).catch(e=>{_({code:"FETCH_ERROR",message:e?.message||"Failed to load questions."}),C(!1)})},[N,v,j]),s.useEffect(()=>{if("timed"===N?.timing&&!M&&!S)return A(60),X.current&&clearInterval(X.current),X.current=setInterval(()=>{A(e=>e<=1?(clearInterval(X.current),F(!0),R(!0),0):e-1)},1e3),()=>clearInterval(X.current)},[q,M,S,N]),s.useEffect(()=>{z&&!T&&O===w[q]?.correct_option_id&&(K(!0),setTimeout(()=>K(!1),1200))},[z,T,O,w,q]),s.useEffect(()=>{if(M){let e=0;const t=G.filter(e=>e.isCorrect).length,s=setInterval(()=>{e++,W(Math.min(e,t)),e>=t&&clearInterval(s)},20);return()=>clearInterval(s)}},[M,G]);const Y=s.useCallback(e=>{z||(Q(e),R(!0),F(!1),c($,{userId:v.id,questionId:w[q].id,isCorrect:e===w[q].correct_option_id,timeSpentSeconds:"timed"===N?.timing?60-U:null}))},[z,w,q,$,v,N,U]),Z=s.useCallback(()=>{H(e=>[...e,{questionId:w[q].id,selectedOption:O,isCorrect:O===w[q].correct_option_id,timedOut:T,timeSpent:"timed"===N?.timing?60-U:null}]),Q(null),R(!1),F(!1),q<w.length-1?E(e=>e+1):(P(!0),u($))},[q,w,O,T,N,U,$]);if(G.filter(e=>e.isCorrect).length,S)return i.jsx(x,{});if(k)return i.jsx(m,{error:k,onRetry:()=>window.location.reload()});if(!w.length)return i.jsx(m,{error:{code:"NO_QUESTIONS",message:"No questions found for this quiz."},onRetry:()=>window.location.reload()});if(M)return i.jsxs(a.div,{className:"max-w-xl mx-auto mt-8 bg-white/80 dark:bg-slate-900/80 rounded-3xl shadow-xl p-8 flex flex-col items-center",initial:{opacity:0,y:24},animate:{opacity:1,y:0},children:[i.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Quiz Complete!"}),i.jsxs("div",{className:"mb-4 text-3xl font-extrabold text-green-600",children:[i.jsx(n,{children:i.jsx(a.span,{initial:{scale:.7},animate:{scale:1},transition:{type:"spring",stiffness:300},children:V},V)}),i.jsxs("span",{className:"text-gray-700 font-normal text-xl",children:[" / ",w.length]})]}),i.jsx("button",{className:"mt-4 px-6 py-2 rounded-xl bg-cyan-600 text-white font-semibold hover:bg-cyan-700 transition",onClick:()=>window.location.reload(),children:"Restart"}),i.jsx("button",{className:"mt-4 px-6 py-2 rounded-xl bg-gray-200 text-gray-800 font-semibold hover:bg-gray-300 transition",onClick:()=>j("/quiz-tab"),"aria-label":"Go to Home",children:"Home"})]});const ee=w[q]||{};return i.jsx("div",{className:"min-h-screen flex flex-col items-center justify-start bg-gradient-to-br from-cyan-100 via-blue-100 to-slate-200 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 py-8",children:i.jsxs("div",{className:"w-full max-w-xl mx-auto relative",children:["timed"===N?.timing&&i.jsx(y,{timeLeft:U,total:60}),i.jsx(g,{current:q+1,total:w.length,secondsLeft:"timed"===N?.timing?U:null}),i.jsxs("div",{className:"flex justify-between items-center mb-4",children:[i.jsx("button",{onClick:()=>j(-1),className:"p-2 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700 transition-colors","aria-label":"Go to Previous Question",children:i.jsx(r,{className:"w-6 h-6 text-gray-600 dark:text-gray-300"})}),i.jsx("button",{onClick:()=>j("/quiz-tab"),className:"p-2 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700 transition-colors","aria-label":"Go to Home",children:i.jsx(o,{className:"w-6 h-6 text-gray-600 dark:text-gray-300"})})]}),i.jsx(n,{mode:"wait",children:ee?i.jsxs(a.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},exit:{opacity:0,x:-50},transition:{duration:.3},className:"relative",children:[i.jsx(f,{show:J}),i.jsxs("div",{className:"mb-2 font-semibold",children:["Question ",q+1," / ",w.length]}),i.jsx("div",{className:"mb-4 text-lg font-medium",children:ee.question_text}),i.jsx("div",{className:"space-y-2 mb-4",children:ee.options&&ee.options.map((e,t)=>i.jsxs(a.button,{className:`w-full flex items-center gap-3 text-left p-3 rounded-xl border transition-all font-semibold text-base focus:outline-none focus:ring-2 focus:ring-cyan-400 ${O===e.id?e.id===ee.correct_option_id?"bg-green-100 border-green-400 scale-105":"bg-red-100 border-red-400 shake":"border-gray-200 hover:bg-cyan-50"} ${z?"pointer-events-none":""}`,disabled:z,onClick:()=>Y(e.id),"aria-pressed":O===e.id,tabIndex:0,"aria-label":`Option ${t+1}: ${e.text}`,children:[i.jsx("span",{className:"text-2xl",children:b[t]||"🔘"}),i.jsx("span",{children:e.text})]},e.id))}),i.jsx(n,{children:z&&i.jsxs(a.div,{initial:{opacity:0,y:12},animate:{opacity:1,y:0},exit:{opacity:0,y:-12},className:"mb-2",children:[O===ee.correct_option_id?i.jsx("div",{className:"text-green-700 font-semibold",children:"Correct!"}):i.jsxs("div",{className:"text-red-700 font-semibold",children:["Incorrect. Correct answer: ",i.jsx("b",{children:ee.options.find(e=>e.id===ee.correct_option_id)?.text})]}),ee.explanation&&i.jsx("div",{className:"mt-2 text-sm text-gray-600 bg-gray-50 rounded p-2",children:ee.explanation}),i.jsx("button",{className:"mt-4 w-full bg-cyan-600 text-white py-2 rounded-xl font-semibold text-lg shadow-lg hover:bg-cyan-700 transition",onClick:Z,"data-next-btn":!0,"aria-label":"Next Question",children:q<w.length-1?"Next Question":"Finish Quiz"})]})})]},ee.id):!S&&i.jsxs("div",{className:"text-center text-gray-600 dark:text-gray-400 py-10",children:[i.jsx("p",{children:"No question available to display. Please try again or select a different category."}),i.jsx("button",{onClick:()=>j(-1),className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Go Back"})]})})]})})};export{p as default};
