import{u as e,r as a,j as t,at as r}from"./vendor-DfmD63KD.js";import{s as l}from"./index-gGBCxdYu.js";const s=[{value:"",label:"Mixed"},{value:"easy",label:"Easy"},{value:"medium",label:"Medium"},{value:"hard",label:"Hard"}],d=[{value:"timed",label:"Timed (1 min/question)"},{value:"self",label:"Self-paced"}],i=()=>{const i=e(),[n,o]=a.useState([]),[c,u]=a.useState([]),[g,m]=a.useState([]),[x,b]=a.useState(""),[h,y]=a.useState(""),[p,f]=a.useState(""),[j,v]=a.useState(""),[k,w]=a.useState(10),[N,S]=a.useState("timed"),[q,C]=a.useState(!0),[_,z]=a.useState(null);a.useEffect(()=>{(async()=>{C(!0),z(null);try{const{data:e,error:a}=await l.from("tags").select("*").eq("type","subject").eq("is_active",!0).order("order_index",{ascending:!0});if(a)throw a;o(e||[]);const{data:t,error:r}=await l.from("tags").select("*").eq("type","system").eq("is_active",!0).order("order_index",{ascending:!0});if(r)throw r;u(t||[]);const{data:s,error:d}=await l.from("tags").select("*").eq("type","topic").eq("is_active",!0).order("order_index",{ascending:!0});if(d)throw d;m(s||[])}catch(e){z(e.message||"Failed to load quiz options.")}finally{C(!1)}})()},[]);const F=g.filter(e=>!(x&&e.parent_id!==x||h&&e.parent_id!==h)),Q=x&&h&&k>0&&k<=40;return t.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100 dark:from-gray-900 dark:via-green-900/20 dark:to-emerald-900/20 flex items-center justify-center p-4",children:t.jsxs("form",{onSubmit:e=>{e.preventDefault(),Q&&i("/custom-quiz",{state:{subjectId:x,systemId:h,topicId:p||null,difficulty:j,questionCount:k,timing:N}})},className:"w-full max-w-lg bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 space-y-6","aria-label":"Custom Quiz Setup",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Custom Quiz Setup"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:"Select your desired filters and options to create a personalized quiz session."}),_&&t.jsx("div",{className:"text-red-600 bg-red-100 dark:bg-red-900/20 p-2 rounded",children:_}),q&&t.jsx("div",{className:"text-gray-500",children:"Loading options..."}),t.jsxs("div",{children:[t.jsxs("label",{htmlFor:"subject",className:"block font-medium text-gray-800 dark:text-gray-200 mb-1",children:["Subject ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsxs("select",{id:"subject",value:x,onChange:e=>b(e.target.value),className:"w-full rounded-lg border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2",required:!0,children:[t.jsx("option",{value:"",children:"Select a subject"}),n.map(e=>t.jsx("option",{value:e.id,children:e.name},e.id))]})]}),t.jsxs("div",{children:[t.jsxs("label",{htmlFor:"system",className:"block font-medium text-gray-800 dark:text-gray-200 mb-1",children:["System ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsxs("select",{id:"system",value:h,onChange:e=>y(e.target.value),className:"w-full rounded-lg border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2",required:!0,children:[t.jsx("option",{value:"",children:"Select a system"}),c.map(e=>t.jsx("option",{value:e.id,children:e.name},e.id))]})]}),t.jsxs("div",{children:[t.jsx("label",{htmlFor:"topic",className:"block font-medium text-gray-800 dark:text-gray-200 mb-1",children:"Topic (optional)"}),t.jsxs("select",{id:"topic",value:p,onChange:e=>f(e.target.value),className:"w-full rounded-lg border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2",children:[t.jsx("option",{value:"",children:"All topics"}),F.map(e=>t.jsx("option",{value:e.id,children:e.name},e.id))]})]}),t.jsxs("div",{children:[t.jsx("label",{htmlFor:"difficulty",className:"block font-medium text-gray-800 dark:text-gray-200 mb-1",children:"Difficulty"}),t.jsx("select",{id:"difficulty",value:j,onChange:e=>v(e.target.value),className:"w-full rounded-lg border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2",children:s.map(e=>t.jsx("option",{value:e.value,children:e.label},e.value))})]}),t.jsxs("div",{children:[t.jsx("label",{htmlFor:"questionCount",className:"block font-medium text-gray-800 dark:text-gray-200 mb-1",children:"Number of Questions"}),t.jsx("input",{id:"questionCount",type:"number",min:1,max:40,value:k,onChange:e=>w(Number(e.target.value)),className:"w-full rounded-lg border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2"}),t.jsx("span",{className:"text-xs text-gray-500",children:"1–40 questions"})]}),t.jsxs("div",{children:[t.jsx("label",{htmlFor:"timing",className:"block font-medium text-gray-800 dark:text-gray-200 mb-1",children:"Timing"}),t.jsx("select",{id:"timing",value:N,onChange:e=>S(e.target.value),className:"w-full rounded-lg border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2",children:d.map(e=>t.jsx("option",{value:e.value,children:e.label},e.value))})]}),t.jsxs("button",{type:"submit",disabled:!Q,className:"w-full flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-semibold text-white transition-all "+(Q?"bg-green-600 hover:bg-green-700 shadow-lg":"bg-gray-400 cursor-not-allowed"),children:["Start Quiz ",t.jsx(r,{className:"w-5 h-5"})]})]})})};export{i as default};
