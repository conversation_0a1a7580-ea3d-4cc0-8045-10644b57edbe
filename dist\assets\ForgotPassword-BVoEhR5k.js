import{r as e,j as a,m as r,l as t,aS as s,i,T as d,aY as l,aP as o}from"./vendor-DfmD63KD.js";import{u as n}from"./index-gGBCxdYu.js";const c=()=>{const[c,x]=e.useState(""),[m,y]=e.useState(!1),[p,h]=e.useState(""),[b,u]=e.useState(!1),{resetPassword:k,isConfigured:g}=n();return b?a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 dark:from-expo-950 dark:to-expo-900 flex items-center justify-center px-4",children:a.jsx(r.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"w-full max-w-md text-center",children:a.jsxs("div",{className:"bg-white dark:bg-expo-850 rounded-2xl p-8 shadow-card dark:shadow-card-dark border border-gray-100 dark:border-expo-700",children:[a.jsx(r.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"inline-flex items-center justify-center w-16 h-16 bg-green-500 rounded-full mb-6",children:a.jsx(t,{size:32,className:"text-white"})}),a.jsx("h2",{className:"text-2xl font-bold text-gray-800 dark:text-dark-50 mb-2",children:"Check Your Email"}),a.jsxs("p",{className:"text-gray-600 dark:text-dark-300 mb-6",children:["We've sent a password reset link to"," ",a.jsx("span",{className:"font-semibold text-primary-600 dark:text-primary-400",children:c})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsx(s,{to:"/auth/login",className:"block bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-6 rounded-xl transition-colors",children:"Back to Sign In"}),a.jsx("button",{onClick:()=>{u(!1),x("")},className:"block w-full text-gray-600 dark:text-dark-300 hover:text-gray-800 dark:hover:text-dark-100 font-medium transition-colors",children:"Try different email"})]})]})})}):a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 dark:from-expo-950 dark:to-expo-900 flex items-center justify-center px-4",children:a.jsxs(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"w-full max-w-md",children:[a.jsxs("div",{className:"text-center mb-8",children:[a.jsx(r.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2},className:"inline-flex items-center justify-center w-16 h-16 bg-primary-600 rounded-2xl mb-4 shadow-lg",children:a.jsx(i,{size:32,className:"text-white"})}),a.jsx("h1",{className:"text-3xl font-bold text-gray-800 dark:text-dark-50 mb-2",children:"Forgot Password?"}),a.jsx("p",{className:"text-gray-600 dark:text-dark-300",children:"No worries! Enter your email and we'll send you a reset link"})]}),a.jsxs(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"bg-white dark:bg-expo-850 rounded-2xl p-8 shadow-card dark:shadow-card-dark border border-gray-100 dark:border-expo-700",children:[a.jsxs("form",{onSubmit:async e=>{if(e.preventDefault(),y(!0),h(""),!c.trim())return h("Please enter your email address"),void y(!1);const{error:a}=await k(c);a?(h(a),y(!1)):(u(!0),y(!1))},className:"space-y-6",children:[!g&&a.jsxs(r.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 p-4 rounded-xl text-sm font-medium border border-yellow-200 dark:border-yellow-800 flex items-center gap-3",children:[a.jsx(d,{size:20}),a.jsxs("div",{children:[a.jsx("h3",{className:"font-bold",children:"Supabase Not Configured"}),a.jsx("p",{className:"text-xs",children:"Please set up your .env.local file to use this feature."})]})]}),p&&a.jsx(r.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-xl text-sm font-medium border border-red-200 dark:border-red-800",children:p}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-semibold text-gray-700 dark:text-dark-200 mb-2",children:"Email Address"}),a.jsxs("div",{className:"relative",children:[a.jsx(l,{size:20,className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-dark-400"}),a.jsx("input",{type:"email",value:c,onChange:e=>x(e.target.value),className:"w-full pl-12 pr-4 py-3 bg-gray-50 dark:bg-expo-800 border border-gray-200 dark:border-expo-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-gray-800 dark:text-dark-50 placeholder-gray-400 dark:placeholder-dark-400 transition-all disabled:opacity-50",placeholder:"Enter your email",disabled:m||!g})]})]}),a.jsx(r.button,{whileHover:{scale:!g||m?1:1.02},whileTap:{scale:!g||m?1:.98},type:"submit",disabled:m||!g,className:"w-full bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 disabled:opacity-70 text-white font-bold py-3 px-6 rounded-xl transition-all shadow-lg disabled:cursor-not-allowed",children:m?"Sending...":g?"Send Reset Link":"Configuration Missing"})]}),a.jsxs("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-expo-600 text-center",children:[a.jsxs("p",{className:"text-sm text-gray-500 dark:text-dark-400 mb-4",children:["Remember your password?"," ",a.jsx(s,{to:"/auth/login",className:"text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-semibold transition-colors",children:"Sign in here"})]}),a.jsx("p",{className:"text-xs text-gray-400 dark:text-dark-500",children:"Didn't receive an email? Check your spam folder or contact support"})]})]}),a.jsx(r.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"mt-6 text-center",children:a.jsxs(s,{to:"/auth/welcome",className:"inline-flex items-center gap-2 text-gray-600 dark:text-dark-300 hover:text-gray-800 dark:hover:text-dark-100 transition-colors",children:[a.jsx(o,{size:16}),a.jsx("span",{className:"text-sm font-medium",children:"Back to welcome"})]})})]})})};export{c as default};
