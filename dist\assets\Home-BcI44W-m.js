const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/WelcomeSection-p6FsBA8n.js","assets/vendor-DfmD63KD.js","assets/HomeStats-aP-UnKXB.js","assets/HomeActions-Cj7ziiTn.js","assets/StudyTipsSection-DWAzxcAg.js","assets/ProgressOverview-BfWjbLcE.js"])))=>i.map(i=>d[i]);
import{v as e,j as r,r as s,C as t,w as a,_ as i}from"./vendor-DfmD63KD.js";import{w as o,s as n,l,u as c,L as d}from"./index-gGBCxdYu.js";import{q as u,g as m,u as y}from"./useViewTransitions-2OP18fwt.js";const x=s.lazy(()=>i(()=>import("./WelcomeSection-p6FsBA8n.js"),__vite__mapDeps([0,1]))),_=s.lazy(()=>i(()=>import("./HomeStats-aP-UnKXB.js"),__vite__mapDeps([2,1]))),g=s.lazy(()=>i(()=>import("./HomeActions-Cj7ziiTn.js"),__vite__mapDeps([3,1]))),p=s.lazy(()=>i(()=>import("./StudyTipsSection-DWAzxcAg.js"),__vite__mapDeps([4,1]))),b=s.lazy(()=>i(()=>import("./ProgressOverview-BfWjbLcE.js"),__vite__mapDeps([5,1]))),h=({isRefreshing:e,isFromCache:s})=>s||e?r.jsxs("div",{className:"fixed top-4 right-4 z-50 flex space-x-2",children:[s&&r.jsx("div",{className:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-xs font-medium border border-yellow-200 dark:border-yellow-800",children:"📱 Offline Mode"}),e&&r.jsxs("div",{className:"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-xs font-medium border border-blue-200 dark:border-blue-800 flex items-center space-x-1",children:[r.jsx(a,{className:"w-3 h-3 animate-spin"}),r.jsx("span",{children:"Syncing..."})]})]}):null,w=({error:e,onRetry:s})=>r.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4",children:r.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-red-200 dark:border-red-800 max-w-md w-full text-center",children:[r.jsx("div",{className:"p-3 bg-red-100 dark:bg-red-900/30 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center",children:r.jsx(t,{className:"w-8 h-8 text-red-600"})}),r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Something went wrong"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:e?.message||"Unable to load your dashboard. Please try again."}),r.jsx("button",{onClick:s,className:"bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200",children:"Try Again"})]})}),j=()=>{const{user:t,profile:a}=c(),{transitionTo:i}=y(),{data:j,isLoading:f,isError:k,error:v,refetch:N}=(S=t?.id,e({queryKey:u.userActivity(S),queryFn:async()=>S?o(async()=>{try{const{data:e,error:r}=await n.from("user_question_history").select("*").eq("user_id",S).limit(1);if(r)throw r;const s=e&&e.length>0,t=!s;let a={totalQuestions:0,accuracy:0,studyTime:0,currentStreak:0},i=[];if(s){const{data:e,error:r}=await n.rpc("get_user_stats",{p_user_id:S});if(!r&&e&&e.length>0){const r=e[0];a={totalQuestions:r.total_questions_attempted||0,accuracy:r.accuracy_percentage||0,studyTime:Math.round(2*r.total_questions_attempted/60*10)/10||0,currentStreak:0}}const{data:s,error:t}=await n.from("quiz_sessions").select("id, session_type, total_questions, correct_answers, started_at, completed_at").eq("user_id",S).order("started_at",{ascending:!1}).limit(5);!t&&s&&(i=s.map(e=>({id:e.id,type:e.session_type||"Quiz",score:e.correct_answers||0,total:e.total_questions||0,timeAgo:m(e.started_at),categories:["General"]})))}return{isNewUser:t,userStats:a,recentActivity:i}}catch(v){throw l.error("Error in useUserActivityQuery",{error:v.message,userId:S}),v}},{queryType:`user-activity-${S}`,fallback:{isNewUser:!0,userStats:{totalQuestions:0,accuracy:0,studyTime:0,currentStreak:0},recentActivity:[]}}):{isNewUser:!0,userStats:{totalQuestions:0,accuracy:0,studyTime:0,currentStreak:0},recentActivity:[]},staleTime:3e5,gcTime:9e5,enabled:!!S,retry:2,retryDelay:e=>Math.min(1e3*2**e,3e4)}));var S;const{isNewUser:T=!0,userStats:A={totalQuestions:0,accuracy:0,studyTime:0,currentStreak:0},recentActivity:q=[]}=j||{};return k?r.jsx(w,{error:v,onRetry:N}):r.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 pb-20 md:pb-0",children:[r.jsx(h,{isRefreshing:!1,isFromCache:!1}),r.jsx("div",{className:"px-3 md:px-6 lg:px-8 pt-2 pb-3 max-w-4xl mx-auto",children:r.jsxs(s.Suspense,{fallback:r.jsx(d,{}),children:[r.jsx(x,{user:t,profile:a,isNewUser:T}),r.jsx(_,{userStats:A,isNewUser:T,isLoading:f}),r.jsx(g,{isNewUser:T,onNavigate:i}),r.jsx(p,{}),r.jsx(b,{userStats:A,isNewUser:T})]})})]})};export{j as default};
