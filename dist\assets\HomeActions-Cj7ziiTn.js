import{u as e,j as t,m as i,aG as o,ax as s,B as r,h as l,ay as c,b1 as a}from"./vendor-DfmD63KD.js";const n=({isNewUser:n,onNavigate:d})=>{const m=e(),u=n?[{icon:o,title:"🟣 Quick Quiz",subtitle:"10 questions (default)",color:"bg-purple-600",iconColor:"text-purple-600",action:()=>m("/quick-quiz"),timeEstimate:"~10 min",difficulty:"Beginner",description:"Quick practice, category-specific review",progress:0,recommended:!0},{icon:s,title:"🔵 Timed Test",subtitle:"20 questions (fixed)",color:"bg-blue-600",iconColor:"text-blue-600",action:()=>m("/timed-test-setup"),timeEstimate:"30 min",difficulty:"Mixed",description:"Exam simulation, time management practice",progress:0,recommended:!1},{icon:r,title:"Learn",subtitle:"Study materials & concepts",color:"bg-green-600",iconColor:"text-green-600",action:()=>m("/learn"),timeEstimate:"Self-paced",difficulty:"All levels",description:"Review core concepts and explanations",progress:0,recommended:!0},{icon:l,title:"My Stats",subtitle:"View your progress",color:"bg-orange-600",iconColor:"text-orange-600",action:()=>m("/profile"),timeEstimate:"~2 min",difficulty:null,description:"Track your performance and achievements",progress:0,recommended:!1},{icon:c,title:"🟢 Custom Quiz",subtitle:"1-40 questions (user choice)",color:"bg-green-600",iconColor:"text-green-600",action:()=>m("/custom-quiz"),timeEstimate:"Variable",difficulty:"Custom",description:"Targeted study, flexible practice",progress:0,recommended:!1},{icon:a,title:"🟡 Block Test",subtitle:"20-50 per block × 2-8 blocks",color:"bg-yellow-600",iconColor:"text-yellow-600",action:()=>m("/block-test"),timeEstimate:"2-6 hours",difficulty:"Full Exam",description:"Full exam simulation",progress:0,recommended:!1}]:[{icon:o,title:"🟣 Quick Quiz",subtitle:"10 questions (default)",color:"bg-purple-600",iconColor:"text-purple-600",action:()=>m("/quick-quiz"),timeEstimate:"~10 min",difficulty:"Mixed",description:"Quick practice, category-specific review",progress:75,recommended:!0},{icon:s,title:"🔵 Timed Test",subtitle:"20 questions (fixed)",color:"bg-blue-600",iconColor:"text-blue-600",action:()=>m("/timed-test-setup"),timeEstimate:"30 min",difficulty:"Progressive",description:"Exam simulation, time management practice",progress:60,recommended:!0},{icon:r,title:"Learn",subtitle:"Study materials & concepts",color:"bg-green-600",iconColor:"text-green-600",action:()=>m("/learn"),timeEstimate:"Self-paced",difficulty:"Adaptive",description:"Deep dive into topics you find challenging",progress:40,recommended:!1},{icon:c,title:"🟢 Custom Quiz",subtitle:"1-40 questions (user choice)",color:"bg-green-600",iconColor:"text-green-600",action:()=>m("/custom-quiz"),timeEstimate:"Variable",difficulty:"Custom",description:"Targeted study, flexible practice",progress:0,recommended:!1},{icon:a,title:"🟡 Block Test",subtitle:"20-50 per block × 2-8 blocks",color:"bg-yellow-600",iconColor:"text-yellow-600",action:()=>m("/block-test"),timeEstimate:"2-6 hours",difficulty:"Full Exam",description:"Full exam simulation",progress:0,recommended:!1},{icon:l,title:"My Stats",subtitle:"View detailed analytics",color:"bg-orange-600",iconColor:"text-orange-600",action:()=>m("/profile"),timeEstimate:"~3 min",difficulty:null,description:"Review your performance trends and insights",progress:100,recommended:!1}];return t.jsxs("div",{className:"mb-8",children:[t.jsxs("div",{className:"flex items-center justify-between mb-4",children:[t.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"Quick Actions"}),t.jsx(i.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>m("/quiz"),className:"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium",children:"View All →"})]}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:u.map((e,o)=>t.jsxs(i.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*o},whileHover:{y:-5,scale:1.02},className:"relative bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-blue-200 dark:hover:border-blue-800 "+(e.disabled?"opacity-60 pointer-events-none":""),onClick:e.action,children:[e.recommended&&t.jsx("div",{className:"absolute -top-2 -right-2 bg-yellow-400 text-yellow-900 text-xs font-bold px-2 py-1 rounded-full shadow-md",children:"⭐ Recommended"}),t.jsxs("div",{className:"flex items-start justify-between mb-4",children:[t.jsx("div",{className:`p-3 rounded-xl ${e.color} bg-opacity-10`,children:t.jsx(e.icon,{className:`w-6 h-6 ${e.iconColor}`})}),t.jsxs("div",{className:"text-right",children:[t.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.timeEstimate}),e.difficulty&&t.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300 font-medium",children:e.difficulty})]})]}),t.jsx("h3",{className:"text-lg font-bold text-gray-900 dark:text-white mb-2",children:e.title}),t.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-3",children:e.subtitle}),t.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mb-4",children:e.description}),!n&&void 0!==e.progress&&t.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2",children:t.jsx(i.div,{className:`h-2 rounded-full ${e.color}`,initial:{width:0},animate:{width:`${e.progress}%`},transition:{duration:1,delay:.2*o}})})]},e.title))})]})};export{n as default};
