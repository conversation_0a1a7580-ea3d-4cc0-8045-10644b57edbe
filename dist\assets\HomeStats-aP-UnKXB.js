import{j as e,m as r,y as a,av as t,z as o}from"./vendor-DfmD63KD.js";const l=({icon:a,value:t,label:o,color:l="text-primary-600"})=>e.jsx(r.div,{whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},className:"bg-white dark:bg-expo-850 p-5 rounded-2xl shadow-card dark:shadow-card-dark hover:shadow-glow dark:hover:shadow-expo-dark transition-all duration-300 border border-gray-100 dark:border-expo-700 cursor-pointer group",children:e.jsxs("div",{className:"flex flex-col items-center text-center space-y-3",children:[e.jsx("div",{className:"p-3 rounded-2xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-expo-800 dark:to-expo-700 shadow-sm group-hover:shadow-md transition-all duration-300",children:e.jsx(a,{size:24,className:`${l} group-hover:scale-110 transition-transform duration-300`})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-gray-800 dark:text-dark-50",children:t}),e.jsx("div",{className:"text-sm text-gray-600 dark:text-dark-300 font-medium",children:o})]})]})}),d=({userStats:d,isNewUser:i,isLoading:n})=>{const s=(()=>{if(i)return[{icon:a,value:"0%",label:"Accuracy",color:"text-gray-400",bgColor:"bg-gray-100 dark:bg-gray-700",iconColor:"text-red-500",trend:{value:0,direction:"neutral",period:"week"},goal:{current:0,target:75,unit:"%"},subtitle:"No data yet",detailed:"Complete your first quiz to track accuracy"},{icon:t,value:"0h",label:"Study Time",color:"text-gray-400",bgColor:"bg-gray-100 dark:bg-gray-700",iconColor:"text-blue-500",trend:{value:0,direction:"neutral",period:"week"},goal:{current:0,target:2,unit:"hrs/day"},subtitle:"Ready to start",detailed:"Recommended: 2 hours daily study time"},{icon:o,value:"0",label:"Streak",color:"text-gray-400",bgColor:"bg-gray-100 dark:bg-gray-700",iconColor:"text-purple-500",trend:{value:0,direction:"neutral",period:"all time"},goal:{current:0,target:7,unit:"days"},subtitle:"Build momentum",detailed:"Start your first study streak today"}];{const e=d.accuracy>70?"up":d.accuracy>50?"neutral":"down",r=d.currentStreak>d.longestStreak-3?"up":"neutral";return[{icon:a,value:`${Math.round(d.accuracy)}%`,label:"Accuracy",color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/30",iconColor:"text-green-600",trend:{value:Math.max(0,Math.round(d.accuracy-70)),direction:e,period:"this week"},goal:{current:Math.round(d.accuracy),target:85,unit:"%"},subtitle:"up"===e?`+${Math.max(0,Math.round(d.accuracy-70))}% this week`:"Room for improvement",detailed:`${d.totalQuestions} questions answered, ${Math.round(d.accuracy*d.totalQuestions/100)} correct`},{icon:t,value:`${d.studyTime}h`,label:"Study Time",color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-100 dark:bg-blue-900/30",iconColor:"text-blue-600",trend:{value:Math.max(0,Math.round(d.studyTime/7*10)/10),direction:"up",period:"this week"},goal:{current:d.studyTime,target:50,unit:"hours"},subtitle:`+${Math.max(0,Math.round(d.studyTime/7*10)/10)}h this week`,detailed:`${Math.round(d.studyTime/7*10)/10}h daily average, ${50-d.studyTime}h to goal`},{icon:o,value:`${d.currentStreak}`,label:"Day Streak",color:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-100 dark:bg-purple-900/30",iconColor:"text-purple-600",trend:{value:d.currentStreak-Math.max(0,d.longestStreak-5),direction:r,period:"personal best"},goal:{current:d.currentStreak,target:Math.max(30,d.longestStreak+5),unit:"days"},subtitle:d.currentStreak>=d.longestStreak?"Personal best!":`Best: ${d.longestStreak} days`,detailed:`${d.currentStreak>=7?"🔥":""} ${d.currentStreak>=d.longestStreak?"New record!":Math.max(30,d.longestStreak+5)-d.currentStreak+" days to goal"}`}]}})();return n?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[1,2,3].map(r=>e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-6 animate-pulse",children:[e.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),e.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2"})]},r))}):e.jsx(r.div,{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.map((a,t)=>e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},children:e.jsx(l,{icon:a.icon,value:a.value,label:a.label,color:a.color,bgColor:a.bgColor,iconColor:a.iconColor,trend:a.trend,goal:a.goal,subtitle:a.subtitle,detailed:a.detailed})},a.label))})};export{d as default};
