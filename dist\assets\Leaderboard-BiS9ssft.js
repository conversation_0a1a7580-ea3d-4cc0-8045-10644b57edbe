import{r as e,j as a,m as s,aC as r,b as t,aD as c,u as n}from"./vendor-DfmD63KD.js";import{s as l}from"./chatService-BoDF_oo_.js";import"./index-gGBCxdYu.js";const o=[{id:1,name:"<PERSON>",country:"US",flag:"https://flagcdn.com/h20/us.png",score:2850,questionsAnswered:485,accuracy:94,streak:12,avatar:"https://i.pravatar.cc/150?img=1",school:"Harvard Medical School",year:"3rd Year",subjects:[{name:"Anatomy",score:95},{name:"Physiology",score:93}]},{id:2,name:"<PERSON>",country:"EG",flag:"https://flagcdn.com/h20/eg.png",score:2720,questionsAnswered:402,accuracy:91,streak:8,avatar:"https://i.pravatar.cc/150?img=2",school:"Cairo University",year:"2nd Year",subjects:[{name:"Pathology",score:92},{name:"Pharmacology",score:89}]},{id:3,name:"<PERSON>",country:"ES",flag:"https://flagcdn.com/h20/es.png",score:2650,questionsAnswered:378,accuracy:89,streak:15,avatar:"https://i.pravatar.cc/150?img=3",school:"Universidad Complutense",year:"4th Year",subjects:[{name:"Cardiology",score:91},{name:"Neurology",score:87}]},{id:4,name:"David Chen",country:"CN",flag:"https://flagcdn.com/h20/cn.png",score:2580,questionsAnswered:445,accuracy:87,streak:6,avatar:"https://i.pravatar.cc/150?img=4",school:"Peking University",year:"1st Year",subjects:[{name:"Biochemistry",score:88},{name:"Anatomy",score:86}]},{id:5,name:"Priya Sharma",country:"IN",flag:"https://flagcdn.com/h20/in.png",score:2520,questionsAnswered:395,accuracy:92,streak:9,avatar:"https://i.pravatar.cc/150?img=5",school:"AIIMS Delhi",year:"3rd Year",subjects:[{name:"Anatomy",score:85},{name:"Physiology",score:84}]},{id:6,name:"James Wilson",country:"GB",flag:"https://flagcdn.com/h20/gb.png",score:2460,questionsAnswered:412,accuracy:88,streak:7,avatar:"https://i.pravatar.cc/150?img=6",school:"Oxford University",year:"2nd Year",subjects:[{name:"Pharmacology",score:90},{name:"Pathology",score:86}]},{id:7,name:"jim kali",country:"US",flag:"https://flagcdn.com/h20/us.png",score:500,questionsAnswered:125,accuracy:78,streak:0,avatar:"https://i.pravatar.cc/150?img=7",school:"Local Medical School",year:"2nd Year",subjects:[{name:"Anatomy",score:78},{name:"Physiology",score:82}],isCurrentUser:!0},{id:8,name:"Anna Kowalski",country:"PL",flag:"https://flagcdn.com/h20/pl.png",score:2390,questionsAnswered:365,accuracy:90,streak:11,avatar:"https://i.pravatar.cc/150?img=8",school:"Medical University Warsaw",year:"3rd Year",subjects:[{name:"Microbiology",score:89},{name:"Immunology",score:91}]},{id:9,name:"Mohammed Al-Rashid",country:"AE",flag:"https://flagcdn.com/h20/ae.png",score:2320,questionsAnswered:388,accuracy:86,streak:5,avatar:"https://i.pravatar.cc/150?img=9",school:"UAE University",year:"4th Year",subjects:[{name:"Surgery",score:87},{name:"Medicine",score:85}]},{id:10,name:"Emily Thompson",country:"CA",flag:"https://flagcdn.com/h20/ca.png",score:2280,questionsAnswered:425,accuracy:85,streak:13,avatar:"https://i.pravatar.cc/150?img=10",school:"University of Toronto",year:"1st Year",subjects:[{name:"Biochemistry",score:84},{name:"Cell Biology",score:86}]},{id:11,name:"Roberto Silva",country:"BR",flag:"https://flagcdn.com/h20/br.png",score:2210,questionsAnswered:356,accuracy:89,streak:4,avatar:"https://i.pravatar.cc/150?img=11",school:"University of São Paulo",year:"3rd Year",subjects:[{name:"Cardiology",score:88},{name:"Respiratory",score:90}]},{id:12,name:"Fatima Al-Zahra",country:"SA",flag:"https://flagcdn.com/h20/sa.png",score:2180,questionsAnswered:342,accuracy:87,streak:8,avatar:"https://i.pravatar.cc/150?img=12",school:"King Saud University",year:"2nd Year",subjects:[{name:"Pathology",score:86},{name:"Pharmacology",score:88}]},{id:13,name:"Kevin Murphy",country:"IE",flag:"https://flagcdn.com/h20/ie.png",score:2150,questionsAnswered:398,accuracy:84,streak:6,avatar:"https://i.pravatar.cc/150?img=13",school:"Trinity College Dublin",year:"4th Year",subjects:[{name:"Surgery",score:85},{name:"Emergency Med",score:83}]},{id:14,name:"Lisa Andersson",country:"SE",flag:"https://flagcdn.com/h20/se.png",score:2120,questionsAnswered:375,accuracy:88,streak:9,avatar:"https://i.pravatar.cc/150?img=14",school:"Karolinska Institute",year:"3rd Year",subjects:[{name:"Neurology",score:87},{name:"Psychiatry",score:89}]},{id:15,name:"Hiroshi Tanaka",country:"JP",flag:"https://flagcdn.com/h20/jp.png",score:2090,questionsAnswered:423,accuracy:82,streak:7,avatar:"https://i.pravatar.cc/150?img=15",school:"University of Tokyo",year:"1st Year",subjects:[{name:"Anatomy",score:81},{name:"Physiology",score:83}]},{id:16,name:"Sophie Martin",country:"FR",flag:"https://flagcdn.com/h20/fr.png",score:2060,questionsAnswered:368,accuracy:86,streak:10,avatar:"https://i.pravatar.cc/150?img=16",school:"Université Paris Descartes",year:"2nd Year",subjects:[{name:"Biochemistry",score:85},{name:"Molecular Biology",score:87}]},{id:17,name:"Carlos Mendoza",country:"MX",flag:"https://flagcdn.com/h20/mx.png",score:2030,questionsAnswered:391,accuracy:83,streak:5,avatar:"https://i.pravatar.cc/150?img=17",school:"UNAM Mexico",year:"4th Year",subjects:[{name:"Medicine",score:82},{name:"Pediatrics",score:84}]},{id:18,name:"Ingrid Nielsen",country:"DK",flag:"https://flagcdn.com/h20/dk.png",score:2e3,questionsAnswered:334,accuracy:87,streak:12,avatar:"https://i.pravatar.cc/150?img=18",school:"University of Copenhagen",year:"3rd Year",subjects:[{name:"Pharmacology",score:86},{name:"Toxicology",score:88}]},{id:19,name:"Marco Bianchi",country:"IT",flag:"https://flagcdn.com/h20/it.png",score:1980,questionsAnswered:356,accuracy:85,streak:3,avatar:"https://i.pravatar.cc/150?img=19",school:"University of Milan",year:"2nd Year",subjects:[{name:"Pathology",score:84},{name:"Histology",score:86}]},{id:20,name:"Aisha Patel",country:"ZA",flag:"https://flagcdn.com/h20/za.png",score:1950,questionsAnswered:378,accuracy:84,streak:8,avatar:"https://i.pravatar.cc/150?img=20",school:"University of Cape Town",year:"1st Year",subjects:[{name:"Anatomy",score:83},{name:"Cell Biology",score:85}]}],i=({periods:e,selectedPeriod:s,onPeriodChange:r})=>a.jsx("div",{className:"flex gap-2 bg-white dark:bg-gray-800 rounded-xl p-1 border border-gray-100 dark:border-gray-700 w-full md:w-auto",children:e.map(e=>a.jsx("button",{onClick:()=>r(e.value),className:"flex-1 md:flex-initial py-2 px-3 md:px-6 rounded-lg text-sm font-medium transition-all "+(s===e.value?"bg-orange-500 text-white shadow-sm":"text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"),children:e.label},e.value))}),d=({topThree:e})=>{if(!e||0===e.length)return null;const n=(e,n,l)=>{const o={1:a.jsx(c,{className:"w-5 h-5 md:w-6 md:h-6 mx-auto mb-1"}),2:a.jsx(t,{className:"w-4 h-4 md:w-5 md:h-5 text-gray-400 mx-auto mb-1"}),3:a.jsx(r,{className:"w-4 h-4 md:w-5 md:h-5 text-amber-600 mx-auto mb-1"})},i={1:{container:"text-center",avatar:"relative w-16 h-16 md:w-20 md:h-20 mx-auto mb-2",avatarImg:"w-full h-full rounded-full object-cover border-4 border-yellow-300",flag:"absolute -bottom-1 -right-1 w-5 h-4 md:w-6 md:h-5 rounded-sm border-2 border-yellow-300",card:"bg-gradient-to-br from-yellow-400 to-yellow-500 p-4 rounded-lg text-white",name:"text-sm md:text-base font-bold",score:"text-xs md:text-sm opacity-90"},2:{container:"text-center",avatar:"relative w-12 h-12 md:w-16 md:h-16 mx-auto mb-2",avatarImg:"w-full h-full rounded-full object-cover border-2 border-gray-300",flag:"absolute -bottom-1 -right-1 w-4 h-3 md:w-5 md:h-4 rounded-sm border border-white",card:"bg-gray-200 dark:bg-gray-700 p-3 rounded-lg",name:"text-xs md:text-sm font-bold text-gray-700 dark:text-gray-300",score:"text-xs md:text-sm text-gray-500 dark:text-gray-400"},3:{container:"text-center",avatar:"relative w-12 h-12 md:w-16 md:h-16 mx-auto mb-2",avatarImg:"w-full h-full rounded-full object-cover border-2 border-amber-600",flag:"absolute -bottom-1 -right-1 w-4 h-3 md:w-5 md:h-4 rounded-sm border border-white",card:"bg-amber-100 dark:bg-amber-900/30 p-3 rounded-lg",name:"text-xs md:text-sm font-bold text-amber-800 dark:text-amber-300",score:"text-xs md:text-sm text-amber-600 dark:text-amber-400"}}[n];return a.jsxs(s.div,{initial:{y:30,opacity:0,scale:1===n?.9:1},animate:{y:0,opacity:1,scale:1},transition:{delay:l,type:1===n?"spring":"tween",stiffness:1===n?200:void 0},className:i.container,children:[a.jsxs("div",{className:i.avatar,children:[a.jsx("img",{src:e.avatar,alt:e.name,className:i.avatarImg}),a.jsx("img",{src:e.flag,alt:e.country,className:i.flag})]}),a.jsxs("div",{className:i.card,children:[o[n],a.jsx("p",{className:i.name,children:e.name}),a.jsx("p",{className:i.score,children:e.score})]})]})};return a.jsxs(s.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.1},className:"bg-white dark:bg-gray-800 rounded-xl p-4 md:p-6 shadow-sm border border-gray-100 dark:border-gray-700",children:[a.jsx("h3",{className:"text-base md:text-lg font-bold text-gray-800 dark:text-white mb-4",children:"Top Performers"}),a.jsxs("div",{className:"flex justify-center items-end gap-4",children:[e[1]&&n(e[1],2,.3),e[0]&&n(e[0],1,.4),e[2]&&n(e[2],3,.2)]})]})},m=({currentUserData:e,currentUserRank:r})=>e?a.jsxs(s.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.4},className:"hidden lg:block bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 md:p-6",children:[a.jsx("h3",{className:"text-base md:text-lg font-bold text-blue-700 dark:text-blue-300 mb-4",children:"Your Performance"}),a.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[a.jsxs("div",{className:"relative w-16 h-16",children:[a.jsx("img",{src:e.avatar,alt:e.name,className:"w-full h-full rounded-full object-cover border-2 border-blue-300"}),a.jsx("img",{src:e.flag,alt:e.country,className:"absolute -bottom-1 -right-1 w-5 h-4 rounded-sm border border-white"})]}),a.jsxs("div",{children:[a.jsx("p",{className:"font-bold text-lg text-blue-700 dark:text-blue-300",children:e.name}),a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.school})]})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Rank"}),a.jsxs("span",{className:"font-bold text-lg text-blue-700 dark:text-blue-300",children:["#",r]})]}),a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Score"}),a.jsx("span",{className:"font-bold text-lg text-blue-700 dark:text-blue-300",children:e.score})]}),a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Accuracy"}),a.jsxs("span",{className:"font-bold text-lg text-blue-700 dark:text-blue-300",children:[e.accuracy,"%"]})]}),a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Questions"}),a.jsx("span",{className:"font-bold text-lg text-blue-700 dark:text-blue-300",children:e.questionsAnswered})]}),a.jsxs("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Streak"}),a.jsxs("span",{className:"font-bold text-lg text-blue-700 dark:text-blue-300",children:[e.streak," days"]})]})]})]}):null,x=e=>{switch(e){case 1:return a.jsx(c,{className:"w-5 h-5 text-yellow-400"});case 2:return a.jsx(t,{className:"w-5 h-5 text-gray-400"});case 3:return a.jsx(r,{className:"w-5 h-5 text-amber-600"});default:return a.jsxs("span",{className:"text-sm font-bold text-gray-400",children:["#",e]})}},h=(e=!1)=>e?"text-blue-700 dark:text-blue-300":"text-gray-800 dark:text-gray-200",g=({leaderboardData:e,totalParticipants:r})=>{const t=n();return e.find(e=>e.isCurrentUser),a.jsxs(s.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.5},className:"lg:col-span-2 bg-white dark:bg-gray-800 rounded-xl p-4 md:p-6 shadow-sm border border-gray-100 dark:border-gray-700",children:[a.jsxs("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-base md:text-lg font-bold text-gray-800 dark:text-white",children:"All Rankings"}),a.jsxs("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[r," participants"]})]}),a.jsxs("div",{className:"hidden md:grid md:grid-cols-12 gap-4 px-4 py-2 border-b border-gray-200 dark:border-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400",children:[a.jsx("div",{className:"col-span-1",children:"Rank"}),a.jsx("div",{className:"col-span-5",children:"Student"}),a.jsx("div",{className:"col-span-2 text-center",children:"Stats"}),a.jsx("div",{className:"col-span-2 text-center",children:"Performance"}),a.jsx("div",{className:"col-span-2 text-right",children:"Score"})]}),a.jsx("div",{className:"space-y-2",children:e.map((e,r)=>a.jsxs(s.div,{initial:{x:-20,opacity:0},animate:{x:0,opacity:1},transition:{delay:.6+.05*r},className:"grid grid-cols-1 md:grid-cols-12 gap-2 md:gap-4 p-3 md:p-4 rounded-lg transition-colors "+(e.isCurrentUser?"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800":"hover:bg-gray-50 dark:hover:bg-gray-700"),children:[a.jsxs("div",{className:"md:hidden flex items-center gap-3",children:[a.jsx("div",{className:"flex-shrink-0 w-8 flex justify-center",children:x(r+1)}),a.jsxs("div",{className:"relative w-10 h-10 flex-shrink-0",children:[a.jsx("img",{src:e.avatar,alt:e.name,className:"w-10 h-10 rounded-full object-cover"}),a.jsx("img",{src:e.flag,alt:e.country,className:"absolute -bottom-1 -right-1 w-4 h-3 rounded-sm border border-white"})]}),a.jsxs("div",{className:"flex-1 min-w-0",children:[a.jsxs("p",{className:`font-medium text-sm truncate ${h(e.isCurrentUser)}`,children:[e.name,e.isCurrentUser&&a.jsx("span",{className:"text-xs ml-1",children:"(You)"})]}),a.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-xs",children:e.school})]}),a.jsxs("div",{className:"text-right",children:[a.jsx("p",{className:`font-bold text-sm ${h(e.isCurrentUser)}`,children:e.score}),a.jsxs("p",{className:"text-gray-500 dark:text-gray-400 text-xs",children:[e.questionsAnswered," Qs"]})]}),!e.isCurrentUser&&a.jsx("button",{className:"ml-2 px-2 py-1 bg-blue-500 text-white rounded text-xs",onClick:async()=>{const a=await l(e.id);t(`/chat?chatId=${a}`)},children:"Chat"})]}),a.jsxs("div",{className:"hidden md:contents",children:[a.jsx("div",{className:"col-span-1 flex items-center justify-center",children:x(r+1)}),a.jsxs("div",{className:"col-span-5 flex items-center gap-3",children:[a.jsxs("div",{className:"relative w-12 h-12 flex-shrink-0",children:[a.jsx("img",{src:e.avatar,alt:e.name,className:"w-12 h-12 rounded-full object-cover"}),a.jsx("img",{src:e.flag,alt:e.country,className:"absolute -bottom-1 -right-1 w-5 h-4 rounded-sm border border-white"})]}),a.jsxs("div",{className:"flex-1 min-w-0",children:[a.jsxs("p",{className:`font-medium text-base ${h(e.isCurrentUser)}`,children:[e.name,e.isCurrentUser&&a.jsx("span",{className:"text-sm ml-2",children:"(You)"})]}),a.jsxs("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:[e.school," • ",e.year]})]})]}),a.jsx("div",{className:"col-span-2 flex items-center justify-center gap-4 text-sm",children:a.jsxs("div",{className:"text-center",children:[a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Questions"}),a.jsx("p",{className:"font-semibold text-gray-800 dark:text-gray-200",children:e.questionsAnswered})]})}),a.jsxs("div",{className:"col-span-2 flex items-center justify-center gap-4 text-sm",children:[a.jsxs("div",{className:"text-center",children:[a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Accuracy"}),a.jsxs("p",{className:"font-semibold text-gray-800 dark:text-gray-200",children:[e.accuracy,"%"]})]}),a.jsxs("div",{className:"text-center",children:[a.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Streak"}),a.jsxs("p",{className:"font-semibold text-gray-800 dark:text-gray-200",children:[e.streak,"d"]})]})]}),a.jsxs("div",{className:"col-span-2 flex items-center justify-end",children:[a.jsx("p",{className:`font-bold text-xl ${h(e.isCurrentUser)}`,children:e.score}),!e.isCurrentUser&&a.jsx("button",{className:"ml-4 px-3 py-1 bg-blue-500 text-white rounded text-xs font-semibold hover:bg-blue-600",onClick:async()=>{const a=await l(e.id);t(`/chat?chatId=${a}`)},children:"Chat"})]})]})]},e.id))})]})},u=()=>{const{selectedPeriod:r,setSelectedPeriod:c,periods:n,leaderboardData:l,topThree:x,currentUserData:h,currentUserRank:u,totalParticipants:y}=(()=>{const[a,s]=e.useState("week");return{selectedPeriod:a,setSelectedPeriod:s,periods:[{value:"day",label:"Today"},{value:"week",label:"This Week"},{value:"month",label:"This Month"},{value:"all",label:"All Time"}],...e.useMemo(()=>{const e=o,a=e.slice(0,3),s=e.find(e=>e.isCurrentUser),r=e.findIndex(e=>e.isCurrentUser)+1;return{leaderboardData:e,topThree:a,currentUserData:s,currentUserRank:r,totalParticipants:e.length}},[a])}})();return a.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 p-3 pb-20 md:pb-6",children:a.jsxs("div",{className:"max-w-7xl mx-auto",children:[a.jsx(s.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"mb-6 md:mb-8",children:a.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[a.jsxs("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"bg-gradient-to-r from-orange-500 to-orange-600 p-2 md:p-3 rounded-xl",children:a.jsx(t,{className:"w-6 h-6 md:w-8 md:h-8 text-white"})}),a.jsxs("div",{children:[a.jsx("h1",{className:"text-2xl md:text-4xl font-bold text-gray-800 dark:text-white",children:"Leaderboard"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-sm md:text-base",children:"See who's leading the way"})]})]}),a.jsx(i,{periods:n,selectedPeriod:r,onPeriodChange:c})]})}),a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6",children:[a.jsxs("div",{className:"lg:col-span-1 space-y-4",children:[a.jsx(d,{topThree:x}),a.jsx(m,{currentUserData:h,currentUserRank:u})]}),a.jsx(g,{leaderboardData:l,totalParticipants:y})]})]})})};export{u as default};
