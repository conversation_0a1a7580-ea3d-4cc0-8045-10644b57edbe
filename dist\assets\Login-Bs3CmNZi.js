import{r as e,u as a,j as r,m as s,i as t,T as i,aY as o,aZ as l,aS as d,aP as n}from"./vendor-DfmD63KD.js";import{u as m,c,l as p}from"./index-gGBCxdYu.js";import{V as x,c as y,v as u,a as g}from"./formValidation-jOc8dLyX.js";const h=()=>{const[h,w]=e.useState({email:"",password:""}),[b,f]=e.useState({}),[v,j]=e.useState({}),[k,N]=e.useState(!1),[S,P]=e.useState(""),{isConfigured:C}=m(),V=a(),E=y({email:e=>g(e),password:e=>u(e,"Password")}),I=(e,a)=>{if(w(r=>({...r,[e]:a})),P(""),v[e]||a){const r=E.validateField(e,a,h);f(a=>({...a,[e]:r.error}))}},T=e=>{j(a=>({...a,[e]:!0}));const a=E.validateField(e,h[e],h);f(r=>({...r,[e]:a.error}))},B=h.email&&h.password&&!b.email&&!b.password;return r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 dark:from-expo-950 dark:to-expo-900 flex items-center justify-center px-4",children:r.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"w-full max-w-md",children:[r.jsxs("div",{className:"text-center mb-8",children:[r.jsx(s.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2},className:"inline-flex items-center justify-center w-16 h-16 bg-primary-600 rounded-2xl mb-4 shadow-lg",children:r.jsx(t,{size:32,className:"text-white"})}),r.jsx("h1",{className:"text-3xl font-bold text-gray-800 dark:text-dark-50 mb-2",children:"Welcome Back"}),r.jsx("p",{className:"text-gray-600 dark:text-dark-300",children:"Continue your USMLE preparation journey"})]}),r.jsx(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"bg-white dark:bg-expo-850 rounded-2xl p-8 shadow-card dark:shadow-card-dark border border-gray-100 dark:border-expo-700",children:r.jsxs("form",{onSubmit:async e=>{e.preventDefault(),N(!0),P("");const{isValid:a,errors:r}=E.validateAll(h);if(!a)return f(r),j({email:!0,password:!0}),void N(!1);if(!h.email||!h.password)return P("Please fill in all fields"),void N(!1);try{await c.signIn(h.email.trim(),h.password),V("/")}catch(s){(e=>{p.error("Login error occurred",{formData:{email:h.email}},e),e.message.includes("JSON")||e.message.includes("Unexpected token")?P("Server communication error. Please try again."):e.message.includes("fetch")||e.message.includes("Network")?P("Network error. Please check your connection and try again."):e.message.includes("Invalid login credentials")?P("Invalid email or password. Please check your credentials."):e.message.includes("Email not confirmed")?P("Please verify your email address before signing in."):e.message.includes("Too many requests")?P("Too many login attempts. Please wait a moment and try again."):P(e.message||"An error occurred during sign in. Please try again.")})(s)}finally{N(!1)}},className:"space-y-6",noValidate:!0,children:[!C&&r.jsxs(s.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 p-4 rounded-xl text-sm font-medium border border-yellow-200 dark:border-yellow-800 flex items-center gap-3",children:[r.jsx(i,{size:20}),r.jsxs("div",{children:[r.jsx("h3",{className:"font-bold",children:"Supabase Not Configured"}),r.jsx("p",{className:"text-xs",children:"Please set up your .env.local file to enable sign-in."})]})]}),S&&r.jsx(s.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-xl text-sm font-medium border border-red-200 dark:border-red-800","data-testid":"login-error",children:S}),r.jsx(x,{type:"email",name:"email",value:h.email,onChange:I,onBlur:T,placeholder:"Enter your email",label:"Email Address",icon:o,error:v.email?b.email:"",isValid:!b.email&&h.email,required:!0,autoComplete:"email","data-testid":"email-input"}),r.jsx(x,{type:"password",name:"password",value:h.password,onChange:I,onBlur:T,placeholder:"Enter your password",label:"Password",icon:l,error:v.password?b.password:"",isValid:!b.password&&h.password,required:!0,showPasswordToggle:!0,autoComplete:"current-password","data-testid":"password-input"}),r.jsx("div",{className:"text-right",children:r.jsx(d,{to:"/forgot-password",className:"text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium",children:"Forgot your password?"})}),r.jsx(s.button,{type:"submit",disabled:k||!C||!B,whileHover:!k&&C&&B?{scale:1.02}:{},whileTap:!k&&C&&B?{scale:.98}:{},className:`\n                w-full py-3 px-6 rounded-xl font-bold text-white transition-all duration-200\n                ${!k&&C&&B?"bg-primary-600 hover:bg-primary-700 shadow-lg hover:shadow-xl":"bg-gray-400 cursor-not-allowed opacity-50"}\n              `,"data-testid":"login-submit",children:k?r.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[r.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),r.jsx("span",{children:"Signing In..."})]}):C?"Sign In":"Configuration Missing"}),r.jsx("div",{className:"text-center pt-4 border-t border-gray-200 dark:border-expo-700",children:r.jsxs("p",{className:"text-sm text-gray-600 dark:text-dark-300",children:["Don't have an account?"," ",r.jsx(d,{to:"/signup",className:"text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-bold",children:"Sign Up"})]})})]})}),r.jsx(s.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"text-center mt-6",children:r.jsxs(d,{to:"/",className:"inline-flex items-center text-sm text-gray-600 dark:text-dark-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors",children:[r.jsx(n,{size:16,className:"mr-2"}),"Back to Home"]})})]})})};export{h as default};
