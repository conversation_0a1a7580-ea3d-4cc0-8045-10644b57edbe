import{j as e,m as r,ak as i,aS as s,aP as a}from"./vendor-DfmD63KD.js";const n=()=>e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 dark:from-expo-950 dark:to-expo-900",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-6 py-12",children:[e.jsxs(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-12",children:[e.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-primary-600 rounded-2xl mb-6 shadow-lg",children:e.jsx(i,{size:32,className:"text-white"})}),e.jsx("h1",{className:"text-4xl font-bold text-gray-800 dark:text-dark-50 mb-4",children:"Privacy Policy"}),e.jsx("p",{className:"text-gray-600 dark:text-dark-300 text-lg",children:"Last updated: January 2024"})]}),e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-white dark:bg-expo-850 rounded-2xl p-8 shadow-card dark:shadow-card-dark border border-gray-100 dark:border-expo-700",children:e.jsxs("div",{className:"prose prose-gray dark:prose-invert max-w-none",children:[e.jsx("h2",{children:"1. Information We Collect"}),e.jsx("p",{children:"We collect information you provide directly to us, such as when you create an account, participate in quizzes, or contact us for support."}),e.jsx("h3",{children:"Personal Information"}),e.jsxs("ul",{children:[e.jsx("li",{children:"Name and email address"}),e.jsx("li",{children:"Profile information (school, bio, country)"}),e.jsx("li",{children:"Quiz performance and study progress"}),e.jsx("li",{children:"Account preferences and settings"})]}),e.jsx("h2",{children:"2. How We Use Your Information"}),e.jsx("p",{children:"We use the information we collect to:"}),e.jsxs("ul",{children:[e.jsx("li",{children:"Provide, maintain, and improve our services"}),e.jsx("li",{children:"Track your learning progress and provide personalized content"}),e.jsx("li",{children:"Send you technical notices and security alerts"}),e.jsx("li",{children:"Respond to your comments, questions, and requests"}),e.jsx("li",{children:"Generate leaderboards and performance statistics"})]}),e.jsx("h2",{children:"3. Information Sharing"}),e.jsx("p",{children:"We do not sell, trade, or otherwise transfer your personal information to third parties except as described below:"}),e.jsxs("ul",{children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Aggregate Data:"})," We may share anonymous, aggregate statistics about users"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Legal Requirements:"})," When required by law or to protect our rights"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Service Providers:"})," With trusted partners who assist in operating our service"]})]}),e.jsx("h2",{children:"4. Data Security"}),e.jsx("p",{children:"We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. This includes:"}),e.jsxs("ul",{children:[e.jsx("li",{children:"Secure data transmission using SSL encryption"}),e.jsx("li",{children:"Secure database storage with access controls"}),e.jsx("li",{children:"Regular security audits and updates"})]}),e.jsx("h2",{children:"5. Data Retention"}),e.jsx("p",{children:"We retain your personal information for as long as your account is active or as needed to provide you services. You may request deletion of your account and personal data at any time."}),e.jsx("h2",{children:"6. Your Rights"}),e.jsx("p",{children:"You have the right to:"}),e.jsxs("ul",{children:[e.jsx("li",{children:"Access and update your personal information"}),e.jsx("li",{children:"Delete your account and personal data"}),e.jsx("li",{children:"Export your quiz performance data"}),e.jsx("li",{children:"Opt out of non-essential communications"})]}),e.jsx("h2",{children:"7. Cookies and Tracking"}),e.jsx("p",{children:"We use cookies and similar technologies to enhance your experience, remember your preferences, and analyze site usage. You can control cookie settings through your browser."}),e.jsx("h2",{children:"8. Children's Privacy"}),e.jsx("p",{children:"Our service is intended for users 13 years and older. We do not knowingly collect personal information from children under 13."}),e.jsx("h2",{children:"9. Changes to Privacy Policy"}),e.jsx("p",{children:'We may update this privacy policy from time to time. We will notify you of any changes by posting the new policy on this page and updating the "Last updated" date.'}),e.jsx("h2",{children:"10. Contact Us"}),e.jsx("p",{children:"If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>."})]})}),e.jsx(r.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"mt-8 text-center",children:e.jsxs(s,{to:"/auth/signup",className:"inline-flex items-center gap-2 text-gray-600 dark:text-dark-300 hover:text-gray-800 dark:hover:text-dark-100 transition-colors",children:[e.jsx(a,{size:16}),e.jsx("span",{className:"font-medium",children:"Back to Sign Up"})]})})]})});export{n as default};
