import{u as e,j as a,m as t,U as s,b as r,y as i,av as l,az as d,r as n,aA as c,d as o,e as x,ay as m,aB as y}from"./vendor-DfmD63KD.js";import{u as g}from"./index-gGBCxdYu.js";import{s as h}from"./chatService-BoDF_oo_.js";const u=()=>{const{user:r,profile:i}=g(),l=e(),d=r?.id===i?.id;return a.jsxs(t.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.1},className:"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm text-center border border-gray-50 dark:border-gray-700",children:[a.jsx(t.div,{whileHover:{scale:1.05},className:"w-20 h-20 rounded-full mx-auto mb-4 overflow-hidden ring-4 ring-blue-100 dark:ring-blue-900",children:i?.avatar_url?a.jsx("img",{src:i.avatar_url,alt:"Profile",className:"w-full h-full object-cover"}):a.jsx("div",{className:"w-full h-full bg-gradient-to-br from-primary-400 to-secondary-500 flex items-center justify-center",children:a.jsx(s,{size:32,className:"text-white"})})}),a.jsx("h2",{className:"text-xl font-bold text-gray-800 dark:text-dark-50 mb-1",children:i?.display_name||r?.user_metadata?.full_name||"USMLE Student"}),a.jsx("p",{className:"text-gray-600 dark:text-dark-300 text-sm",children:i?.school_of_medicine||"USMLE Step 1 Candidate"}),a.jsx("div",{className:"mt-4 flex justify-center",children:a.jsx("span",{className:"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium",children:i?.achievement_grades?.name||"Beginner"})}),i?.countries&&a.jsxs("div",{className:"mt-2 flex items-center justify-center gap-2",children:[a.jsx("span",{className:"text-xl",children:i.countries.flag_emoji}),a.jsx("span",{className:"text-sm text-gray-600 dark:text-dark-300",children:i.countries.name})]}),!d&&i?.id&&a.jsx("button",{className:"mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg font-semibold hover:bg-blue-600",onClick:async()=>{const e=await h(i.id);l(`/chat?chatId=${e}`)},children:"Chat"})]})},b=()=>{const{profile:e}=g(),s=[{icon:r,label:"Total Points",value:e?.total_points?.toLocaleString()||"0",color:"text-yellow-500"},{icon:i,label:"Overall Accuracy",value:(()=>{if(!e?.total_questions_answered||0===e.total_questions_answered)return"0%";const a=e.correct_answers/e.total_questions_answered*100;return`${Math.round(a)}%`})(),color:"text-blue-500"},{icon:l,label:"Study Time",value:(()=>{if(!e?.total_study_time_seconds)return"0h";return`${Math.round(e.total_study_time_seconds/3600*10)/10}h`})(),color:"text-green-500"}];return a.jsx(t.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2},className:"grid grid-cols-3 gap-4",children:s.map((e,s)=>a.jsxs(t.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.3+.1*s},whileHover:{scale:1.05},className:"bg-white dark:bg-gray-800 rounded-xl p-4 shadow-card dark:shadow-card-dark text-center border border-gray-50 dark:border-gray-700",children:[a.jsx(e.icon,{size:24,className:`mx-auto mb-2 ${e.color}`}),a.jsx("p",{className:"text-2xl font-bold text-gray-800 dark:text-dark-50",children:e.value}),a.jsx("p",{className:"text-sm text-gray-600 dark:text-dark-300",children:e.label})]},e.label))})},p=()=>a.jsxs(t.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.5},className:"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-card dark:shadow-card-dark border border-gray-50 dark:border-gray-700",children:[a.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[a.jsx(d,{size:20,className:"text-yellow-600"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-dark-50",children:"Achievements"})]}),a.jsx("div",{className:"space-y-3",children:[{title:"First Quiz",description:"Complete your first quiz",earned:!0},{title:"Perfect Score",description:"Get 100% on any quiz",earned:!0},{title:"Study Streak",description:"7 days in a row",earned:!1},{title:"Category Master",description:"Complete all questions in a category",earned:!1}].map((e,s)=>a.jsxs(t.div,{initial:{x:-20,opacity:0},animate:{x:0,opacity:1},transition:{delay:.6+.1*s},className:"flex items-center gap-3 p-3 rounded-lg "+(e.earned?"bg-green-50 border border-green-200":"bg-gray-50"),children:[a.jsx("div",{className:"w-3 h-3 rounded-full "+(e.earned?"bg-green-500":"bg-gray-300")}),a.jsxs("div",{className:"flex-1",children:[a.jsx("p",{className:"font-medium "+(e.earned?"text-green-800":"text-gray-600"),children:e.title}),a.jsx("p",{className:"text-sm "+(e.earned?"text-green-600":"text-gray-500"),children:e.description})]})]},e.title))})]}),j=()=>{const[s,r]=n.useState(!1),{signOut:i}=g(),l=e(),d=[{icon:o,label:"Notifications",enabled:!0},{icon:x,label:"Dark Mode",enabled:!1}];return a.jsxs(t.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.8},className:"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-card dark:shadow-card-dark border border-gray-50 dark:border-gray-700",children:[a.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[a.jsx(c,{size:20,className:"text-gray-600"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-dark-50",children:"Settings"})]}),a.jsxs("div",{className:"space-y-4",children:[d.map((e,s)=>a.jsxs(t.div,{initial:{x:-20,opacity:0},animate:{x:0,opacity:1},transition:{delay:.9+.1*s},className:"flex items-center justify-between",children:[a.jsxs("div",{className:"flex items-center gap-3",children:[a.jsx(e.icon,{size:20,className:"text-gray-600 dark:text-dark-300"}),a.jsx("span",{className:"text-gray-800 dark:text-dark-50",children:e.label})]}),a.jsx(t.button,{whileTap:{scale:.95},className:"w-12 h-6 rounded-full transition-colors "+(e.enabled?"bg-blue-500":"bg-gray-300"),children:a.jsx(t.div,{className:"w-5 h-5 bg-white rounded-full shadow-sm",animate:{x:e.enabled?26:2},transition:{type:"spring",stiffness:300,damping:30}})})]},e.label)),a.jsxs(t.button,{initial:{x:-20,opacity:0},animate:{x:0,opacity:1},transition:{delay:1.1},whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full flex items-center gap-3 p-3 text-left hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors",children:[a.jsx(m,{size:20,className:"text-gray-600 dark:text-dark-300"}),a.jsx("span",{className:"text-gray-800 dark:text-dark-50",children:"More Settings"})]}),a.jsxs(t.button,{initial:{x:-20,opacity:0},animate:{x:0,opacity:1},transition:{delay:1.2},whileHover:{scale:1.02},whileTap:{scale:.98},onClick:async()=>{r(!0);try{await i(),l("/auth/welcome",{replace:!0})}catch(e){console.error("Sign out error:",e)}finally{r(!1)}},disabled:s,className:"w-full flex items-center gap-3 p-3 text-left hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors text-red-600 dark:text-red-400 disabled:opacity-50 disabled:cursor-not-allowed",children:[a.jsx(y,{size:20}),a.jsx("span",{children:s?"Signing Out...":"Sign Out"})]})]})]})},f=()=>{const{user:e,profile:r}=g();return a.jsxs(t.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:1},className:"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-card dark:shadow-card-dark border border-gray-50 dark:border-gray-700",children:[a.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[a.jsx(s,{size:20,className:"text-gray-600"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-dark-50",children:"Account Information"})]}),a.jsxs("div",{className:"space-y-3 text-sm",children:[a.jsxs("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600 dark:text-dark-300",children:"Email:"}),a.jsx("span",{className:"text-gray-800 dark:text-dark-50",children:e?.email})]}),r?.bio&&a.jsxs("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600 dark:text-dark-300",children:"Bio:"}),a.jsx("span",{className:"text-gray-800 dark:text-dark-50",children:r.bio})]}),a.jsxs("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-gray-600 dark:text-dark-300",children:"Member Since:"}),a.jsx("span",{className:"text-gray-800 dark:text-dark-50",children:new Date(e?.created_at).toLocaleDateString()})]})]})]})},k=()=>a.jsxs(t.div,{initial:{opacity:0},animate:{opacity:1},className:"space-y-6",children:[a.jsx(u,{}),a.jsx(b,{}),a.jsx(p,{}),a.jsx(j,{}),a.jsx(f,{})]});export{k as default};
