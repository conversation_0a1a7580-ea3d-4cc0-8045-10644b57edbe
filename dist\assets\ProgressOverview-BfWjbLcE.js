import{u as e,j as a,m as t,b as s,y as r,z as l,az as i,at as d,av as c}from"./vendor-DfmD63KD.js";const n=({userStats:n,isNewUser:x})=>{const o=e();if(x)return a.jsx(t.div,{className:"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.5},children:a.jsxs("div",{className:"text-center py-8",children:[a.jsx("div",{className:"p-4 bg-blue-100 dark:bg-blue-900/30 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center",children:a.jsx(s,{className:"w-10 h-10 text-blue-600"})}),a.jsx("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:"Your Journey Starts Here!"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:"Complete your first quiz to unlock detailed progress tracking, performance analytics, and personalized study recommendations."}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[a.jsxs("div",{className:"p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[a.jsx(r,{className:"w-6 h-6 text-gray-400 mx-auto mb-2"}),a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Accuracy Tracking"})]}),a.jsxs("div",{className:"p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[a.jsx(l,{className:"w-6 h-6 text-gray-400 mx-auto mb-2"}),a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Progress Analytics"})]}),a.jsxs("div",{className:"p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[a.jsx(i,{className:"w-6 h-6 text-gray-400 mx-auto mb-2"}),a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Achievement System"})]})]}),a.jsxs(t.button,{onClick:()=>o("/quick-quiz"),className:"bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2 mx-auto",whileHover:{scale:1.05},whileTap:{scale:.95},children:[a.jsx("span",{children:"Take Your First Quiz"}),a.jsx(d,{className:"w-5 h-5"})]})]})});const m={weeklyGoal:{current:Math.round(n.studyTime),target:14,unit:"hours",percentage:Math.min(100,n.studyTime/14*100)},accuracyGoal:{current:Math.round(n.accuracy),target:85,unit:"%",percentage:n.accuracy/85*100},streakGoal:{current:n.currentStreak,target:30,unit:"days",percentage:n.currentStreak/30*100}};return a.jsxs(t.div,{className:"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.5},children:[a.jsxs("div",{className:"flex items-center justify-between mb-6",children:[a.jsx("h3",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"Progress Overview"}),a.jsxs(t.button,{onClick:()=>o("/profile"),className:"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium flex items-center space-x-1",whileHover:{scale:1.05},children:[a.jsx("span",{children:"View Details"}),a.jsx(d,{className:"w-4 h-4"})]})]}),a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{children:[a.jsxs("div",{className:"flex items-center justify-between mb-2",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(c,{className:"w-5 h-5 text-blue-600"}),a.jsx("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Weekly Study Goal"})]}),a.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:[m.weeklyGoal.current,"/",m.weeklyGoal.target," ",m.weeklyGoal.unit]})]}),a.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3",children:a.jsx(t.div,{className:"bg-blue-600 h-3 rounded-full",initial:{width:0},animate:{width:`${Math.min(100,m.weeklyGoal.percentage)}%`},transition:{duration:1,delay:.2}})})]}),a.jsxs("div",{children:[a.jsxs("div",{className:"flex items-center justify-between mb-2",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(r,{className:"w-5 h-5 text-green-600"}),a.jsx("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Accuracy Goal"})]}),a.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:[m.accuracyGoal.current,"/",m.accuracyGoal.target,m.accuracyGoal.unit]})]}),a.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3",children:a.jsx(t.div,{className:"bg-green-600 h-3 rounded-full",initial:{width:0},animate:{width:`${Math.min(100,m.accuracyGoal.percentage)}%`},transition:{duration:1,delay:.4}})})]}),a.jsxs("div",{children:[a.jsxs("div",{className:"flex items-center justify-between mb-2",children:[a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(l,{className:"w-5 h-5 text-purple-600"}),a.jsx("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Streak Goal"})]}),a.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:[m.streakGoal.current,"/",m.streakGoal.target," ",m.streakGoal.unit]})]}),a.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3",children:a.jsx(t.div,{className:"bg-purple-600 h-3 rounded-full",initial:{width:0},animate:{width:`${Math.min(100,m.streakGoal.percentage)}%`},transition:{duration:1,delay:.6}})})]})]}),n.currentStreak>=7&&a.jsx(t.div,{className:"mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.6,delay:.8},children:a.jsxs("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"p-2 bg-yellow-400 rounded-full",children:a.jsx(s,{className:"w-5 h-5 text-yellow-900"})}),a.jsxs("div",{children:[a.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"🔥 On Fire!"}),a.jsxs("div",{className:"text-xs text-gray-600 dark:text-gray-300",children:[n.currentStreak," day study streak - keep it going!"]})]})]})})]})};export{n as default};
