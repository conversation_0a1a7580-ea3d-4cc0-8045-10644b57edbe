import{r as e,aE as t,j as a,m as s,l as r,aF as i,av as n,A as o,aG as l,T as c,b as d,y as x,a as m,au as u,aH as g,aI as h,aJ as p,aK as b,aL as y,aM as f,aN as j,f as w,u as v,aO as N,aP as k,aQ as S,aR as C,H as q}from"./vendor-DfmD63KD.js";import{g as I,u as O}from"./index-gGBCxdYu.js";import{f as $,c as M,r as z,Q as T,a as Q,b as E}from"./QuizError-Mp16REcC.js";const A=({option:e,isSelected:t,isAnswered:n,isCorrectOption:o,handleSelect:l,disabled:c,showFeedback:d})=>{let x="bg-white/10 dark:bg-slate-900/40 border-white/20 dark:border-slate-700 text-white hover:bg-cyan-900/20 hover:border-cyan-400";return n?x=o?"border-green-500 bg-green-500/10 text-green-200":t?"border-red-500 bg-red-500/10 text-red-200":"border-white/10 dark:border-slate-700 opacity-60":t&&(x="border-cyan-400 bg-cyan-400/10 text-cyan-200"),a.jsxs(s.button,{layout:!0,className:`w-full flex items-center justify-between px-5 py-4 rounded-2xl border transition-all duration-200 text-base font-medium focus:outline-none focus:ring-2 focus:ring-cyan-400 ${x} shadow-sm`,onClick:()=>!c&&l(e.id),disabled:c,whileHover:c?{}:{scale:1.03},whileTap:c?{}:{scale:.98},children:[a.jsx("span",{children:e.text}),d&&n&&(o?a.jsx(r,{className:"w-5 h-5 text-green-400"}):t?a.jsx(i,{className:"w-5 h-5 text-red-400"}):null)]})},_=()=>a.jsx("div",{className:"w-full h-40 bg-gradient-to-br from-slate-700/40 to-slate-800/60 animate-pulse rounded-xl mb-6"}),L=({seconds:e})=>a.jsx("div",{className:"absolute top-0 right-0 mt-[-18px] mr-[-10px]",children:a.jsxs("div",{className:"flex items-center gap-1 px-3 py-1 rounded-full bg-cyan-500/90 shadow-lg text-white text-sm font-semibold border-2 border-white/30 backdrop-blur-md",children:[a.jsx(n,{className:"w-4 h-4 mr-1 opacity-80"}),a.jsxs("span",{children:[e,"s"]})]})}),P=({currentQuestion:t,selectedOption:n,isAnswered:l,handleOptionSelect:c,showExplanations:d=!1,timedOut:x=!1,progress:m=0,total:u=10,secondsLeft:g=null})=>{const[h,p]=e.useState(!1),b=e=>e?e.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/\n/g,"<br>"):"",y=n===t.correct_option_id;return a.jsx(s.div,{layout:!0,className:"relative mx-auto max-w-xl w-full p-0 sm:p-0",style:{zIndex:1},children:a.jsxs("div",{className:"backdrop-blur-xl bg-white/20 dark:bg-slate-900/70 border border-white/20 dark:border-slate-700 rounded-3xl shadow-2xl px-6 py-8 sm:px-10 sm:py-12 flex flex-col items-stretch",children:[a.jsxs("div",{className:"w-full mb-6 relative",children:[a.jsx("div",{className:"h-1.5 w-full bg-white/10 rounded-full overflow-hidden",children:a.jsx(s.div,{className:"h-full bg-gradient-to-r from-indigo-400 to-cyan-400 rounded-full",initial:{width:0},animate:{width:`${Math.round(m/u*100)}%`},transition:{duration:.5}})}),a.jsxs("div",{className:"text-xs text-white/70 mt-1 text-right",children:[m," / ",u]}),"number"==typeof g&&a.jsx(L,{seconds:g})]}),t.image_url&&a.jsxs("div",{className:"flex justify-center mb-6",children:[!h&&a.jsx(_,{}),a.jsx("img",{src:I(t.image_url,{width:600}),alt:"Question",className:"max-h-40 rounded-xl shadow-lg object-contain transition-opacity duration-300 "+(h?"opacity-100":"opacity-0"),onLoad:()=>p(!0),style:{display:h?"block":"none",minHeight:"160px"}})]}),a.jsx("div",{className:"mb-8",children:a.jsx("h2",{className:"text-xl sm:text-2xl font-bold text-white text-center leading-snug drop-shadow",dangerouslySetInnerHTML:{__html:b(t.question_text)}})}),a.jsx("div",{className:"flex flex-col gap-4 mb-4",children:t.options?.map(e=>a.jsx(A,{option:e,isSelected:n===e.id,isAnswered:l,isCorrectOption:e.id===t.correct_option_id,handleSelect:c,disabled:l,showFeedback:!0},e.id))}),a.jsx(o,{children:d&&l&&t.explanation&&a.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.4},className:"mt-6 px-5 py-4 rounded-2xl flex items-center gap-3 shadow bg-slate-900/80 border border-white/10",children:[y?a.jsx(r,{className:"w-7 h-7 text-green-400 shrink-0"}):a.jsx(i,{className:"w-7 h-7 text-red-400 shrink-0"}),a.jsxs("div",{children:[a.jsx("div",{className:"font-semibold mb-1 text-white text-lg",children:y?"Correct!":"Incorrect"}),a.jsx("div",{className:"text-white/80 text-base",dangerouslySetInnerHTML:{__html:b(t.explanation)}})]})]})})]})})},H=({timeLeft:e,formatTime:t,isLowTime:r=!1,progressPercentage:i=100,timerKey:d,initialTime:x=60,showProgressBar:m=!0,size:u="large"})=>{const g=e/x*100,h=g<=10?"critical":g<=25?"warning":g<=50?"caution":"normal",p=(()=>{const e={container:"relative flex items-center justify-center rounded-xl transition-all duration-300",text:"font-bold tabular-nums",progress:"absolute inset-0 rounded-xl transition-all duration-300"};return{...{small:{container:`${e.container} h-12 w-24 text-sm`,text:`${e.text} text-sm`,icon:"w-3 h-3",progress:e.progress},normal:{container:`${e.container} h-16 w-32 text-lg`,text:`${e.text} text-lg`,icon:"w-4 h-4",progress:e.progress},large:{container:`${e.container} h-20 w-40 text-xl`,text:`${e.text} text-xl`,icon:"w-5 h-5",progress:e.progress}}[u],...{normal:{container:"bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg",progress:"bg-gradient-to-r from-blue-400 to-blue-500",glow:""},caution:{container:"bg-gradient-to-r from-yellow-500 to-orange-500 text-white shadow-lg",progress:"bg-gradient-to-r from-yellow-400 to-orange-400",glow:"shadow-yellow-200"},warning:{container:"bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg",progress:"bg-gradient-to-r from-orange-400 to-red-400",glow:"shadow-orange-200 animate-pulse"},critical:{container:"bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg",progress:"bg-gradient-to-r from-red-400 to-red-500",glow:"shadow-red-200 animate-pulse"}}[h]}})();return a.jsxs("div",{className:"fixed top-4 left-1/2 transform -translate-x-1/2 z-30 flex flex-col items-center space-y-2",children:[a.jsxs(s.div,{variants:{normal:{scale:1,rotate:0},caution:{scale:1.02,rotate:0},warning:{scale:1.05,rotate:[-1,1,-1,0]},critical:{scale:1.1,rotate:[-2,2,-2,2,0]}},animate:h,transition:{duration:"critical"===h?.5:"warning"===h?.8:1,repeat:"critical"===h||"warning"===h?1/0:0,repeatType:"reverse"},className:`${p.container} ${p.glow} relative overflow-hidden`,children:[m&&a.jsx("div",{className:`${p.progress} opacity-30`,style:{width:`${Math.max(0,Math.min(100,g))}%`,transition:"width 1s linear"}}),a.jsxs("div",{className:"relative z-10 flex items-center space-x-2",children:[(()=>{switch(h){case"critical":return a.jsx(c,{className:`${p.icon} animate-bounce`});case"warning":return a.jsx(c,{className:`${p.icon} animate-pulse`});case"caution":return a.jsx(l,{className:p.icon});default:return a.jsx(n,{className:p.icon})}})(),a.jsx(s.span,{variants:{normal:{scale:1},caution:{scale:1},warning:{scale:[1,1.1,1]},critical:{scale:[1,1.2,1]}},animate:h,transition:{duration:.3,repeat:"critical"===h?1/0:0,repeatType:"reverse"},className:p.text,children:t?t(e):`${e}s`})]}),a.jsx(o,{children:"critical"===h&&a.jsx(s.div,{initial:{scale:1,opacity:.5},animate:{scale:1.5,opacity:0},exit:{scale:1,opacity:0},transition:{duration:1,repeat:1/0,ease:"easeOut"},className:"absolute inset-0 bg-red-400 rounded-xl"})})]},d),m&&a.jsx("div",{className:"w-full max-w-xs",children:a.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden",children:a.jsx(s.div,{className:"h-full rounded-full transition-all duration-1000 ease-linear "+("critical"===h?"bg-red-500":"warning"===h?"bg-orange-500":"caution"===h?"bg-yellow-500":"bg-blue-500"),style:{width:`${Math.max(0,Math.min(100,g))}%`},animate:{boxShadow:"critical"===h?["0 0 0 rgba(239, 68, 68, 0)","0 0 20px rgba(239, 68, 68, 0.6)","0 0 0 rgba(239, 68, 68, 0)"]:"warning"===h?["0 0 0 rgba(249, 115, 22, 0)","0 0 15px rgba(249, 115, 22, 0.4)","0 0 0 rgba(249, 115, 22, 0)"]:"none"},transition:{boxShadow:{duration:1,repeat:1/0,ease:"easeInOut"}}})})}),a.jsx(o,{children:("warning"===h||"critical"===h)&&a.jsx(s.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.3},className:"text-center text-xs font-medium px-3 py-1 rounded-full "+("critical"===h?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"),children:"critical"===h?"Time almost up!":"Running low on time"})}),a.jsx(o,{children:"normal"===h&&g>75&&a.jsx(s.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},transition:{duration:.3},className:"text-center text-xs font-medium text-green-600 dark:text-green-400",children:"Great pace! Keep it up 🚀"})})]})},G=({trigger:t,duration:r=3e3})=>{const[i,n]=e.useState(!1);e.useEffect(()=>{if(t){n(!0);const e=setTimeout(()=>n(!1),r);return()=>clearTimeout(e)}},[t,r]);const l=Array.from({length:50},(e,t)=>({id:t,left:100*Math.random(),delay:2*Math.random(),duration:2+2*Math.random(),color:["bg-red-500","bg-blue-500","bg-green-500","bg-yellow-500","bg-purple-500","bg-pink-500"][Math.floor(6*Math.random())]}));return a.jsx(o,{children:i&&a.jsx("div",{className:"fixed inset-0 pointer-events-none z-50 overflow-hidden",children:l.map(e=>a.jsx(s.div,{className:`absolute w-2 h-2 ${e.color} rounded-sm`,style:{left:`${e.left}%`,top:"-10px"},initial:{y:-20,rotate:0,opacity:1},animate:{y:window.innerHeight+20,rotate:360,opacity:0},transition:{duration:e.duration,delay:e.delay,ease:"easeIn"}},e.id))})})},R=({accuracy:e,performance:t})=>a.jsxs("div",{className:"text-center mb-8",children:[a.jsx(G,{trigger:e>=80,duration:4e3}),a.jsx(s.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"inline-flex items-center justify-center w-16 h-16 rounded-full text-white mb-4 "+(e>=80?"bg-gradient-to-r from-yellow-500 to-orange-500":"bg-purple-600"),children:e>=80?a.jsx(d,{className:"w-8 h-8"}):a.jsx(l,{className:"w-8 h-8"})}),a.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-2",children:e>=80?"🎉 Outstanding! 🎉":"Quick Quiz Complete!"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:e>=80?"You absolutely crushed it!":t.message}),e>=80&&a.jsx(s.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.8},className:"text-sm text-purple-600 dark:text-purple-400 mt-2",children:"🌟 You're ready for the real thing! 🌟"})]}),K=({score:e,questionCount:t,accuracy:r,performance:i})=>a.jsx(s.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.3},className:`${i.bgColor} rounded-2xl p-4 sm:p-8 mb-8 text-center`,children:a.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-center sm:space-x-6 space-y-4 sm:space-y-0",children:[a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:`text-4xl sm:text-6xl font-bold ${i.color} mb-1 sm:mb-2`,children:e}),a.jsxs("div",{className:"text-sm sm:text-base text-gray-600 dark:text-gray-300",children:["out of ",t]})]}),a.jsxs("div",{className:"text-center",children:[a.jsxs("div",{className:`text-4xl sm:text-6xl font-bold ${i.color} mb-1 sm:mb-2`,children:[r,"%"]}),a.jsx("div",{className:"text-sm sm:text-base text-gray-600 dark:text-gray-300",children:"accuracy"})]}),a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:`text-4xl sm:text-6xl font-bold ${i.color} mb-1 sm:mb-2`,children:i.grade}),a.jsx("div",{className:"text-sm sm:text-base text-gray-600 dark:text-gray-300",children:"grade"})]})]})}),B=({score:e,accuracy:t})=>a.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[a.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-4 text-center",children:[a.jsx(d,{className:"w-8 h-8 text-yellow-500 mx-auto mb-2"}),a.jsx("div",{className:"font-semibold text-gray-900 dark:text-white",children:e>=8?"Quiz Master!":e>=6?"Good Work!":"Keep Going!"})]}),a.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-4 text-center",children:[a.jsx(x,{className:"w-8 h-8 text-blue-500 mx-auto mb-2"}),a.jsx("div",{className:"font-semibold text-gray-900 dark:text-white",children:t>=80?"Sharpshooter":t>=60?"On Target":"Practice Makes Perfect"})]}),a.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-4 text-center",children:[a.jsx(n,{className:"w-8 h-8 text-green-500 mx-auto mb-2"}),a.jsx("div",{className:"font-semibold text-gray-900 dark:text-white",children:"Quick & Focused"})]})]}),D=({userAnswers:e})=>a.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 mb-8",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:"Detailed Review"}),a.jsx("div",{className:"space-y-6",children:e.map((e,t)=>a.jsx(s.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.6+.1*t},className:"border border-gray-200 dark:border-gray-700 rounded-xl p-4 sm:p-6",children:a.jsxs("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center "+(e.isCorrect?"bg-green-100 dark:bg-green-900/30":e.timedOut?"bg-orange-100 dark:bg-orange-900/30":"bg-red-100 dark:bg-red-900/30"),children:e.isCorrect?a.jsx(r,{className:"w-5 h-5 text-green-600"}):e.timedOut?a.jsx(n,{className:"w-5 h-5 text-orange-600"}):a.jsx(i,{className:"w-5 h-5 text-red-600"})}),a.jsxs("div",{className:"flex-1",children:[a.jsxs("div",{className:"flex items-center justify-between mb-3",children:[a.jsxs("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:["Question ",t+1]}),e.timedOut&&a.jsx("span",{className:"text-sm font-medium text-orange-600 dark:text-orange-400",children:"Time ran out"})]}),a.jsx("p",{className:"text-gray-700 dark:text-gray-300 mb-4",children:e.question.question_text}),a.jsx("div",{className:"space-y-2 mb-4",children:e.question.options.map(t=>{const s=t.id===e.selectedOptionId,r=t.id===e.question.correct_option_id;return a.jsx("div",{className:"p-3 rounded-lg border-2 "+(s&&r?"border-green-500 bg-green-50 dark:bg-green-900/20":s&&!r?"border-red-500 bg-red-50 dark:bg-red-900/20":r?"border-green-300 bg-green-50 dark:bg-green-900/10":"border-gray-200 dark:border-gray-600"),children:a.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-1 sm:space-y-0",children:[a.jsx("span",{className:"text-gray-900 dark:text-white",children:t.text}),a.jsxs("div",{className:"flex space-x-2",children:[s&&a.jsx("span",{className:"text-xs sm:text-sm font-medium text-blue-600",children:"Your Answer"}),r&&a.jsx("span",{className:"text-xs sm:text-sm font-medium text-green-600",children:"Correct"})]})]})},t.id)})}),e.question.explanation&&a.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[a.jsx("h4",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2",children:"Explanation"}),a.jsx("p",{className:"text-blue-800 dark:text-blue-200",children:e.question.explanation})]})]})]})},t))})]}),U={whileTap:{scale:.93},whileHover:{scale:1.04},transition:{type:"spring",stiffness:400,damping:20}},F=({accuracy:e,onRestart:t,onGoHome:r,handleShare:i,onButtonPress:n})=>a.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},className:"flex flex-col sm:flex-row gap-4 justify-center",children:[a.jsxs(s.button,{...U,onClick:e=>{n?.(),t(e)},className:"flex items-center justify-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-xl transition-colors",children:[a.jsx(m,{className:"w-5 h-5"}),a.jsx("span",{children:"Try Again"})]}),a.jsx(s.button,{...U,onClick:e=>{n?.(),r(e)},className:"flex items-center justify-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-xl transition-colors",children:a.jsx("span",{children:"Back to Home"})}),e>=80&&a.jsxs(s.button,{...U,onClick:e=>{n?.(),i(e)},className:"flex items-center justify-center space-x-2 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-semibold py-3 px-6 rounded-xl transition-colors",children:[a.jsx(u,{className:"w-5 h-5"}),a.jsx("span",{children:"Share Success!"})]})]});const W=["#06b6d4","#f59e42","#10b981","#f43f5e","#6366f1","#eab308"],Y=({score:e,questionCount:t,accuracy:r,userAnswers:i,quizSession:n,quizConfig:o,timeSpent:l,onRestart:c,onGoHome:d})=>{const x=r>=80?{grade:"A",color:"text-green-600",bgColor:"bg-green-100",message:"Excellent! USMLE ready!"}:r>=70?{grade:"B",color:"text-blue-600",bgColor:"bg-blue-100",message:"Good job! Keep it up!"}:r>=60?{grade:"C",color:"text-yellow-600",bgColor:"bg-yellow-100",message:"Not bad! More practice needed!"}:{grade:"D",color:"text-red-600",bgColor:"bg-red-100",message:"Keep practicing! You'll improve!"};l&&(Math.floor(l/60),(l%60).toString().padStart(2,"0"));const m=function(e){let t=0,a=0,s=0,r=0;return e.forEach(e=>{e.isCorrect?(s++,t=Math.max(t,s),r=0):(r++,a=Math.max(a,r),s=0)}),{maxCorrect:t,maxWrong:a}}(i),u=function(e){const t={};return e.forEach(e=>{const a=e.question.topic||e.question.system||e.question.subject||"Other";t[a]||(t[a]={topic:a,correct:0,total:0}),t[a].total++,e.isCorrect&&t[a].correct++}),Object.values(t)}(i),w=function(e){return e.map((e,t)=>({q:t+1,time:e.timeSpent||0,correct:e.isCorrect}))}(i);return a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-purple-900/20 dark:to-indigo-900/20",children:a.jsx("div",{className:"container mx-auto px-4 py-8",children:a.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"max-w-4xl mx-auto",children:[a.jsx(R,{accuracy:r,performance:x}),a.jsx(K,{score:e,questionCount:t,accuracy:r,performance:x}),a.jsx(B,{score:e,accuracy:r}),a.jsxs(s.div,{initial:{opacity:0,y:24},animate:{opacity:1,y:0},transition:{delay:.3},className:"bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 mb-8 shadow",children:[a.jsx("h3",{className:"text-xl font-bold mb-2",children:"Performance Analytics"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsxs("div",{children:[a.jsx("div",{className:"font-semibold mb-1",children:"Per-topic Breakdown"}),a.jsx(g,{width:"100%",height:180,children:a.jsxs(h,{data:u,margin:{left:-20,right:10},children:[a.jsx(p,{dataKey:"topic",fontSize:12,tick:{fill:"#64748b"},interval:0,angle:-15,dy:10}),a.jsx(b,{allowDecimals:!1,fontSize:12,tick:{fill:"#64748b"}}),a.jsx(y,{formatter:(e,t)=>"correct"===t?`${e} correct`:e}),a.jsx(f,{dataKey:"correct",fill:"#06b6d4",children:u.map((e,t)=>a.jsx(j,{fill:W[t%W.length]},e.topic))})]})})]}),a.jsxs("div",{children:[a.jsx("div",{className:"font-semibold mb-1",children:"Time per Question (s)"}),a.jsx(g,{width:"100%",height:180,children:a.jsxs(h,{data:w,margin:{left:-20,right:10},children:[a.jsx(p,{dataKey:"q",fontSize:12,tick:{fill:"#64748b"}}),a.jsx(b,{allowDecimals:!1,fontSize:12,tick:{fill:"#64748b"}}),a.jsx(y,{formatter:e=>`${e}s`}),a.jsx(f,{dataKey:"time",fill:"#6366f1",children:w.map((e,t)=>a.jsx(j,{fill:e.correct?"#10b981":"#f43f5e"},e.q))})]})})]})]}),a.jsxs("div",{className:"flex flex-wrap gap-6 mt-6",children:[a.jsxs("div",{className:"bg-cyan-50 dark:bg-cyan-900/20 rounded-xl px-4 py-2 font-semibold",children:["Longest Correct Streak: ",a.jsx("span",{className:"text-green-600",children:m.maxCorrect})]}),a.jsxs("div",{className:"bg-red-50 dark:bg-red-900/20 rounded-xl px-4 py-2 font-semibold",children:["Longest Incorrect Streak: ",a.jsx("span",{className:"text-red-600",children:m.maxWrong})]})]})]}),a.jsx(D,{userAnswers:i}),a.jsx(F,{accuracy:r,onRestart:c,onGoHome:d,handleShare:()=>{const a=`Just scored ${e}/${t} (${r}%) on a USMLE ${o?.quizMode||"Quick"} Quiz! 🎉`;navigator.share?navigator.share({title:"USMLE Quiz Results",text:a,url:window.location.origin}):(navigator.clipboard.writeText(`${a} Check it out at ${window.location.origin}`),alert("Results copied to clipboard!"))}})]})})})},J=()=>{const{state:r}=w(),i=v(),{user:n}=O(),{questions:l,currentIndex:c,currentQuestion:d,userAnswers:x,isAnswered:m,selectedOption:u,timedOut:g,loading:h,error:p,isComplete:b,timeLeft:y,handleOptionSelect:f,quizSession:j}=function({userId:t,categoryId:a="mixed",questionCount:s=10,difficulty:r=null,timePerQuestion:i=60}){const[n,o]=e.useState([]),[l,c]=e.useState(0),[d,x]=e.useState([]),[m,u]=e.useState(!1),[g,h]=e.useState(null),[p,b]=e.useState(!1),[y,f]=e.useState(!0),[j,w]=e.useState(null),[v,N]=e.useState(null),[k,S]=e.useState(!1),[C,q]=e.useState(i),I=e.useRef(null);e.useEffect(()=>{let e=!0;return f(!0),w(null),console.log("Fetching questions with params:",{categoryId:a,questionCount:s,difficulty:r}),$({userId:t,categoryId:a,questionCount:s,difficulty:r}).then(async s=>{if(e&&(console.log("Fetched questions:",s.length,s),o(s),s.length>0)){const r=await M({userId:t,sessionType:"quick",totalQuestions:s.length,categoryName:"mixed"!==a?a:null,timePerQuestion:i,autoAdvance:!0,showExplanations:!1,settings:{}});e&&N(r)}}).catch(t=>{e&&w(t)}).finally(()=>{e&&f(!1)}),()=>{e=!1}},[t,a,s,r,i]),e.useEffect(()=>{if(!(m||k||y))return I.current=setInterval(()=>{q(e=>e<=1?(clearInterval(I.current),b(!0),u(!0),0):e-1)},1e3),()=>clearInterval(I.current)},[m,k,y]);const O=e.useCallback(e=>{if(m||p||k)return;h(e),u(!0),clearInterval(I.current);const a=n[l],s=e===a.correct_option_id,r=i-C,o={userId:t,questionId:a.id,selectedOptionId:e,isCorrect:s,timeSpent:r,responseOrder:l+1,question:a,timedOut:!1};x(e=>[...e,o]),v&&z(v.id).catch(()=>{})},[m,p,k,n,l,C,i,v]);e.useEffect(()=>{if(p&&!m&&!k){const e=n[l],a={userId:t,questionId:e.id,selectedOptionId:null,isCorrect:!1,timeSpent:i,responseOrder:l+1,question:e,timedOut:!0};x(e=>[...e,a]),u(!0),v&&z(v.id).catch(()=>{})}},[p,m,k,n,l,i,v]);const T=e.useCallback(()=>{u(!1),h(null),b(!1),q(i),c(e=>e+1)},[i]),Q=e.useCallback(()=>{S(!0),clearInterval(I.current)},[]);return e.useEffect(()=>{if(m&&!k){const e=l===n.length-1,t=setTimeout(()=>{e?Q():T()},p?1e3:500);return()=>clearTimeout(t)}},[m,k,l,n.length,Q,T,p]),{questions:n,currentIndex:l,currentQuestion:n[l],userAnswers:d,isAnswered:m,selectedOption:g,timedOut:p,loading:y,error:j,isComplete:k,timeLeft:C,handleOptionSelect:O,moveToNextQuestion:T,completeQuiz:Q,quizSession:v}}({userId:n?.id,categoryId:r?.categoryId||"mixed",questionCount:r?.questionCount||10,difficulty:r?.difficulty||null,timePerQuestion:60}),[I,A]=N.useState(()=>"true"===localStorage.getItem("quizMuted")),{playCorrect:_,playWrong:L,playTimesUp:G,playComplete:R}=function(e){const[a]=t("/assets/correct-nn-w_bNA.mp3",{volume:.5,soundEnabled:!e}),[s]=t("/assets/wrong-DOiMQ82m.wav",{volume:.5,soundEnabled:!e}),[r]=t("/assets/timesup-D7XQLLVA.wav",{volume:.5,soundEnabled:!e}),[i]=t("/assets/next-CVDITkpr.ogg",{volume:.5,soundEnabled:!e}),[n]=t("/assets/completed-D5Ikm1fC.wav",{volume:.5,soundEnabled:!e});return{playCorrect:a,playWrong:s,playTimesUp:r,playNext:i,playComplete:n}}(I);N.useEffect(()=>{m&&!g&&(u===d?.correct_option_id?_():L()),g&&G()},[m,g,u,d,_,L,G]),N.useEffect(()=>{b&&R()},[b,R]),l.length;const K=()=>i(-1),B=()=>i("/"),D=x.filter(e=>e.isCorrect).length,U=Math.round(D/Math.max(1,x.length)*100),F=x.reduce((e,t)=>e+t.timeSpent,0);return h?a.jsx(T,{message:""}):p&&0===l.length?a.jsx(Q,{error:p,onRetry:()=>window.location.reload(),onGoBack:K}):0===l.length?a.jsx(Q,{error:{code:"NO_QUESTIONS",message:"No questions available."},onRetry:()=>window.location.reload(),onGoBack:K}):b?a.jsx(Y,{score:D,questionCount:l.length,accuracy:U,userAnswers:x,quizSession:j,quizConfig:{...r,quizMode:"quick"},timeSpent:F,onRestart:()=>{i("/quick-quiz",{state:r,replace:!0}),window.location.reload()},onGoHome:B}):a.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900/20",children:[a.jsx("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:a.jsxs("div",{className:"container mx-auto px-4 py-3 flex items-center justify-between",children:[a.jsx("button",{onClick:K,className:"text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 p-2 rounded",children:a.jsx(k,{className:"w-5 h-5"})}),a.jsx("h1",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Quick Quiz"}),a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx("button",{onClick:()=>{A(e=>(localStorage.setItem("quizMuted",!e),!e))},className:"p-2 rounded",title:I?"Unmute":"Mute",children:I?a.jsx(S,{className:"w-5 h-5"}):a.jsx(C,{className:"w-5 h-5"})}),a.jsx("button",{onClick:B,className:"text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 p-2 rounded",children:a.jsx(q,{className:"w-5 h-5"})})]})]})}),a.jsxs("div",{className:"container mx-auto px-4 pt-8 pb-2 flex flex-col items-center",children:[a.jsx(H,{timeLeft:y,formatTime:e=>`${e}s`,isLowTime:y<=15,progressPercentage:y/60*100,timerKey:c,initialTime:60,showProgressBar:!1,size:"small"}),a.jsx(E,{current:c+1,total:l.length,progress:(c+1)/l.length,showDetailed:!1,size:"small"})]}),a.jsx("div",{className:"container mx-auto px-4 pb-8",children:a.jsx(o,{mode:"wait",children:d?a.jsx(s.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},exit:{opacity:0,x:-50},transition:{duration:.3},children:a.jsx(P,{currentQuestion:d,isAnswered:m,selectedOption:u,handleOptionSelect:f,showExplanations:!1,timedOut:g,secondsLeft:y},d.id)},d.id):!h&&a.jsxs("div",{className:"text-center text-gray-600 dark:text-gray-400 py-10",children:[a.jsx("p",{children:"No question available to display. Please try again or select a different category."}),a.jsx("button",{onClick:()=>i(-1),className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Go Back"})]})})})]})};export{J as default};
