import{l as e,b as s,s as t}from"./index-gGBCxdYu.js";import{j as r,y as a,z as n,av as i,m as o,l as c,aV as l,aW as d,u,T as m,R as g,aw as x,H as h,D as p,ay as f,aX as y}from"./vendor-DfmD63KD.js";async function N({categoryId:e="mixed",questionCount:s=10,difficulty:r=null}){let a=t.from("questions").select("*").eq("is_active",!0);e&&"mixed"!==e&&(a=a.contains("question_tags",[{id:e}])),r&&(a=a.eq("difficulty",r)),a=a.limit(s);const n=performance.now(),{data:i,error:o}=await a,c=performance.now()-n;if(o)throw console.error("❌ [QuizService] Error fetching questions:",{message:o.message,details:o.details,categoryId:e,questionCount:s,difficulty:r,duration:`${c.toFixed(2)}ms`}),new Error(`FETCH_QUESTIONS_ERROR: ${o.message}`);return console.log("✅ [QuizService] Successfully fetched questions:",{count:i.length,categoryId:e,difficulty:r,duration:`${c.toFixed(2)}ms`}),i}async function b({userId:r,categoryId:a="mixed",questionCount:n=10,difficulty:i=null}){return e.setContext({userId:r,operation:"fetchQuestionsForUser"}),r?(e.info("Starting smart question fetch for user (using RPC)",{userId:r,categoryId:a,questionCount:n,difficulty:i}),await s("fetchQuestionsForUser",async()=>{try{const{data:s,error:o}=await t.rpc("get_unseen_questions",{p_user_id:r,p_category_id:"mixed"===a?null:a,p_difficulty:i,p_limit:2*n});if(o)return e.error("Error fetching unseen questions via RPC",{error:o}),e.warn("Falling back to basic fetch due to RPC error",{error:o.message}),console.error("[RPC ERROR] get_unseen_questions:",o),N({categoryId:a,questionCount:n,difficulty:i});if(!s||0===s.length)return e.warn("No unseen questions found, falling back to basic fetch"),N({categoryId:a,questionCount:n,difficulty:i});const c=function(e){let s,t=e.length;for(;t>0;)s=Math.floor(Math.random()*t),t--,[e[t],e[s]]=[e[s],e[t]];return e}([...s]).slice(0,n);return e.success("Successfully fetched smart questions for user",{totalUnseen:s.length,selected:c.length,categoryId:a,difficulty:i}),c}catch(s){return e.error("Exception in fetchQuestionsForUser, falling back to basic fetch",{error:s.message,stack:s.stack}),N({categoryId:a,questionCount:n,difficulty:i})}})):(e.warn("No userId provided, falling back to basic fetchQuestions",{categoryId:a,questionCount:n,difficulty:i}),N({categoryId:a,questionCount:n,difficulty:i}))}const w={quick:"quick",timed:"timed",custom:"self_paced",block:"block",self_paced:"self_paced",learn_module:"learn_module"};async function j(s){const{userId:r,sessionType:a="self_paced",totalQuestions:n,categoryName:i=null,timePerQuestion:o=null,autoAdvance:c=!1,showExplanations:l=!0,settings:d={}}=s;e.info("Creating quiz session",{userId:r,sessionType:a,totalQuestions:n,categoryName:i,timePerQuestion:o,autoAdvance:c,showExplanations:l});const u=w[a]||"self_paced",m={user_id:r,session_type:u,total_questions:n,started_at:(new Date).toISOString(),category_name:i,settings:{timePerQuestion:o,autoAdvance:c,showExplanations:l,...d}},{data:g,error:x}=await t.from("quiz_sessions").insert(m).select().single();if(x)throw e.error("Error creating quiz session",{error:x,sessionData:m}),new Error(`CREATE_SESSION_ERROR: ${x.message}`);return e.success("Quiz session created successfully",{sessionId:g.id,sessionType:u,totalQuestions:n}),g}async function _(s,r){const{correctAnswers:a,totalTimeSeconds:n,pointsEarned:i=0,completed:o=!0}=r;e.info("Completing quiz session",{sessionId:s,correctAnswers:a,totalTimeSeconds:n,pointsEarned:i,completed:o});const c={correct_answers:a,completed_at:(new Date).toISOString(),total_time_seconds:n,points_earned:i,is_completed:o},{data:l,error:d}=await t.from("quiz_sessions").update(c).eq("id",s).select().single();if(d)throw e.error("Error completing quiz session",{error:d,sessionId:s,updateData:c}),new Error(`COMPLETE_SESSION_ERROR: ${d.message}`);return e.success("Quiz session completed successfully",{sessionId:l.id,correctAnswers:a,totalTimeSeconds:n,pointsEarned:i}),l}async function R(s){const{sessionId:r,questionId:a,selectedOptionId:n,isCorrect:i,timeSpent:o,responseOrder:c,userId:l}=s;e.info("Recording quiz response",{sessionId:r,questionId:a,selectedOptionId:n,isCorrect:i,timeSpent:o,responseOrder:c,userId:l});try{const{data:d,error:u}=await t.from("quiz_responses").insert({session_id:r,question_id:a,selected_option_id:n,is_correct:i,time_spent_seconds:o,response_order:c}).select().single();if(u)throw e.error("Error recording quiz response",{error:u,responseData:s}),new Error(`RECORD_RESPONSE_ERROR: ${u.message}`);return l&&a&&async function({userId:s,questionId:r,answeredCorrectly:a}){e.info("Recording question interaction via RPC",{userId:s,questionId:r,answeredCorrectly:a});try{const{data:n,error:i}=await t.rpc("record_question_interaction",{p_user_id:s,p_question_id:r,p_answered_correctly:a});if(i)throw e.error("Error recording question interaction via RPC",{error:i,userId:s,questionId:r,answeredCorrectly:a}),new Error(`RECORD_INTERACTION_ERROR: ${i.message}`);return e.success("Question interaction recorded successfully",{userId:s,questionId:r,answeredCorrectly:a,result:n}),n}catch(n){throw e.error("Exception in recordQuestionInteraction",{error:n.message,userId:s,questionId:r,answeredCorrectly:a}),n}}({userId:l,questionId:a,answeredCorrectly:i}).catch(s=>{e.warn("Failed to update user question history (non-critical)",{error:s.message,userId:l,questionId:a,isCorrect:i})}),e.success("Quiz response recorded successfully",{responseId:d.id,questionId:a,isCorrect:i,timeSpent:o}),d}catch(d){throw e.error("Exception in recordQuizResponse",{error:d.message,stack:d.stack,responseData:s}),d}}const q=({current:e,total:s,progress:t,score:d=0,accuracy:u=0,averageTime:m=0,showDetailed:g=!0,size:x="normal"})=>{const h=Math.max(0,Math.min(100,e/s*100)),p=Math.max(0,Math.min(100,u)),f=s-e,y=()=>p>=80?"text-green-600 dark:text-green-400":p>=60?"text-blue-600 dark:text-blue-400":p>=40?"text-yellow-600 dark:text-yellow-400":"text-red-600 dark:text-red-400",N=(()=>{switch(x){case"compact":return{container:"py-2",progressHeight:"h-2",text:"text-sm",icons:"w-4 h-4",spacing:"space-y-1"};case"detailed":return{container:"py-6",progressHeight:"h-4",text:"text-base",icons:"w-5 h-5",spacing:"space-y-4"};default:return{container:"py-4",progressHeight:"h-3",text:"text-sm",icons:"w-4 h-4",spacing:"space-y-2"}}})();return r.jsxs("div",{className:`bg-white dark:bg-gray-800 rounded-xl shadow-lg p-4 ${N.container}`,children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("h3",{className:`font-semibold text-gray-900 dark:text-white ${N.text}`,children:"Progress"}),r.jsxs("span",{className:`font-bold ${N.text} text-blue-600 dark:text-blue-400`,children:[e," / ",s]})]}),g&&r.jsxs("div",{className:"flex items-center space-x-4 text-sm",children:[r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(a,{className:"w-4 h-4 text-purple-500"}),r.jsxs("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:["Score: ",r.jsx("span",{className:"text-purple-600 dark:text-purple-400",children:d})]})]}),u>0&&r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(n,{className:"w-4 h-4 text-green-500"}),r.jsxs("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:["Accuracy: ",r.jsxs("span",{className:y(),children:[Math.round(p),"%"]})]})]}),m>0&&r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(i,{className:"w-4 h-4 text-orange-500"}),r.jsxs("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:["Avg: ",r.jsxs("span",{className:"text-orange-600 dark:text-orange-400",children:[Math.round(m),"s"]})]})]})]})]}),r.jsxs("div",{className:`relative bg-gray-200 dark:bg-gray-700 rounded-full ${N.progressHeight} overflow-hidden mb-4`,children:[r.jsxs(o.div,{className:`${N.progressHeight} bg-gradient-to-r ${p>=80?"from-green-500 to-emerald-600":p>=60?"from-blue-500 to-blue-600":p>=40?"from-yellow-500 to-orange-500":"from-orange-500 to-red-500"} rounded-full relative`,initial:{width:0},animate:{width:`${h}%`},transition:{duration:.8,ease:"easeOut"},children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"}),h>15&&r.jsx("div",{className:"absolute inset-0 flex items-center justify-end pr-2",children:r.jsxs("span",{className:"text-xs font-bold text-white drop-shadow-sm",children:[Math.round(h),"%"]})})]}),r.jsx(o.div,{className:"absolute top-0 h-full w-1 bg-white shadow-lg",initial:{left:0},animate:{left:`${h}%`},transition:{duration:.8,ease:"easeOut"},style:{transform:"translateX(-50%)"}})]}),s<=20&&"compact"!==x&&r.jsx("div",{className:"mb-4",children:r.jsx("div",{className:"flex items-center justify-center space-x-2 flex-wrap gap-1 pb-6",children:Array.from({length:s},(s,t)=>{const a=t+1,n=a<e,i=a===e;return r.jsxs(o.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.05*t,duration:.3},className:"relative",children:[n?r.jsx(o.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.1},className:"flex items-center justify-center",children:r.jsx(c,{className:`${N.icons} text-green-500`})}):i?r.jsx(o.div,{animate:{scale:[1,1.2,1],rotate:[0,180,360]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},className:"flex items-center justify-center",children:r.jsx(l,{className:`${N.icons} text-blue-500 fill-current`})}):r.jsx(l,{className:`${N.icons} text-gray-300 dark:text-gray-600`}),r.jsx("div",{className:"absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 dark:text-gray-400",children:a})]},t)})})}),g&&"detailed"===x&&r.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[r.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3",children:[r.jsx("div",{className:"text-lg font-bold text-blue-600 dark:text-blue-400",children:f}),r.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Remaining"})]}),r.jsxs("div",{className:"bg-green-50 dark:bg-green-900/20 rounded-lg p-3",children:[r.jsx("div",{className:"text-lg font-bold text-green-600 dark:text-green-400",children:d}),r.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Correct"})]}),r.jsxs("div",{className:"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3",children:[r.jsxs("div",{className:`text-lg font-bold ${y()}`,children:[Math.round(p),"%"]}),r.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Accuracy"})]}),r.jsxs("div",{className:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3",children:[r.jsx("div",{className:"text-lg font-bold text-orange-600 dark:text-orange-400",children:m>0?`${Math.round(m)}s`:"--"}),r.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Avg Time"})]})]}),g&&"compact"!==x&&r.jsx("div",{className:"mt-4 text-center",children:r.jsx(o.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:1},className:"text-sm text-gray-600 dark:text-gray-400",children:h>=75?r.jsx("span",{className:"text-green-600 dark:text-green-400 font-medium",children:"🎉 Almost there! You're doing great!"}):h>=50?r.jsx("span",{className:"text-blue-600 dark:text-blue-400 font-medium",children:"🚀 Halfway done! Keep up the momentum!"}):h>=25?r.jsx("span",{className:"text-purple-600 dark:text-purple-400 font-medium",children:"💪 Good progress! Stay focused!"}):r.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"🌟 Just getting started! You've got this!"})})})]})},E=({isFetching:e})=>r.jsxs("div",{className:"flex flex-col justify-center items-center h-screen",children:[r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"}),r.jsx("p",{className:"text-gray-600",children:"Loading quiz questions..."}),e&&r.jsxs("p",{className:"text-sm text-gray-500",children:["Fetching latest questions ",r.jsx(d,{size:16,className:"inline-block animate-pulse"})]})]}),I=e=>e?"NO_QUESTIONS"===e.code?e.message||"No questions found for your selected category or difficulty.":"NO_CONFIG"===e.code?"Quiz configuration is missing. Please try starting the quiz again.":"FETCH_ERROR"===e.code?e.message||"Failed to load questions from the database.":e.message?.includes("FETCH_QUESTIONS_ERROR")?"Failed to fetch quiz questions. Please try again or adjust your filters.":e.message?.includes("SESSION_CREATION_ERROR")?"Failed to create a quiz session. Please try starting the quiz again.":e.message?.includes("RESPONSE_RECORDING_ERROR")?"Failed to record your response. Please continue to the next question.":e.message?.includes("SESSION_COMPLETION_ERROR")?"Failed to complete the quiz session. Your progress may not be saved.":"ERR_NETWORK"===e.code||e.message?.toLowerCase().includes("network")?"Network error: Please check your internet connection and try again.":"ERR_SERVER"===e.code||500===e.status?"Server error: Please try again later.":406===e.status?"Not Acceptable: The server could not process your request. Please contact support if this persists.":401===e.status||403===e.status?"You are not authorized to access this quiz. Please log in or check your permissions.":e.message||"There was a problem loading the quiz questions.":"There was a problem loading the quiz questions.",k=({error:e,onRetry:s,categoryId:t,questionCount:a,getOfflineData:n,showOfflineOption:i=!0})=>{const o=u(),c=i?n?.(t,a):null,l=(e=>"NO_QUESTIONS"===e.code?p:"NO_CONFIG"===e.code?f:"FETCH_ERROR"===e.code?m:y)(e),d=((e,s,t)=>{const r=[];return"NO_QUESTIONS"===e.code?(r.push("Try selecting a different difficulty level"),r.push("Choose a different category"),r.push("Use mixed categories for more options"),t>10&&r.push(`Try with fewer questions (${Math.max(5,t-5)})`)):"FETCH_ERROR"===e.code||e.message?.includes("FETCH_QUESTIONS_ERROR")?(r.push("Check your internet connection"),r.push("Try again in a few moments"),r.push("Contact support if the problem persists")):"NO_CONFIG"===e.code||e.message?.includes("SESSION_CREATION_ERROR")?(r.push("Go back to quiz selection"),r.push("Try starting the quiz again")):e.message?.includes("RESPONSE_RECORDING_ERROR")||e.message?.includes("SESSION_COMPLETION_ERROR")?(r.push("Continue with the quiz if possible"),r.push("Your progress may not be saved"),r.push("Contact support if issues persist")):(r.push("Try refreshing the page"),r.push("Check your internet connection"),r.push("Go back to quiz selection")),r})(e,0,a);return r.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4",children:r.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 max-w-lg w-full text-center",children:[r.jsx("div",{className:"p-3 bg-red-100 dark:bg-red-900/30 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center",children:r.jsx(l,{className:"w-8 h-8 text-red-600"})}),r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Unable to Load Quiz"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:I(e)}),e&&(e.code||e.details)&&r.jsxs("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6 text-left",children:[r.jsxs("h4",{className:"font-medium text-yellow-800 dark:text-yellow-200 mb-2 flex items-center",children:[r.jsx(m,{className:"w-4 h-4 mr-2"}),"What went wrong:"]}),r.jsx("ul",{className:"text-sm text-yellow-700 dark:text-yellow-300 space-y-1",children:d.map((e,s)=>r.jsxs("li",{className:"flex items-start",children:[r.jsx("span",{className:"text-yellow-500 mr-2",children:"•"}),e]},s))})]}),r.jsxs("div",{className:"space-y-3",children:[s&&r.jsxs("button",{onClick:s,className:"w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center space-x-2",children:[r.jsx(g,{className:"w-4 h-4"}),r.jsx("span",{children:"Try Again"})]}),c&&c.length>0&&r.jsxs("button",{onClick:()=>window.location.reload(),className:"w-full bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors duration-200 flex items-center justify-center space-x-2",children:[r.jsx(x,{className:"w-4 h-4"}),r.jsxs("span",{children:["Use Offline Questions (",c.length,")"]})]}),r.jsxs("button",{onClick:()=>{o("/quick-quiz",{state:{categoryId:"mixed",categoryName:"Mixed Categories",questionCount:Math.min(a||10,10),quizType:"quick"}})},className:"w-full bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors duration-200 flex items-center justify-center space-x-2",children:[r.jsx(x,{className:"w-4 h-4"}),r.jsx("span",{children:"Try Mixed Quiz"})]}),r.jsx("button",{onClick:()=>o("/quiz"),className:"w-full bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors duration-200",children:"Back to Categories"}),r.jsxs("button",{onClick:()=>o("/"),className:"w-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200 flex items-center justify-center space-x-2",children:[r.jsx(h,{className:"w-4 h-4"}),r.jsx("span",{children:"Go Home"})]})]}),!1]})})};export{E as Q,k as a,q as b,j as c,_ as d,b as f,R as r};
