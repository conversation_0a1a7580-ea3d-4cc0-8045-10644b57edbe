import{v as e,r as a,x as r,k as t,B as s,y as i,z as o,j as l,E as n,X as c,F as d,J as g,m as u,K as m,O as y,i as x,P as b,V as h,W as p,Y as f,Z as v,$ as j,a0 as w,a1 as k,a2 as N,a3 as C,a4 as q,a5 as S,a6 as P,a7 as M,a8 as A,a9 as T,aa as I,ab as Q,ac as E,ad as z,ae as H,af as D,ag as $,ah as B,ai as L,aj as F,ak as G,al as R,am as _,an as O,ao as U,ap as Y,aq as V,ar as W,as as K,at as Z,au as J,av as X,aw as ee,ax as ae,ay as re,u as te,C as se}from"./vendor-DfmD63KD.js";import{a as ie,l as oe,s as le,u as ne}from"./index-gGBCxdYu.js";import{q as ce,a as de,u as ge}from"./useViewTransitions-2OP18fwt.js";const ue=({filters:e,onFiltersChange:y})=>{const[x,b]=a.useState(!1),h=[{value:"all",label:"All Categories",icon:r},{value:"system",label:"Body Systems",icon:t},{value:"subject",label:"Medical Subjects",icon:s},{value:"topic",label:"Specific Topics",icon:i},{value:"high-yield",label:"High-Yield (50+ Questions)",icon:o}],p=a=>{y({...e,selectedFilter:a}),b(!1)},f=a=>{y({...e,viewMode:a})},v=()=>{y({...e,search:""})},j=h.find(a=>a.value===e.selectedFilter);return l.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-4 mb-6 shadow-lg border border-gray-200 dark:border-gray-700",children:[l.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4",children:[l.jsxs("div",{className:"relative flex-1 max-w-md",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(n,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{type:"text",value:e.search,onChange:a=>{y({...e,search:a.target.value})},placeholder:"Search categories...",className:"block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"}),e.search&&l.jsx("button",{onClick:v,className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:l.jsx(c,{className:"h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"})})]}),l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsxs("div",{className:"relative",children:[l.jsxs("button",{onClick:()=>b(!x),className:"flex items-center space-x-2 px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors",children:[l.jsx(d,{className:"w-4 h-4"}),l.jsx("span",{className:"hidden sm:inline",children:j?.label||"Filter"}),l.jsx(g,{className:"w-4 h-4 transition-transform "+(x?"rotate-180":"")})]}),x&&l.jsx(u.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"absolute top-full mt-2 right-0 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10",children:h.map(a=>l.jsxs("button",{onClick:()=>p(a.value),className:"w-full text-left px-4 py-3 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors first:rounded-t-lg last:rounded-b-lg flex items-center space-x-2 "+(e.selectedFilter===a.value?"bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400":"text-gray-700 dark:text-gray-200"),children:[l.jsx(a.icon,{className:"w-4 h-4"}),l.jsx("span",{children:a.label})]},a.value))})]}),l.jsxs("div",{className:"flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1",children:[l.jsx("button",{onClick:()=>f("grid"),className:"p-2 rounded-md transition-colors "+("grid"===e.viewMode?"bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"),title:"Grid View",children:l.jsx(r,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>f("list"),className:"p-2 rounded-md transition-colors "+("list"===e.viewMode?"bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"),title:"List View",children:l.jsx(m,{className:"w-4 h-4"})})]})]})]}),(e.search||"all"!==e.selectedFilter)&&l.jsxs("div",{className:"mt-4 flex flex-wrap items-center gap-2",children:[l.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Active filters:"}),e.search&&l.jsxs("span",{className:"inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs font-medium rounded-full",children:['Search: "',e.search,'"',l.jsx("button",{onClick:v,className:"ml-1 hover:text-blue-600 dark:hover:text-blue-300",children:l.jsx(c,{className:"w-3 h-3"})})]}),"all"!==e.selectedFilter&&l.jsxs("span",{className:"inline-flex items-center px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-xs font-medium rounded-full",children:[j?.label,l.jsx("button",{onClick:()=>p("all"),className:"ml-1 hover:text-green-600 dark:hover:text-green-300",children:l.jsx(c,{className:"w-3 h-3"})})]})]})]})},me=e=>{if(!e)return"Never";try{const a=new Date(e);if(isNaN(a.getTime()))return"Never";const r=new Date,t=Math.abs(r-a),s=Math.ceil(t/864e5);return 1===s?"Yesterday":s<=7?`${s} days ago`:s<=30?`${Math.ceil(s/7)} weeks ago`:s<=365?`${Math.ceil(s/30)} months ago`:`${Math.ceil(s/365)} years ago`}catch(a){return console.warn("Error formatting last used date:",a),"Never"}},ye={"Cardiovascular System":t,Cardiology:t,"Myocardial Infarction":t,Heart:t,"Pulmonary & Critical Care":$,"Respiratory System":$,Pulmonology:$,Asthma:$,Pneumonia:$,COPD:$,"Nervous System":D,Neurology:D,Stroke:D,"Ischemic Stroke":D,"Hemorrhagic Stroke":D,"Behavioral science":D,Psychiatry:D,"Psychiatric/Behavioral & Substance Use Disorder":D,"Gastrointestinal & Nutrition":H,"Gastrointestinal System":H,Gastroenterology:H,"Peptic Ulcer Disease":H,GERD:H,"Renal, Urinary Systems & Electrolytes":z,"Renal System":z,Urology:z,"Acute Kidney Injury":z,Glomerulonephritis:z,"Endocrine, Diabetes & Metabolism":E,Endocrinology:E,"Hematology & Oncology":Q,Hematology:Q,Oncology:Q,"Infectious Diseases":I,"Infectious disease":I,Microbiology:T,"Microbiology (General Principles)":T,Pharmacology:A,Drugs:A,Medications:A,Anatomy:k,Physiology:b,Pathophysiology:b,"Laboratory Medicine":M,Diagnostics:P,Radiology:P,Imaging:P,"Emergency Medicine":S,Trauma:S,"Critical Care":S,"Internal Medicine":x,"Family Medicine":q,"General Practice":q,"Reproductive System":N,Obstetrics:C,Gynecology:N,"Musculoskeletal System":k,Orthopedics:k,Rheumatology:k,Genetics:w,"Molecular Biology":w,Biochemistry:j,"Integumentary System":y,Dermatology:y,Ophthalmology:v,Hepatology:f,"Pancreatic Disorders":p,"Spinal Disorders":h,Mixed:b,General:x,Other:y},xe={"Allergy & Immunology":"bg-rose-500",Dermatology:"bg-pink-500","Cardiovascular System":"bg-red-500","Pulmonary & Critical Care":"bg-red-600","Gastrointestinal & Nutrition":"bg-orange-500","Hematology & Oncology":"bg-red-700","Renal, Urinary Systems & Electrolytes":"bg-amber-500","Nervous System":"bg-purple-500","Rheumatology/Orthopedics & Sports":"bg-stone-500","Infectious Diseases":"bg-red-400","Endocrine, Diabetes & Metabolism":"bg-orange-600","Female Reproductive System & Breast":"bg-pink-600","Male Reproductive System":"bg-blue-600","Pregnancy, Childbirth & Puerperium":"bg-pink-400","Biostatistics & Epidemiology":"bg-slate-500","Ear, Nose & Throat (ENT)":"bg-teal-500","Psychiatric/Behavioral & Substance Use Disorder":"bg-indigo-500","Poisoning & Environmental Exposure":"bg-yellow-600",Ophthalmology:"bg-cyan-500","Social Sciences (Ethics/Legal/Professional)":"bg-gray-500","Miscellaneous (Multisystem)":"bg-neutral-500","Biochemistry (General Principles)":"bg-emerald-600","Genetics (General Principles)":"bg-violet-600","Microbiology (General Principles)":"bg-indigo-600","Pathology (General Principles)":"bg-red-800","Pharmacology (General Principles)":"bg-teal-600",Anatomy:"bg-blue-500","Behavioral science":"bg-indigo-500",Biochemistry:"bg-emerald-500",Biostatistics:"bg-slate-500",Embryology:"bg-sky-500",Genetics:"bg-violet-500",Histology:"bg-blue-600",Immunology:"bg-green-500",Microbiology:"bg-indigo-500",Pathology:"bg-red-500",Pathophysiology:"bg-orange-500",Pharmacology:"bg-teal-500",Physiology:"bg-green-600","Aortic and peripheral artery diseases":"bg-red-400","Congenital heart disease":"bg-red-300","Coronary heart disease":"bg-red-600","Valvular heart diseases":"bg-rose-500","Myopericardial diseases":"bg-red-500","Cardiac arrhythmias":"bg-red-700","Cardiovascular drugs":"bg-red-400",Hypertension:"bg-red-800","Heart failure and shock":"bg-red-900","Pulmonary infections":"bg-blue-400","Obstructive lung disease":"bg-blue-500","Pulmonary vascular disease":"bg-blue-600","Critical care medicine":"bg-blue-700","Lung cancer":"bg-blue-800","Interstitial lung disease":"bg-sky-500","Sleep disorders":"bg-indigo-400","Cerebrovascular disease":"bg-purple-400","Neurodegenerative disorders and dementias":"bg-purple-500","Spinal cord disorders":"bg-purple-600","Disorders of peripheral nerves and muscles":"bg-purple-300","CNS infections":"bg-purple-700","Seizures and epilepsy":"bg-purple-800","Traumatic brain injuries":"bg-purple-900",Headache:"bg-violet-400","Tumors of the nervous system":"bg-purple-600",Anesthesia:"bg-indigo-600","Demyelinating diseases":"bg-purple-500","Hepatic disorders":"bg-green-500","Biliary tract disorders":"bg-green-600","Tumors of the GI tract":"bg-orange-600","Intestinal and colorectal disorders":"bg-green-400","Gastroesophageal disorders":"bg-orange-400","Pancreatic disorders":"bg-orange-500","Disorders of nutrition":"bg-lime-500","Obesity and dyslipidemia":"bg-yellow-500","Hypothalamus and pituitary disorders":"bg-orange-400","Reproductive endocrinology":"bg-orange-500","Endocrine tumors":"bg-orange-600","Adrenal disorders":"bg-orange-700","Diabetes mellitus":"bg-amber-500","Thyroid disorders":"bg-yellow-600","Viral infections":"bg-red-400","HIV and sexually transmitted infections":"bg-red-500","Parasitic and helminthic infections":"bg-red-300","Fungal infections":"bg-orange-400","Bacterial infections":"bg-red-600","Antimicrobial drugs":"bg-teal-400","Infection control":"bg-blue-400","White blood cell disorders":"bg-red-500","Platelet disorders":"bg-red-600","Red blood cell disorders":"bg-red-700","Hemostasis and thrombosis":"bg-red-800","Principles of oncology":"bg-red-900","Transfusion medicine":"bg-rose-600","Plasma cell disorders":"bg-red-400","system-default":"bg-red-500","subject-default":"bg-blue-500","topic-default":"bg-green-500",default:"bg-gray-500"},be=(e,a,r={})=>{if(ye[e])return e=>l.jsx("medicalIcon",{...r,...e});if(a){const e={BookOpen:"BookOpenText",FAMicroscope:"Microscope",Shield:"Shield"}[a]||a,s={Brain:D,Heart:t,Activity:b,Microscope:K,Pill:A,Syringe:W,Thermometer:V,Baby:C,Bone:k,Eye:v,Stethoscope:x,TestTube:M,Dna:Y,FlaskConical:U,ScanLine:P,Atom:j,BrainCircuit:O,Waves:_,CircleDot:y,BookOpenText:R,Shield:G,Building:F,Hospital:L,Building2:B,Truck:S,UserRound:q},i=s[e];if(i)return e=>l.jsx(i,{...r,...e});oe.warn("Icon not found in lucide-react",{databaseIcon:a,mappedIconName:e,availableIconsCount:Object.keys(s).length})}return e=>l.jsx(b,{...r,...e})},he=({icon:e,fallback:a=b,...r})=>{if(!e){const e=a;return l.jsx(e,{...r})}if("string"==typeof e){const e=a;return l.jsx(e,{...r})}try{return l.jsx(e,{...r})}catch(t){oe.warn("Icon rendering failed, using fallback",{iconComponent:e?.name},t);const s=a;return l.jsx(s,{...r})}},pe=({title:e,description:a,icon:r,color:t,progress:s,questionCount:i,onClick:o,delay:n=0,type:c})=>{const d=be(e,r),g=t||((e,a)=>{if(!e)return"bg-gray-500";if(xe[e])return xe[e];const r=Object.keys(xe).find(a=>a.toLowerCase().includes(e.toLowerCase())||e.toLowerCase().includes(a.toLowerCase()));if(r)return xe[r];const t=e.toLowerCase();if(t.includes("cardio")||t.includes("heart"))return"bg-red-500";if(t.includes("neuro")||t.includes("brain")||t.includes("nervous"))return"bg-purple-500";if(t.includes("pulmonary")||t.includes("lung")||t.includes("respiratory"))return"bg-blue-600";if(t.includes("gastro")||t.includes("gi")||t.includes("nutrition"))return"bg-orange-500";if(t.includes("renal")||t.includes("kidney")||t.includes("urology"))return"bg-amber-500";if(t.includes("endocrine")||t.includes("diabetes")||t.includes("metabolism"))return"bg-orange-600";if(t.includes("hematology")||t.includes("oncology")||t.includes("blood"))return"bg-red-700";if(t.includes("infectious")||t.includes("infection"))return"bg-red-400";if(t.includes("reproductive")||t.includes("gynecology"))return"bg-pink-600";if(t.includes("psychiatric")||t.includes("behavioral"))return"bg-indigo-500";if(t.includes("ophthalm")||t.includes("eye"))return"bg-cyan-500";if(t.includes("dermatology")||t.includes("skin"))return"bg-pink-500";if(t.includes("anatomy"))return"bg-blue-500";if(t.includes("physiology"))return"bg-green-600";if(t.includes("pharmacology")||t.includes("drug"))return"bg-teal-500";if(t.includes("pathology"))return"bg-red-500";if(t.includes("biochemistry"))return"bg-emerald-500";if(t.includes("microbiology"))return"bg-indigo-500";if(t.includes("genetics"))return"bg-violet-500";switch(a){case"system":return"bg-red-500";case"subject":return"bg-blue-500";case"topic":return"bg-green-500";default:return"bg-gray-500"}})(e,c);return l.jsxs(u.button,{initial:{y:15,opacity:0},animate:{y:0,opacity:1},transition:{delay:n},whileHover:{scale:1.01},whileTap:{scale:.98},onClick:o,className:"w-full bg-white dark:bg-expo-850 rounded-xl p-3 shadow-card dark:shadow-card-dark hover:shadow-card-hover dark:hover:shadow-card-dark-hover text-left transition-all duration-200 border border-gray-50 dark:border-expo-700",children:[l.jsxs("div",{className:"flex items-start justify-between mb-3",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx("div",{className:`p-2 rounded-xl ${g}`,children:l.jsx(he,{icon:d,fallback:b,size:18,className:"text-white","data-lucide":r?r.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase():void 0})}),l.jsxs("div",{children:[l.jsx("h3",{className:"font-semibold text-gray-800 dark:text-dark-50 text-sm",children:e}),l.jsxs("p",{className:"text-xs text-gray-500 dark:text-dark-400",children:[i," questions"]})]})]}),l.jsx(Z,{size:16,className:"text-gray-400 dark:text-dark-400 mt-0.5"})]}),l.jsx("p",{className:"text-xs text-gray-600 dark:text-dark-300 mb-3 leading-relaxed",children:a}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("div",{className:"flex-1",children:l.jsx("div",{className:"bg-gray-200 dark:bg-expo-700 rounded-full h-2 mr-2 overflow-hidden",children:l.jsx(u.div,{className:"h-2 rounded-full transition-all duration-500 "+(m=s,m>=80?"bg-success-500":m>=60?"bg-warning-500":m>=40?"bg-orange-500":"bg-danger-500"),initial:{width:0},animate:{width:`${s}%`},transition:{delay:n+.2,duration:.6}})})}),l.jsxs("span",{className:"text-xs font-bold text-gray-700 dark:text-dark-200",children:[s,"%"]})]})]});var m},fe=({category:e,onClick:a,delay:r=0})=>{const t=be(e.name,e.icon,{"data-lucide":e.icon?e.icon.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase():void 0}),s=(e=>e&&"object"==typeof e?{totalQuestions:e.questionCount||e.question_count||0,averageAccuracy:e.accuracy||0,completionRate:e.completionRate||0,lastAttempted:e.lastUsed||e.last_used||null,difficulty:e.difficulty||"medium",isHighYield:e.isHighYield||e.is_high_yield||!1,estimatedTime:Math.ceil(1.5*(e.questionCount||e.question_count||10))}:{totalQuestions:0,averageAccuracy:0,completionRate:0,lastAttempted:null,difficulty:"medium",isHighYield:!1,estimatedTime:15})(e),n=(e=>{switch(e?.toLowerCase()){case"easy":case"beginner":return"text-green-600 bg-green-100";case"medium":case"intermediate":return"text-yellow-600 bg-yellow-100";case"hard":case"advanced":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(s.difficulty),c=(d=s.averageAccuracy)&&0!==d?d>=80?"text-green-600":d>=60?"text-yellow-600":d>=40?"text-orange-600":"text-red-600":"text-gray-600";var d;const g=(e=>e>=80?"bg-green-500":e>=60?"bg-yellow-500":e>=40?"bg-orange-500":"bg-red-500")(s.averageAccuracy);return l.jsx(u.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.4,delay:r},whileHover:{x:5},onClick:a,className:"bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-4 flex-1",children:[l.jsx("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl",children:l.jsx(t,{className:"w-6 h-6 text-blue-600 dark:text-blue-400"})}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[l.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white truncate",children:e.name}),e.isFavorite&&l.jsx(J,{className:"w-4 h-4 text-yellow-500 fill-current"}),s.isHighYield&&l.jsx("span",{className:"px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 text-xs font-medium rounded-full",children:"High-Yield"})]}),l.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 truncate mb-2",children:e.description||`${s.totalQuestions} practice questions available`}),l.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400",children:[l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx(X,{className:"w-3 h-3"}),l.jsxs("span",{children:[s.estimatedTime," min"]})]}),l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx(i,{className:"w-3 h-3"}),l.jsx("span",{className:`font-medium ${c}`,children:s.averageAccuracy?`${s.averageAccuracy}%`:"No data"})]}),l.jsxs("div",{className:"flex items-center space-x-1",children:[l.jsx(o,{className:"w-3 h-3"}),l.jsxs("span",{children:["Last: ",me(s.lastAttempted)]})]})]})]})]}),l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsxs("div",{className:"text-right",children:[l.jsx("div",{className:"text-lg font-bold text-gray-900 dark:text-white",children:s.totalQuestions}),l.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"questions"})]}),l.jsx("div",{className:`px-2 py-1 rounded-full text-xs font-medium ${n}`,children:s.difficulty}),s.averageAccuracy>0&&l.jsxs("div",{className:"w-16",children:[l.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:l.jsx("div",{className:`h-2 rounded-full ${g}`,style:{width:`${Math.min(100,s.averageAccuracy)}%`}})}),l.jsxs("div",{className:"text-xs text-center mt-1 text-gray-500 dark:text-gray-400",children:[s.averageAccuracy,"%"]})]}),l.jsx(Z,{className:"w-5 h-5 text-gray-400"})]})]})})},ve=({icon:e,title:a,subtitle:r,description:t,action:s})=>l.jsx(u.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.3},className:"bg-white dark:bg-expo-850 rounded-2xl p-6 shadow-card dark:shadow-card-dark border border-gray-100 dark:border-expo-700 text-center",children:l.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[l.jsx("div",{className:"p-4 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-expo-800 dark:to-expo-700 rounded-2xl",children:l.jsx(e,{size:32,className:"text-gray-400 dark:text-dark-400"})}),l.jsxs("div",{className:"space-y-2",children:[l.jsx("h3",{className:"text-lg font-bold text-gray-800 dark:text-dark-50",children:a}),l.jsx("p",{className:"text-sm text-gray-600 dark:text-dark-300",children:r}),t&&l.jsx("p",{className:"text-xs text-gray-500 dark:text-dark-400 max-w-sm mx-auto leading-relaxed",children:t})]}),s&&l.jsx(u.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"mt-4",children:s})]})}),je=({categories:e=[],filters:a={},onCategorySelect:t,onClearFilters:s})=>{if(!t||"function"!=typeof t)return oe.error("CategoryGrid: onCategorySelect prop is required and must be a function",{onCategorySelect:typeof t}),l.jsx("div",{className:"flex items-center justify-center py-12",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-red-600 mb-2",children:"Configuration Error"}),l.jsx("div",{className:"text-sm text-gray-500",children:"Invalid component props"})]})});const i={search:"",selectedFilter:"all",viewMode:"grid",...a},o=Array.isArray(e)?e:[];let c=[],d=[];try{c=((e,a)=>{if(!Array.isArray(e))return console.warn("filterCategories: categories must be an array"),[];if(!a||"object"!=typeof a)return console.warn("filterCategories: filters must be an object"),e;const{search:r,selectedFilter:t}=a;let s=e.filter(e=>e&&"object"==typeof e&&(e.id||e.title||e.name));if(r&&r.trim()){const e=r.toLowerCase().trim();s=s.filter(a=>{const r=(a.title||a.name||"").toLowerCase(),t=(a.description||"").toLowerCase();return r.includes(e)||t.includes(e)})}if(t&&"all"!==t)switch(t){case"system":s=s.filter(e=>"system"===e.type);break;case"subject":s=s.filter(e=>"subject"===e.type);break;case"topic":s=s.filter(e=>"topic"===e.type);break;case"high-yield":s=s.filter(e=>(e.questionCount||0)>=50||!0===e.isHighYield);break;default:s=s.filter(e=>e.type===t)}return s})(o,i),d=((e,a="name")=>{if(!Array.isArray(e))return console.warn("sortCategories: categories must be an array"),[];let r=[...e.filter(e=>e&&"object"==typeof e)];try{switch(a){case"name":return r.sort((e,a)=>{const r=(e.title||e.name||"Untitled").toString(),t=(a.title||a.name||"Untitled").toString();return r.localeCompare(t)});case"questionCount":return r.sort((e,a)=>(a.questionCount||0)-(e.questionCount||0));case"difficulty":{const e={easy:1,beginner:1,medium:2,intermediate:2,hard:3,advanced:3};return r.sort((a,r)=>{const t=(a.difficulty||"medium").toLowerCase(),s=(r.difficulty||"medium").toLowerCase();return(e[t]||2)-(e[s]||2)})}case"lastUsed":return r.sort((e,a)=>{const r=e.lastUsed||e.last_used,t=a.lastUsed||a.last_used;if(!r&&!t)return 0;if(!r)return 1;if(!t)return-1;try{return new Date(t)-new Date(r)}catch(s){return console.warn("Error comparing dates:",s),0}});case"accuracy":return r.sort((e,a)=>(a.accuracy||0)-(e.accuracy||0));case"progress":return r.sort((e,a)=>(a.progress||0)-(e.progress||0));case"popular":return r.sort((e,a)=>e.isPopular&&!a.isPopular?-1:!e.isPopular&&a.isPopular?1:(a.questionCount||0)-(e.questionCount||0));default:return r}}catch(t){return console.error("Error sorting categories:",t),r}})(c,"name")}catch(g){oe.error("Error filtering/sorting categories",{categoriesCount:o.length,filters:i},g),d=o}if(0===d.length){const e=i.search||"all"!==i.selectedFilter,a=()=>{e&&s&&"function"==typeof s?s():window.location.reload()};return l.jsx("div",{className:"flex items-center justify-center py-12",children:l.jsx(ve,{icon:e?n:r,title:e?"No categories found":"No categories available",subtitle:e?`No categories match your search "${i.search}" or filter "${i.selectedFilter}".`:"Categories are being loaded. Please check your connection.",description:e?"Try adjusting your search terms or clearing filters.":"Please try again in a moment.",action:l.jsx("button",{onClick:a,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:e?"Clear Filters":"Refresh"})})})}return"grid"===i.viewMode?l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:d.map((e,a)=>e&&e.id?l.jsx(u.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.05*a},children:l.jsx(pe,{title:e.title||e.name||"Unknown Category",description:e.description||"No description available",icon:e.icon,color:e.color,type:e.type,progress:e.progress||0,questionCount:e.questionCount||0,onClick:()=>t(e.id,e.title||e.name),delay:.05*a})},e.id):(oe.warn("Invalid category data in grid view",{category:e,index:a}),null)).filter(Boolean)}):l.jsx("div",{className:"space-y-3",children:d.map((e,a)=>e&&e.id?l.jsx(u.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.4,delay:.03*a},children:l.jsx(fe,{category:e,onClick:()=>t(e.id,e.title||e.name),delay:.03*a})},e.id):(oe.warn("Invalid category data in list view",{category:e,index:a}),null)).filter(Boolean)})},we={questionCount:10,timePerQuestion:60},ke={questionCount:20,totalTime:1800},Ne=({onQuickStart:e,onTimedTest:a,onCustomQuiz:r})=>{const t=we,s=ke,i=[{id:"quick-quiz",title:"🟣 Quick Quiz",subtitle:`${t.questionCount} questions (default)`,description:"Quick practice, category-specific review",icon:ee,color:"bg-purple-600 hover:bg-purple-700",action:e,timeEstimate:`~${Math.ceil(t.questionCount*t.timePerQuestion/60)} min`,mode:"quick",status:"✅ Live",details:`${t.timePerQuestion}s per question • Auto-advance (0.5s delay) • Explanations after quiz completion`},{id:"timed-test",title:"🔵 Timed Test",subtitle:`${s.questionCount} questions (fixed)`,description:"Exam simulation, time management practice",icon:ae,color:"bg-blue-600 hover:bg-blue-700",action:a,timeEstimate:s.totalTime/60+" min",mode:"timed",status:"✅ Live",details:s.totalTime/60+" min total • Manual advance (click Next) • Explanations after each answer"},{id:"custom-quiz",title:"🟢 Custom Quiz",subtitle:"1-40 questions (user choice)",description:"Targeted study, flexible practice",icon:re,color:"bg-green-600 hover:bg-green-700",action:r,timeEstimate:"Variable",mode:"custom",status:"✅ Live",details:"1 min/question or self-paced • Manual advance • Explanations after each answer"}];return l.jsxs("div",{className:"mb-8",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"Quiz Modes"}),l.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Choose your practice style"})]}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:i.map((e,a)=>l.jsxs(u.button,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1*a},whileHover:{y:-3,scale:1.02},whileTap:{scale:.98},onClick:e.action,disabled:e.status.includes("Coming Soon"),className:`${e.color} text-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 text-left group relative ${e.status.includes("Coming Soon")?"opacity-75 cursor-not-allowed":""}`,children:[l.jsx("div",{className:"absolute top-2 right-2 text-xs px-2 py-1 bg-white/20 rounded-full",children:e.status}),l.jsxs("div",{className:"flex items-start justify-between mb-4",children:[l.jsx("div",{className:"p-2 bg-white/20 rounded-lg",children:l.jsx(e.icon,{className:"w-6 h-6"})}),l.jsx("div",{className:"text-xs opacity-80",children:e.timeEstimate})]}),l.jsx("h3",{className:"text-lg font-bold mb-1",children:e.title}),l.jsx("p",{className:"text-sm opacity-90 mb-2",children:e.subtitle}),l.jsx("p",{className:"text-xs opacity-75 mb-3",children:e.description}),l.jsx("div",{className:"text-xs opacity-75 mb-2",children:e.details}),!e.status.includes("Coming Soon")&&l.jsxs("div",{className:"flex items-center text-sm opacity-80 group-hover:opacity-100 transition-opacity",children:[l.jsx("span",{children:"Start now"}),l.jsx(ee,{className:"w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform"})]})]},e.id))})]})},Ce=()=>l.jsxs("div",{className:"space-y-6",children:[l.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg animate-pulse",children:l.jsxs("div",{className:"flex space-x-4",children:[l.jsx("div",{className:"flex-1 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"}),l.jsx("div",{className:"w-32 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"}),l.jsx("div",{className:"w-20 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"})]})}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[1,2,3,4].map(e=>l.jsx("div",{className:"h-32 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse"},e))}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3,4,5,6].map(e=>l.jsx("div",{className:"h-48 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse"},e))})]}),qe=({error:e,onRetry:a,onGoHome:r})=>l.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4",children:l.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-red-200 dark:border-red-800 max-w-md w-full text-center",children:[l.jsx("div",{className:"p-3 bg-red-100 dark:bg-red-900/30 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center",children:l.jsx(se,{className:"w-8 h-8 text-red-600"})}),l.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Unable to Load Categories"}),l.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:e?.message||"There was a problem loading the quiz categories. This might be due to a network issue."}),l.jsxs("div",{className:"space-y-3",children:[l.jsx("button",{onClick:a,className:"w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200",children:"Try Again"}),r&&l.jsx("button",{onClick:r,className:"w-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200",children:"Go to Home"})]}),!1]})}),Se=()=>{const r=te(),{user:t}=ne();ge();const{data:s=[],isLoading:i,isError:o,error:n,refetch:c}=(d=t?.id,e({queryKey:ce.categoriesWithProgress(d),queryFn:async()=>ie(async()=>{oe.info("🔍 [CategoriesQuery] Starting categories fetch",{userId:d});const{data:e,error:a}=await le.from("tags").select("id, name, type, is_active").eq("is_active",!0).eq("type","subject").order("name");if(a)throw oe.error("❌ [CategoriesQuery] Error fetching categories",{error:a,userId:d}),a;if(!e||0===e.length)return oe.warn("⚠️ [CategoriesQuery] No categories found",{userId:d}),[];if(oe.success("✅ [CategoriesQuery] Categories fetched successfully",{count:e.length,userId:d}),!d)return e.map(e=>({...e,progress:0,questionsAnswered:0,totalQuestions:0,accuracy:0,color:de(e.name)}));const r=await Promise.all(e.map(async e=>{try{const{count:a,error:r}=await le.from("question_tags").select("*",{count:"exact",head:!0}).eq("tag_id",e.id);r&&oe.warn("⚠️ [CategoriesQuery] Error counting questions for category",{categoryId:e.id,error:r});const{data:t,error:s}=await le.from("quiz_responses").select("\n                  is_correct,\n                  questions!inner(\n                    question_tags!inner(tag_id)\n                  )\n                ").eq("questions.question_tags.tag_id",e.id);s&&oe.warn("⚠️ [CategoriesQuery] Error fetching user responses for category",{categoryId:e.id,error:s});const i=t?.length||0,o=t?.filter(e=>e.is_correct).length||0,l=i>0?Math.round(o/i*100):0,n=a>0?Math.round(i/a*100):0;return{...e,progress:Math.min(n,100),questionsAnswered:i,totalQuestions:a||0,accuracy:l,color:de(e.name)}}catch(n){return oe.error("❌ [CategoriesQuery] Error processing category progress",{categoryId:e.id,error:n.message}),{...e,progress:0,questionsAnswered:0,totalQuestions:0,accuracy:0,color:de(e.name)}}}));return oe.success("✅ [CategoriesQuery] Categories with progress calculated",{totalCategories:r.length,userId:d}),r},{queryType:`categories-${d||"anonymous"}`,fallback:[]}),staleTime:3e5,gcTime:9e5,enabled:!0,retry:2,retryDelay:e=>Math.min(1e3*2**e,3e4)}));var d;const[g,u]=a.useState({search:"",selectedFilter:"all",viewMode:"grid"}),m=a.useCallback(e=>{e&&"object"==typeof e?u(a=>({...a,...e})):console.warn("Invalid filters object:",e)},[]),y=a.useCallback(()=>{u({search:"",selectedFilter:"all",viewMode:"grid"})},[]),x={questionCount:10,autoAdvance:!0,timePerQuestion:60,showExplanations:!1,allowReview:!1},b=a.useCallback((e,a)=>{if(e)try{const t=x;r("/quick-quiz",{state:{categoryId:e,categoryName:a||"Quiz",questionCount:t.questionCount,difficulty:null,quizMode:"quick",quizType:"category",autoAdvance:t.autoAdvance,timePerQuestion:t.timePerQuestion}})}catch(t){console.error("Navigation error:",t),r(`/quiz/${e}`)}else console.warn("Category ID is required")},[r]),h=a.useCallback(()=>{r("/quick-quiz",{state:{categoryId:"mixed",categoryName:"Quick Start Quiz",questionCount:x.questionCount,quizMode:"quick",quizType:"quick_start",autoAdvance:x.autoAdvance,timePerQuestion:x.timePerQuestion}})},[r]),p=a.useCallback(()=>{r("/timed-test-setup")},[r]),f=a.useCallback(()=>{r("/custom-quiz-setup")},[r]),v=a.useCallback(()=>{r("/")},[r]);return o?l.jsx(qe,{error:n,onRetry:c,onGoHome:v}):i?l.jsx(Ce,{}):l.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 pb-20 md:pb-0",children:l.jsxs("div",{className:"px-3 md:px-6 lg:px-8 pt-2 pb-3 max-w-6xl mx-auto",children:[l.jsxs("div",{className:"mb-6",children:[l.jsx("h1",{className:"text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"Quiz Categories"}),l.jsxs("p",{className:"text-gray-600 dark:text-gray-300",children:["Choose from ",s.length," categories or start a quick quiz"]})]}),l.jsx(Ne,{onQuickStart:h,onTimedTest:p,onCustomQuiz:f}),l.jsx(a.Suspense,{fallback:l.jsx("div",{children:"Loading filters..."}),children:l.jsx(ue,{filters:g,onFiltersChange:m})}),l.jsx(a.Suspense,{fallback:l.jsx("div",{children:"Loading categories..."}),children:l.jsx(je,{categories:s,filters:g,onCategorySelect:b,onClearFilters:y})})]})})};export{Se as default};
