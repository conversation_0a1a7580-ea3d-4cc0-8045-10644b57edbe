import{r as e,u as a,j as r,m as s,l as t,aS as i,i as o,T as l,U as d,aY as n,aZ as m,aP as c}from"./vendor-DfmD63KD.js";import{u,c as p,l as x}from"./index-gGBCxdYu.js";import{V as y,c as f,b as w,d as g,a as h,e as b}from"./formValidation-jOc8dLyX.js";const N=()=>{const[N,j]=e.useState({fullName:"",email:"",password:"",confirmPassword:""}),[v,k]=e.useState({}),[P,C]=e.useState({}),[S,A]=e.useState(!1),[V,T]=e.useState(""),[U,q]=e.useState(!1),{isConfigured:B}=u();a();const z=f({fullName:e=>b(e),email:e=>h(e),password:e=>g(e),confirmPassword:e=>w(N.password,e)}),E=(e,a)=>{if(j(r=>({...r,[e]:a})),T(""),P[e]||a){const r=z.validateField(e,a,{...N,[e]:a});k(a=>({...a,[e]:r.error}))}if("password"===e&&P.confirmPassword){const e=w(a,N.confirmPassword);k(a=>({...a,confirmPassword:e.error}))}},F=e=>{C(a=>({...a,[e]:!0}));const a=z.validateField(e,N[e],N);k(r=>({...r,[e]:a.error}))},I=N.fullName&&N.email&&N.password&&N.confirmPassword&&!v.fullName&&!v.email&&!v.password&&!v.confirmPassword;return U?r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 dark:from-expo-950 dark:to-expo-900 flex items-center justify-center px-4",children:r.jsx(s.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"w-full max-w-md text-center",children:r.jsxs("div",{className:"bg-white dark:bg-expo-850 rounded-2xl p-8 shadow-card dark:shadow-card-dark border border-gray-100 dark:border-expo-700",children:[r.jsx(s.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"inline-flex items-center justify-center w-16 h-16 bg-green-500 rounded-full mb-6",children:r.jsx(t,{size:32,className:"text-white"})}),r.jsx("h2",{className:"text-2xl font-bold text-gray-800 dark:text-dark-50 mb-2",children:"Account Created!"}),r.jsx("p",{className:"text-gray-600 dark:text-dark-300 mb-6",children:"Please check your email to verify your account before signing in."}),r.jsx(i,{to:"/login",className:"inline-block bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-6 rounded-xl transition-colors","data-testid":"go-to-login",children:"Go to Sign In"})]})})}):r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 dark:from-expo-950 dark:to-expo-900 flex items-center justify-center px-4",children:r.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"w-full max-w-md",children:[r.jsxs("div",{className:"text-center mb-8",children:[r.jsx(s.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2},className:"inline-flex items-center justify-center w-16 h-16 bg-primary-600 rounded-2xl mb-4 shadow-lg",children:r.jsx(o,{size:32,className:"text-white"})}),r.jsx("h1",{className:"text-3xl font-bold text-gray-800 dark:text-dark-50 mb-2",children:"Create Account"}),r.jsx("p",{className:"text-gray-600 dark:text-dark-300",children:"Start your USMLE preparation journey today"})]}),r.jsx(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"bg-white dark:bg-expo-850 rounded-2xl p-8 shadow-card dark:shadow-card-dark border border-gray-100 dark:border-expo-700",children:r.jsxs("form",{onSubmit:async e=>{e.preventDefault(),A(!0),T("");const{isValid:a,errors:r}=z.validateAll(N);if(!a)return k(r),C({fullName:!0,email:!0,password:!0,confirmPassword:!0}),void A(!1);if(!(N.fullName.trim()&&N.email&&N.password&&N.confirmPassword))return T("Please fill in all fields"),void A(!1);if(N.password!==N.confirmPassword)return T("Passwords do not match"),void A(!1);try{await p.signUp(N.email.trim(),N.password,N.fullName.trim()),q(!0)}catch(s){(e=>{x.error("SignUp error occurred",{formData:{email:N.email,fullName:N.fullName}},e),e.message.includes("JSON")||e.message.includes("Unexpected token")?T("Server communication error. Please try again."):e.message.includes("fetch")||e.message.includes("Network")?T("Network error. Please check your connection and try again."):e.message.includes("User already registered")||e.message.includes("already in use")?T("This email is already registered. Please use a different email or try signing in."):e.message.includes("Password should be at least")?T("Password must be at least 6 characters long."):e.message.includes("Invalid email")?T("Please enter a valid email address."):e.message.includes("Too many requests")?T("Too many signup attempts. Please wait a moment and try again."):T(e.message||"An error occurred during registration. Please try again.")})(s)}finally{A(!1)}},className:"space-y-6",noValidate:!0,children:[!B&&r.jsxs(s.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 p-4 rounded-xl text-sm font-medium border border-yellow-200 dark:border-yellow-800 flex items-center gap-3",children:[r.jsx(l,{size:20}),r.jsxs("div",{children:[r.jsx("h3",{className:"font-bold",children:"Supabase Not Configured"}),r.jsx("p",{className:"text-xs",children:"Please set up your .env.local file to enable registration."})]})]}),V&&r.jsx(s.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-xl text-sm font-medium border border-red-200 dark:border-red-800","data-testid":"signup-error",children:V}),r.jsx(y,{type:"text",name:"fullName",value:N.fullName,onChange:E,onBlur:F,placeholder:"Enter your full name",label:"Full Name",icon:d,error:P.fullName?v.fullName:"",isValid:!v.fullName&&N.fullName,required:!0,autoComplete:"name","data-testid":"fullname-input"}),r.jsx(y,{type:"email",name:"email",value:N.email,onChange:E,onBlur:F,placeholder:"Enter your email",label:"Email Address",icon:n,error:P.email?v.email:"",isValid:!v.email&&N.email,required:!0,autoComplete:"email","data-testid":"email-input"}),r.jsx(y,{type:"password",name:"password",value:N.password,onChange:E,onBlur:F,placeholder:"Create a password",label:"Password",icon:m,error:P.password?v.password:"",isValid:!v.password&&N.password,required:!0,showPasswordToggle:!0,autoComplete:"new-password","data-testid":"password-input"}),r.jsx(y,{type:"password",name:"confirmPassword",value:N.confirmPassword,onChange:E,onBlur:F,placeholder:"Confirm your password",label:"Confirm Password",icon:m,error:P.confirmPassword?v.confirmPassword:"",isValid:!v.confirmPassword&&N.confirmPassword,required:!0,showPasswordToggle:!0,autoComplete:"new-password","data-testid":"confirm-password-input"}),r.jsx(s.button,{type:"submit",disabled:S||!B||!I,whileHover:!S&&B&&I?{scale:1.02}:{},whileTap:!S&&B&&I?{scale:.98}:{},className:`\n                w-full py-3 px-6 rounded-xl font-bold text-white transition-all duration-200\n                ${!S&&B&&I?"bg-primary-600 hover:bg-primary-700 shadow-lg hover:shadow-xl":"bg-gray-400 cursor-not-allowed opacity-50"}\n              `,"data-testid":"signup-submit",children:S?r.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[r.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),r.jsx("span",{children:"Creating Account..."})]}):B?"Create Account":"Configuration Missing"}),r.jsx("div",{className:"text-center pt-4 border-t border-gray-200 dark:border-expo-700",children:r.jsxs("p",{className:"text-sm text-gray-600 dark:text-dark-300",children:["Already have an account?"," ",r.jsx(i,{to:"/login",className:"text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-bold",children:"Sign In"})]})})]})}),r.jsx(s.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"text-center mt-6",children:r.jsxs(i,{to:"/",className:"inline-flex items-center text-sm text-gray-600 dark:text-dark-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors",children:[r.jsx(c,{size:16,className:"mr-2"}),"Back to Home"]})})]})})};export{N as default};
