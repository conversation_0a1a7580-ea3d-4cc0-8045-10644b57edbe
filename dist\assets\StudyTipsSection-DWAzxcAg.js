import{r as e,j as t,m as a,b2 as r,b3 as s,at as l,A as i}from"./vendor-DfmD63KD.js";const o=()=>{const[o,n]=e.useState(0),c=["Review your incorrect answers immediately after each quiz to reinforce learning.","Focus on understanding concepts rather than memorizing isolated facts.","Practice with timed quizzes to simulate real exam conditions.","Take breaks every 45-60 minutes to maintain peak concentration.","Study high-yield topics that frequently appear on the USMLE Step 1.","Use spaced repetition to review previously learned material.","Form study groups to discuss difficult concepts with peers."];e.useEffect(()=>{const e=setInterval(()=>{n(e=>(e+1)%c.length)},15e3);return()=>clearInterval(e)},[c.length]);return t.jsxs(a.div,{className:"bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-xl p-6 mb-8 border border-yellow-200 dark:border-yellow-800",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},children:[t.jsxs("div",{className:"flex items-center justify-between mb-4",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx("div",{className:"p-2 bg-yellow-400 rounded-lg mr-3",children:t.jsx(r,{className:"w-5 h-5 text-yellow-900"})}),t.jsx("h3",{className:"text-lg font-bold text-gray-900 dark:text-white",children:"Study Tip"})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("button",{onClick:()=>{n(e=>(e-1+c.length)%c.length)},className:"p-1 rounded-full hover:bg-yellow-200 dark:hover:bg-yellow-800 transition-colors",children:t.jsx(s,{className:"w-4 h-4 text-gray-600 dark:text-gray-300"})}),t.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:[o+1," / ",c.length]}),t.jsx("button",{onClick:()=>{n(e=>(e+1)%c.length)},className:"p-1 rounded-full hover:bg-yellow-200 dark:hover:bg-yellow-800 transition-colors",children:t.jsx(l,{className:"w-4 h-4 text-gray-600 dark:text-gray-300"})})]})]}),t.jsx("div",{className:"relative min-h-[60px] flex items-center",children:t.jsx(i,{mode:"wait",children:t.jsx(a.p,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},className:"text-gray-700 dark:text-gray-200 leading-relaxed",children:c[o]},o)})}),t.jsx("div",{className:"flex justify-center space-x-2 mt-4",children:c.map((e,a)=>t.jsx("button",{onClick:()=>n(a),className:"w-2 h-2 rounded-full transition-colors "+(a===o?"bg-yellow-500":"bg-yellow-200 dark:bg-yellow-700 hover:bg-yellow-300 dark:hover:bg-yellow-600")},a))})]})};export{o as default};
