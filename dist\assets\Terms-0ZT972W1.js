import{j as e,m as a,i as r,aS as i,aP as t}from"./vendor-DfmD63KD.js";const s=()=>e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 dark:from-expo-950 dark:to-expo-900",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-6 py-12",children:[e.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-12",children:[e.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-primary-600 rounded-2xl mb-6 shadow-lg",children:e.jsx(r,{size:32,className:"text-white"})}),e.jsx("h1",{className:"text-4xl font-bold text-gray-800 dark:text-dark-50 mb-4",children:"Terms of Service"}),e.jsx("p",{className:"text-gray-600 dark:text-dark-300 text-lg",children:"Last updated: January 2024"})]}),e.jsx(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-white dark:bg-expo-850 rounded-2xl p-8 shadow-card dark:shadow-card-dark border border-gray-100 dark:border-expo-700",children:e.jsxs("div",{className:"prose prose-gray dark:prose-invert max-w-none",children:[e.jsx("h2",{children:"1. Acceptance of Terms"}),e.jsx("p",{children:"By accessing and using USMLE Trivia, you accept and agree to be bound by the terms and provision of this agreement."}),e.jsx("h2",{children:"2. Use License"}),e.jsx("p",{children:"Permission is granted to temporarily download one copy of USMLE Trivia materials for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:"}),e.jsxs("ul",{children:[e.jsx("li",{children:"modify or copy the materials"}),e.jsx("li",{children:"use the materials for any commercial purpose or for any public display"}),e.jsx("li",{children:"attempt to reverse engineer any software contained on the website"}),e.jsx("li",{children:"remove any copyright or other proprietary notations from the materials"})]}),e.jsx("h2",{children:"3. Medical Disclaimer"}),e.jsx("p",{children:"USMLE Trivia is for educational purposes only. The content provided should not be used as a substitute for professional medical advice, diagnosis, or treatment. Always seek the advice of your physician or other qualified health provider with any questions you may have regarding a medical condition."}),e.jsx("h2",{children:"4. Accuracy of Materials"}),e.jsx("p",{children:"The materials appearing on USMLE Trivia could include technical, typographical, or photographic errors. We do not warrant that any of the materials on the website are accurate, complete, or current."}),e.jsx("h2",{children:"5. User Accounts"}),e.jsx("p",{children:"When you create an account with us, you must provide information that is accurate, complete, and current at all times. You are responsible for safeguarding the password and for all activities that occur under your account."}),e.jsx("h2",{children:"6. Privacy Policy"}),e.jsx("p",{children:"Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service, to understand our practices."}),e.jsx("h2",{children:"7. Contact Information"}),e.jsx("p",{children:"If you have any questions about these Terms of Service, please contact <NAME_EMAIL>."})]})}),e.jsx(a.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"mt-8 text-center",children:e.jsxs(i,{to:"/auth/signup",className:"inline-flex items-center gap-2 text-gray-600 dark:text-dark-300 hover:text-gray-800 dark:hover:text-dark-100 transition-colors",children:[e.jsx(t,{size:16}),e.jsx("span",{className:"font-medium",children:"Back to Sign Up"})]})})]})});export{s as default};
