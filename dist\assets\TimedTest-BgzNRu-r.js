import{j as e,m as t,aH as s,aI as a,aJ as r,aK as i,aL as l,aM as n,aN as o,l as c,aF as d,a as x,H as m,u as h,f as u,r as f,A as b,aP as g}from"./vendor-DfmD63KD.js";import{f as p,c as j,d as y,r as N,Q as v,a as w,b as k}from"./QuizError-Mp16REcC.js";import{u as S}from"./index-gGBCxdYu.js";const C=["#06b6d4","#f59e42","#10b981","#f43f5e","#6366f1","#eab308"],I=({questions:h,answers:u,timeLeft:f,onRestart:b,onGoHome:g})=>{const p=u.filter(e=>e.isCorrect).length,j=Math.round(p/h.length*100),y=90*h.length-f,N=function(e){let t=0,s=0,a=0,r=0;return e.forEach(e=>{e.isCorrect?(a++,t=Math.max(t,a),r=0):(r++,s=Math.max(s,r),a=0)}),{maxCorrect:t,maxWrong:s}}(u),v=function(e,t){const s={};return e.forEach((e,a)=>{const r=e.topic||e.system||e.subject||"Other";s[r]||(s[r]={topic:r,correct:0,total:0}),s[r].total++,t[a]?.isCorrect&&s[r].correct++}),Object.values(s)}(h,u),w=function(e){return e.map((e,t)=>({q:t+1,time:e.timeSpent||0,correct:e.isCorrect}))}(u);return e.jsxs("div",{className:"max-w-2xl mx-auto mt-8 bg-white/80 dark:bg-slate-900/80 rounded-3xl shadow-xl p-8 flex flex-col items-center",children:[e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Timed Test Results"}),e.jsxs("div",{className:"flex flex-wrap gap-6 justify-center mb-6 w-full",children:[e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{className:"text-4xl font-bold text-green-600 dark:text-green-400",children:p}),e.jsx("span",{className:"text-sm text-slate-600 dark:text-slate-300",children:"Correct"})]}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("span",{className:"text-4xl font-bold text-cyan-600 dark:text-cyan-400",children:[j,"%"]}),e.jsx("span",{className:"text-sm text-slate-600 dark:text-slate-300",children:"Accuracy"})]}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{className:"text-4xl font-bold text-blue-600 dark:text-blue-400",children:(k=y,`${Math.floor(k/60).toString().padStart(2,"0")}:${(k%60).toString().padStart(2,"0")}`)}),e.jsx("span",{className:"text-sm text-slate-600 dark:text-slate-300",children:"Time Used"})]})]}),e.jsxs(t.div,{initial:{opacity:0,y:24},animate:{opacity:1,y:0},transition:{delay:.2},className:"w-full bg-white dark:bg-gray-800 rounded-2xl p-4 mb-6 shadow",children:[e.jsx("h3",{className:"text-lg font-bold mb-2",children:"Performance Analytics"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-semibold mb-1",children:"Per-topic Breakdown"}),e.jsx(s,{width:"100%",height:140,children:e.jsxs(a,{data:v,margin:{left:-20,right:10},children:[e.jsx(r,{dataKey:"topic",fontSize:12,tick:{fill:"#64748b"},interval:0,angle:-15,dy:10}),e.jsx(i,{allowDecimals:!1,fontSize:12,tick:{fill:"#64748b"}}),e.jsx(l,{formatter:(e,t)=>"correct"===t?`${e} correct`:e}),e.jsx(n,{dataKey:"correct",fill:"#06b6d4",children:v.map((t,s)=>e.jsx(o,{fill:C[s%C.length]},t.topic))})]})})]}),e.jsxs("div",{children:[e.jsx("div",{className:"font-semibold mb-1",children:"Time per Question (s)"}),e.jsx(s,{width:"100%",height:140,children:e.jsxs(a,{data:w,margin:{left:-20,right:10},children:[e.jsx(r,{dataKey:"q",fontSize:12,tick:{fill:"#64748b"}}),e.jsx(i,{allowDecimals:!1,fontSize:12,tick:{fill:"#64748b"}}),e.jsx(l,{formatter:e=>`${e}s`}),e.jsx(n,{dataKey:"time",fill:"#6366f1",children:w.map((t,s)=>e.jsx(o,{fill:t.correct?"#10b981":"#f43f5e"},t.q))})]})})]})]}),e.jsxs("div",{className:"flex flex-wrap gap-6 mt-4",children:[e.jsxs("div",{className:"bg-cyan-50 dark:bg-cyan-900/20 rounded-xl px-4 py-2 font-semibold",children:["Longest Correct Streak: ",e.jsx("span",{className:"text-green-600",children:N.maxCorrect})]}),e.jsxs("div",{className:"bg-red-50 dark:bg-red-900/20 rounded-xl px-4 py-2 font-semibold",children:["Longest Incorrect Streak: ",e.jsx("span",{className:"text-red-600",children:N.maxWrong})]})]})]}),e.jsx("div",{className:"w-full overflow-x-auto mb-6",children:e.jsxs("table",{className:"min-w-full text-sm text-left border rounded-xl overflow-hidden",children:[e.jsx("thead",{className:"bg-cyan-100 dark:bg-cyan-900/40",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-3 py-2",children:"#"}),e.jsx("th",{className:"px-3 py-2",children:"Question"}),e.jsx("th",{className:"px-3 py-2",children:"Your Answer"}),e.jsx("th",{className:"px-3 py-2",children:"Correct"}),e.jsx("th",{className:"px-3 py-2",children:"Result"})]})}),e.jsx("tbody",{children:h.map((t,s)=>{const a=u[s],r=t.options?.find(e=>e.id===a?.selectedOption),i=t.options?.find(e=>e.id===t.correct_option_id);return e.jsxs("tr",{className:"border-b last:border-b-0",children:[e.jsx("td",{className:"px-3 py-2 font-mono",children:s+1}),e.jsx("td",{className:"px-3 py-2 max-w-xs truncate",title:t.question_text,children:t.question_text}),e.jsx("td",{className:"px-3 py-2",children:r?r.text:e.jsx("span",{className:"italic text-slate-400",children:"No answer"})}),e.jsx("td",{className:"px-3 py-2",children:i?i.text:"-"}),e.jsx("td",{className:"px-3 py-2",children:a?.isCorrect?e.jsx(c,{className:"inline text-green-500",size:18,title:"Correct"}):e.jsx(d,{className:"inline text-red-500",size:18,title:"Incorrect"})})]},t.id)})})]})}),e.jsxs("div",{className:"flex gap-4 mt-2",children:[e.jsxs("button",{className:"flex items-center gap-2 px-5 py-2 rounded-xl bg-cyan-600 text-white font-semibold hover:bg-cyan-700 transition",onClick:b,children:[e.jsx(x,{size:20})," Restart"]}),e.jsxs("button",{className:"flex items-center gap-2 px-5 py-2 rounded-xl bg-slate-200 dark:bg-slate-800 text-slate-800 dark:text-slate-100 font-semibold hover:bg-slate-300 dark:hover:bg-slate-700 transition",onClick:g,children:[e.jsx(m,{size:20})," Home"]})]})]});var k},_=["🅰️","🅱️","🇨","🇩"],q=({show:t})=>t?e.jsx("div",{className:"absolute inset-0 pointer-events-none z-40 flex items-center justify-center",children:e.jsx("span",{role:"img","aria-label":"confetti",className:"text-5xl animate-bounce",children:"🎉"})}):null,z=({timeLeft:s,total:a})=>e.jsxs("div",{className:"sticky top-0 z-20 mb-4",children:[e.jsx(t.div,{className:"h-3 w-full bg-gray-200 rounded overflow-hidden",children:e.jsx(t.div,{className:"h-3 rounded "+(s<60?"bg-red-500":s<a/3?"bg-yellow-400":"bg-blue-500"),initial:{width:"100%"},animate:{width:s/a*100+"%"},transition:{duration:.5}})}),e.jsx("div",{className:"flex justify-between text-xs mt-1",children:e.jsxs("span",{children:["Time Left: ",e.jsxs("b",{children:[Math.floor(s/60),":",(s%60).toString().padStart(2,"0")]})]})})]}),T=()=>{const s=h(),a=u(),{user:r}=S(),i=a.state||{},l=i.questionCount||20,n=i.difficulty||"",o=90*l,[c,d]=f.useState([]),[x,C]=f.useState(!0),[T,E]=f.useState(null),[O,Q]=f.useState(0),[L,R]=f.useState(null),[A,M]=f.useState(!1),[P,$]=f.useState(!1),[H,K]=f.useState([]),[U,D]=f.useState(null),[F,G]=f.useState(!1),[B,V]=f.useState(o),[W,J]=f.useState(()=>"true"===localStorage.getItem("quizMuted")),[Y,X]=f.useState(!1),[Z,ee]=f.useState(0),te=f.useRef();f.useEffect(()=>{C(!0),p({userId:r.id,questionCount:l,difficulty:n||null}).then(async e=>{if(!e||e.length<l)return E({code:"NO_QUESTIONS",message:`Not enough questions available for your selection. Only ${e?.length||0} found.`}),d(e||[]),void C(!1);d(e),C(!1);const t=await j({userId:r.id,sessionType:"timed_test",totalQuestions:e.length,timePerQuestion:90,settings:{mode:"timed_test",totalTime:o,difficulty:n}});D(t.id)}).catch(e=>{E({code:"FETCH_ERROR",message:e?.message||"Failed to load questions."}),C(!1)})},[r,l,n,o]),f.useEffect(()=>{if(!F&&!x)return te.current&&clearInterval(te.current),te.current=setInterval(()=>{V(e=>e<=1?(clearInterval(te.current),G(!0),y(U),0):e-1)},1e3),()=>clearInterval(te.current)},[F,x,U]),f.useEffect(()=>{A&&!P&&L===c[O]?.correct_option_id&&(X(!0),setTimeout(()=>X(!1),1200))},[A,P,L,c,O]),f.useEffect(()=>{if(F){let e=0;const t=H.filter(e=>e.isCorrect).length,s=setInterval(()=>{e++,ee(Math.min(e,t)),e>=t&&clearInterval(s)},20);return()=>clearInterval(s)}},[F,H]);const se=f.useCallback(e=>{if(A)return;const t=c[O];t&&t.id?(R(e),M(!0),$(!1),N(U,{userId:r.id,questionId:t.id,isCorrect:e===t.correct_option_id})):E({code:"INVALID_QUESTION",message:"An internal error occurred: question is missing or invalid. Please restart the quiz."})},[A,c,O,U,r]),ae=f.useCallback(()=>{const e=c[O];e&&e.id?(K(t=>[...t,{questionId:e.id,selectedOption:L,isCorrect:L===e.correct_option_id,timedOut:P}]),R(null),M(!1),$(!1),O<c.length-1?Q(e=>e+1):(G(!0),y(U))):E({code:"INVALID_QUESTION",message:"An internal error occurred: question is missing or invalid. Please restart the quiz."})},[O,c,L,P,U]);if(H.filter(e=>e.isCorrect).length,x)return e.jsx(v,{});if(T)return e.jsx(w,{error:T,onRetry:()=>window.location.reload()});if(!c.length)return e.jsx(w,{error:{code:"NO_QUESTIONS",message:"No questions found for this test."},onRetry:()=>window.location.reload()});if(F)return e.jsxs(t.div,{className:"max-w-xl mx-auto mt-8 bg-white/80 dark:bg-slate-900/80 rounded-3xl shadow-xl p-8 flex flex-col items-center",initial:{opacity:0,y:24},animate:{opacity:1,y:0},children:[e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Test Complete!"}),e.jsxs("div",{className:"mb-4 text-3xl font-extrabold text-green-600",children:[e.jsx(b,{children:e.jsx(t.span,{initial:{scale:.7},animate:{scale:1},transition:{type:"spring",stiffness:300},children:Z},Z)}),e.jsxs("span",{className:"text-gray-700 font-normal text-xl",children:[" / ",c.length]})]}),e.jsx(I,{questions:c,answers:H,timeLeft:B,onRestart:()=>window.location.reload(),onGoHome:()=>s("/quiz-tab")})]});const re=c[O]||{};return e.jsx("div",{className:"min-h-screen flex flex-col items-center justify-start bg-gradient-to-br from-cyan-100 via-blue-100 to-slate-200 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 py-8",children:e.jsxs("div",{className:"w-full max-w-xl mx-auto relative",children:[e.jsx(z,{timeLeft:B,total:o}),e.jsx(k,{current:O+1,total:c.length,secondsLeft:B}),e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("button",{onClick:()=>s(-1),className:"p-2 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700 transition-colors",children:e.jsx(g,{className:"w-6 h-6 text-gray-600 dark:text-gray-300"})}),e.jsx("button",{onClick:()=>s("/quiz-tab"),className:"p-2 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700 transition-colors",children:e.jsx(m,{className:"w-6 h-6 text-gray-600 dark:text-gray-300"})})]}),e.jsx(b,{mode:"wait",children:re?e.jsxs(t.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},exit:{opacity:0,x:-50},transition:{duration:.3},className:"relative",children:[e.jsx(q,{show:Y}),e.jsxs("div",{className:"mb-2 font-semibold",children:["Question ",O+1," / ",c.length]}),e.jsx("div",{className:"mb-4 text-lg font-medium",children:re.question_text}),e.jsx("div",{className:"space-y-2 mb-4",children:re.options&&re.options.map((s,a)=>e.jsxs(t.button,{className:`w-full flex items-center gap-3 text-left p-3 rounded-xl border transition-all font-semibold text-base focus:outline-none focus:ring-2 focus:ring-cyan-400 ${L===s.id?s.id===re.correct_option_id?"bg-green-100 border-green-400 scale-105":"bg-red-100 border-red-400 shake":"border-gray-200 hover:bg-cyan-50"} ${A?"pointer-events-none":""}`,disabled:A,onClick:()=>se(s.id),"aria-pressed":L===s.id,tabIndex:0,children:[e.jsx("span",{className:"text-2xl",children:_[a]||"🔘"}),e.jsx("span",{children:s.text})]},s.id))}),e.jsx(b,{children:A&&e.jsxs(t.div,{initial:{opacity:0,y:12},animate:{opacity:1,y:0},exit:{opacity:0,y:-12},className:"mb-2",children:[L===re.correct_option_id?e.jsx("div",{className:"text-green-700 font-semibold",children:"Correct!"}):e.jsxs("div",{className:"text-red-700 font-semibold",children:["Incorrect. Correct answer: ",e.jsx("b",{children:re.options.find(e=>e.id===re.correct_option_id)?.text})]}),re.explanation&&e.jsx("div",{className:"mt-2 text-sm text-gray-600 bg-gray-50 rounded p-2",children:re.explanation}),e.jsx("button",{className:"mt-4 w-full bg-cyan-600 text-white py-2 rounded-xl font-semibold text-lg shadow-lg hover:bg-cyan-700 transition",onClick:ae,"data-next-btn":!0,children:O<c.length-1?"Next Question":"Finish Test"})]})})]},re.id):!x&&e.jsxs("div",{className:"text-center text-gray-600 dark:text-gray-400 py-10",children:[e.jsx("p",{children:"No question available to display. Please try again or select a different category."}),e.jsx("button",{onClick:()=>s(-1),className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Go Back"})]})})]})})};export{T as default};
