import{u as e,r as a,j as s,av as t,I as l}from"./vendor-DfmD63KD.js";const r=[{value:"",label:"Mixed"},{value:"easy",label:"Easy"},{value:"medium",label:"Medium"},{value:"hard",label:"Hard"}],i=()=>{const i=e(),[n,c]=a.useState(""),[d,m]=a.useState(20),[x,u]=a.useState(null);return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-cyan-100 via-blue-100 to-slate-200 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900",children:s.jsxs("form",{onSubmit:e=>{e.preventDefault(),d<10||d>40?u("Please select between 10 and 40 questions."):(u(null),i("/timed-test",{state:{difficulty:n,questionCount:d}}))},className:"max-w-lg w-full bg-white/80 dark:bg-slate-900/80 rounded-3xl shadow-2xl p-8 flex flex-col items-center",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(t,{size:32,className:"text-cyan-600"}),s.jsx("h1",{className:"text-2xl font-bold",children:"Timed Test Setup"})]}),s.jsxs("ul",{className:"text-slate-700 dark:text-slate-200 mb-6 text-base space-y-2 w-full",children:[s.jsxs("li",{className:"flex items-center gap-2",children:[s.jsx(l,{size:18,className:"text-cyan-500"})," Choose 10–40 questions from all categories"]}),s.jsxs("li",{className:"flex items-center gap-2",children:[s.jsx(l,{size:18,className:"text-cyan-500"})," 1.5 minutes per question (time scales with count)"]}),s.jsxs("li",{className:"flex items-center gap-2",children:[s.jsx(l,{size:18,className:"text-cyan-500"})," Select difficulty: Easy, Medium, Hard, or Mixed"]}),s.jsxs("li",{className:"flex items-center gap-2",children:[s.jsx(l,{size:18,className:"text-cyan-500"})," Manual advance: review answer, then click Next"]}),s.jsxs("li",{className:"flex items-center gap-2",children:[s.jsx(l,{size:18,className:"text-cyan-500"})," Explanations shown after each answer"]})]}),s.jsxs("div",{className:"w-full mb-4",children:[s.jsx("label",{htmlFor:"difficulty",className:"block font-medium text-gray-800 dark:text-gray-200 mb-1",children:"Difficulty"}),s.jsx("select",{id:"difficulty",value:n,onChange:e=>c(e.target.value),className:"w-full rounded-lg border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2",children:r.map(e=>s.jsx("option",{value:e.value,children:e.label},e.value))})]}),s.jsxs("div",{className:"w-full mb-4",children:[s.jsx("label",{htmlFor:"questionCount",className:"block font-medium text-gray-800 dark:text-gray-200 mb-1",children:"Number of Questions"}),s.jsx("input",{id:"questionCount",type:"number",min:10,max:40,value:d,onChange:e=>m(Number(e.target.value)),className:"w-full rounded-lg border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2"}),s.jsx("span",{className:"text-xs text-gray-500",children:"10–40 questions"})]}),x&&s.jsx("div",{className:"text-red-600 bg-red-100 dark:bg-red-900/20 p-2 rounded mb-2 w-full text-center",children:x}),s.jsx("button",{className:"mt-2 px-8 py-3 rounded-xl bg-cyan-600 text-white font-semibold text-lg shadow-lg hover:bg-cyan-700 transition w-full",type:"submit",children:"Start Timed Test"})]})})};export{i as default};
