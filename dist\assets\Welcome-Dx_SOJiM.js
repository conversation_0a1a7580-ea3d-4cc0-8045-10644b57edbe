import{j as e,m as t,k as i,y as s,z as a,a$ as n,aS as r}from"./vendor-DfmD63KD.js";const o=()=>{const o=[{icon:s,title:"High-Yield Content",description:"Focus on the most important concepts for USMLE success"},{icon:a,title:"Track Progress",description:"Monitor your improvement with detailed analytics"},{icon:n,title:"Compete & Learn",description:"Join thousands of medical students worldwide"}];return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary-600 via-secondary-600 to-purple-700 flex items-center justify-center p-4",children:e.jsxs("div",{className:"max-w-md mx-auto text-center",children:[e.jsx(t.div,{initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0},transition:{type:"spring",stiffness:260,damping:20,duration:1.2},className:"mb-8",children:e.jsxs("div",{className:"relative mx-auto w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30",children:[e.jsx(i,{className:"w-10 h-10 text-white"}),e.jsx("div",{className:"absolute -top-2 -right-2",children:e.jsx("div",{className:"w-6 h-6 bg-accent-400 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-xs text-white font-bold",children:"+"})})})]})}),e.jsxs(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5,duration:.6},className:"mb-8",children:[e.jsxs("h1",{className:"text-4xl font-bold text-white mb-4",children:["Welcome to",e.jsx("br",{}),e.jsx("span",{className:"text-accent-300",children:"USMLE Trivia"})]}),e.jsx("p",{className:"text-white/80 text-lg",children:"Master medicine, one question at a time"})]}),e.jsx(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8,duration:.6},className:"space-y-4 mb-8",children:o.map((i,s)=>{const a=i.icon;return e.jsxs(t.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:1+.2*s},className:"flex items-center space-x-4 bg-white/10 rounded-xl p-4 backdrop-blur-sm border border-white/20",children:[e.jsx("div",{className:"w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center",children:e.jsx(a,{className:"w-5 h-5 text-white"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("h3",{className:"font-semibold text-white",children:i.title}),e.jsx("p",{className:"text-white/70 text-sm",children:i.description})]})]},i.title)})}),e.jsxs(t.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.8,duration:.6},className:"space-y-3",children:[e.jsx(r,{to:"/signup",className:"block w-full bg-white text-primary-600 rounded-xl py-4 px-6 font-semibold hover:bg-gray-50 transition-colors shadow-lg",children:"Create Account"}),e.jsx(r,{to:"/login",className:"block w-full bg-white/20 text-white rounded-xl py-4 px-6 font-semibold hover:bg-white/30 transition-colors backdrop-blur-sm border border-white/30",children:"Sign In"})]}),e.jsx(t.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:2.2,duration:.6},className:"mt-8 text-white/60 text-sm",children:e.jsx("p",{children:"Join over 10,000+ medical students"})})]})})};export{o as default};
