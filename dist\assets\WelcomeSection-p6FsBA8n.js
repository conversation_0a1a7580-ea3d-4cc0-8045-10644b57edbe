import{u as e,j as t,m as i,aw as a,az as s,b0 as n}from"./vendor-DfmD63KD.js";const o=({user:o,profile:l,isNewUser:r})=>{const c=e(),d=(()=>{const e=(new Date).getHours(),t=e<12?"morning":e<17?"afternoon":"evening",i=l?.display_name?.split(" ")[0]||o?.email?.split("@")[0]||"Student";if(r)return{greeting:`Good ${t}, ${i}! 👋`,message:"Welcome to your USMLE Step 1 journey! Ready to start your first quiz?",cta:"Start Your First Quiz",ctaAction:()=>c("/quick-quiz"),motivation:"Every expert was once a beginner. Let's build your medical knowledge together!"};{const e=["Time to level up your medical knowledge!","Every question brings you closer to your goal!","Your dedication is paying off - keep going!","Ready to tackle some challenging questions?","Let's continue building your expertise!"];return{greeting:`Good ${t}, ${i}! 🩺`,message:e[Math.floor(Math.random()*e.length)],cta:"Continue Learning",ctaAction:()=>c("/quiz"),motivation:"Consistency is key to mastering medicine. You've got this!"}}})();return t.jsxs(i.div,{className:"bg-gradient-to-r from-blue-600 to-purple-700 rounded-xl p-6 md:p-8 text-white mb-6 relative overflow-hidden",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-700/20",children:t.jsx("div",{className:"absolute inset-0",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")"}})}),t.jsx("div",{className:"relative z-10",children:t.jsxs("div",{className:"flex items-start justify-between",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx(i.h1,{className:"text-2xl md:text-3xl font-bold mb-2",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.1},children:d.greeting}),t.jsx(i.p,{className:"text-blue-100 mb-4 text-lg",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},children:d.message}),t.jsx(i.p,{className:"text-blue-200 text-sm mb-6",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.3},children:d.motivation}),t.jsxs(i.button,{onClick:d.ctaAction,className:"bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors duration-200 flex items-center space-x-2 shadow-lg",whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},children:[t.jsx(a,{className:"w-5 h-5"}),t.jsx("span",{children:d.cta})]})]}),t.jsxs("div",{className:"hidden md:flex flex-col items-center space-y-4 ml-8",children:[t.jsx("div",{className:"p-3 bg-white/10 rounded-full",children:r?t.jsx(s,{className:"w-8 h-8 text-yellow-300"}):t.jsx(n,{className:"w-8 h-8 text-blue-200"})}),t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"text-sm text-blue-200 font-medium",children:r?"New Learner":"Active Learner"}),t.jsx("div",{className:"text-xs text-blue-300",children:r?"Welcome aboard!":"Keep it up!"})]})]})]})})]})};export{o as default};
