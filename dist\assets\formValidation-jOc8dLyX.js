import{r as e,j as r,a_ as s,Z as a,A as t,m as i,C as l,l as o}from"./vendor-DfmD63KD.js";const n=({type:n="text",name:d,value:c,onChange:u,onBlur:h,placeholder:x,label:g,icon:m,error:b,isValid:p,required:y=!1,disabled:f=!1,className:j="",showPasswordToggle:V=!1,autoComplete:N,...v})=>{const[w,k]=e.useState(!1),[C,$]=e.useState(!1),P=c&&c.toString().trim().length>0,A=P||b,S="password"===n&&C?"text":n;return r.jsxs("div",{className:"space-y-2",children:[g&&r.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[g,y&&r.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),r.jsxs("div",{className:"relative",children:[m&&r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(m,{className:"w-5 h-5 text-gray-400"})}),r.jsx("input",{type:S,name:d,value:c,onChange:e=>u(d,e.target.value),onBlur:()=>{k(!1),h&&h(d)},onFocus:()=>k(!0),placeholder:x,disabled:f,autoComplete:N,className:(()=>{let e=`\n      w-full px-4 py-3 rounded-xl border transition-all duration-200\n      focus:outline-none focus:ring-2 focus:ring-offset-2\n      dark:bg-gray-800 dark:border-gray-600\n      ${m?"pl-12":""}\n      ${V||A?"pr-12":""}\n      ${j}\n    `;return e+=b?" border-red-300 focus:border-red-500 focus:ring-red-500 bg-red-50 dark:bg-red-900/20":p&&P?" border-green-300 focus:border-green-500 focus:ring-green-500 bg-green-50 dark:bg-green-900/20":w?" border-blue-300 focus:border-blue-500 focus:ring-blue-500 bg-blue-50 dark:bg-blue-900/20":" border-gray-300 focus:border-blue-500 focus:ring-blue-500 bg-white",f&&(e+=" opacity-50 cursor-not-allowed"),e})(),...v}),r.jsxs("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center space-x-2",children:["password"===n&&V&&r.jsx("button",{type:"button",onClick:()=>$(!C),className:"text-gray-400 hover:text-gray-600 focus:outline-none",tabIndex:-1,children:C?r.jsx(s,{className:"w-5 h-5"}):r.jsx(a,{className:"w-5 h-5"})}),A?b?r.jsx(l,{className:"w-5 h-5 text-red-500"}):p&&P?r.jsx(o,{className:"w-5 h-5 text-green-500"}):null:null]})]}),r.jsx(t,{children:b&&r.jsxs(i.div,{initial:{opacity:0,y:-10,height:0},animate:{opacity:1,y:0,height:"auto"},exit:{opacity:0,y:-10,height:0},transition:{duration:.2},className:"text-sm text-red-600 dark:text-red-400 flex items-center space-x-1",children:[r.jsx(l,{className:"w-4 h-4 flex-shrink-0"}),r.jsx("span",{children:b})]})}),r.jsx(t,{children:p&&P&&!b&&r.jsxs(i.div,{initial:{opacity:0,y:-10,height:0},animate:{opacity:1,y:0,height:"auto"},exit:{opacity:0,y:-10,height:0},transition:{duration:.2},className:"text-sm text-green-600 dark:text-green-400 flex items-center space-x-1",children:[r.jsx(o,{className:"w-4 h-4 flex-shrink-0"}),r.jsx("span",{children:"Looks good!"})]})})]})},d=e=>e?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?{isValid:!0,error:""}:{isValid:!1,error:"Please enter a valid email address"}:{isValid:!1,error:""},c=e=>{if(!e)return{isValid:!1,error:""};if(e.length<6)return{isValid:!1,error:"Password must be at least 6 characters"};const r=/[a-zA-Z]/.test(e),s=/\d/.test(e);return r&&s?{isValid:!0,error:"",level:"strong"}:{isValid:!1,error:"Password should contain both letters and numbers",level:"weak"}},u=(e,r)=>r?e!==r?{isValid:!1,error:"Passwords do not match"}:{isValid:!0,error:""}:{isValid:!1,error:""},h=e=>{if(!e)return{isValid:!1,error:""};const r=e.trim();return r.length<2?{isValid:!1,error:"Name must be at least 2 characters"}:/^[a-zA-Z\s'-]+$/.test(r)?{isValid:!0,error:""}:{isValid:!1,error:"Name can only contain letters, spaces, hyphens, and apostrophes"}},x=(e,r)=>e&&e.toString().trim()?{isValid:!0,error:""}:{isValid:!1,error:`${r} is required`},g=e=>({validateField:(r,s,a={})=>e[r]?e[r](s,a):{isValid:!0,error:""},validateAll:r=>{const s={};let a=!0;return Object.keys(e).forEach(t=>{const i=e[t](r[t],r);i.isValid||(s[t]=i.error,a=!1)}),{isValid:a,errors:s}}});export{n as V,d as a,u as b,g as c,c as d,h as e,x as v};
