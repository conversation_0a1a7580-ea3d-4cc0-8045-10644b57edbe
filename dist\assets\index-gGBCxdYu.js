const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Home-BcI44W-m.js","assets/vendor-DfmD63KD.js","assets/useViewTransitions-2OP18fwt.js","assets/QuizTab-BiuxmGKi.js","assets/Profile-B3-isjiX.js","assets/chatService-BoDF_oo_.js","assets/Leaderboard-BiS9ssft.js","assets/Learn-BCkyiAhC.js","assets/QuickQuiz-DzKMkY9F.js","assets/QuizError-Mp16REcC.js","assets/TimedTest-BgzNRu-r.js","assets/TimedTestSetup-CP2ciNQ3.js","assets/Results-CrUtkTSt.js","assets/CustomQuiz-CsC8T2nG.js","assets/CustomQuizSetup-P2D1dkYp.js","assets/Login-Bs3CmNZi.js","assets/formValidation-jOc8dLyX.js","assets/SignUp-7rcT1V5H.js","assets/ForgotPassword-BVoEhR5k.js","assets/Welcome-Dx_SOJiM.js","assets/Privacy-DLpaQhVa.js","assets/Terms-0ZT972W1.js"])))=>i.map(i=>d[i]);
import{Q as e,p as t,c as r,r as s,j as a,T as i,R as n,H as o,L as l,D as c,C as d,a as u,B as h,u as m,m as g,M as x,b as p,d as f,S as y,e as b,U as w,f as j,g as N,G as v,X as k,h as _,i as E,k as T,N as C,A as R,I as S,l as I,_ as A,n as O,o as P,q as z,s as q,t as U}from"./vendor-DfmD63KD.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const D=new e({defaultOptions:{queries:{staleTime:12e4,gcTime:6e5,refetchOnWindowFocus:!0,refetchOnReconnect:!0,refetchOnMount:"always",retry:(e,t)=>!(t?.status>=400&&t?.status<500)&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4)},mutations:{retry:1,onError:e=>{console.error("Mutation error:",e)}}}}),L={persistClient:async e=>{try{const t=JSON.stringify({clientState:e,timestamp:Date.now()});localStorage.setItem("REACT_QUERY_OFFLINE_CACHE",t)}catch(t){console.warn("Failed to persist query cache:",t)}},restoreClient:async()=>{try{const e=localStorage.getItem("REACT_QUERY_OFFLINE_CACHE");if(!e)return;const{clientState:t,timestamp:r}=JSON.parse(e),s=864e5;return Date.now()-r>s?void localStorage.removeItem("REACT_QUERY_OFFLINE_CACHE"):t}catch(e){return console.warn("Failed to restore query cache:",e),void localStorage.removeItem("REACT_QUERY_OFFLINE_CACHE")}},removeClient:async()=>{localStorage.removeItem("REACT_QUERY_OFFLINE_CACHE")}};t({queryClient:D,persister:L,maxAge:864e5,buster:"1.0.0"});const F="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJrdW93b293bG13cmFuZm9saWVhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyMDA4NTQsImV4cCI6MjA2NTc3Njg1NH0.lNbEJRUEOl3nVtRPNqKsJu4w031DHae5bjOxZIFlSpI",$=r("https://bkuowoowlmwranfoliea.supabase.co",F,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}}),Q=Boolean(F);class H extends Error{constructor(e){super(e),this.name="AuthError"}}const M={signUp:async(e,t,r)=>{const{data:s,error:a}=await $.auth.signUp({email:e,password:t,options:{data:{full_name:r}}});if(a)throw new H(a.message);if(s.user&&s.user.identities&&0===s.user.identities.length)throw new H("This email is already in use. Please try another email.");return s},signIn:async(e,t)=>{const{data:r,error:s}=await $.auth.signInWithPassword({email:e,password:t});if(s)throw new H(s.message);return r},signOut:async()=>{const{error:e}=await $.auth.signOut();if(e)throw new H(e.message)},resetPassword:async e=>{const{error:t}=await $.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/reset-password`});if(t)throw new H(t.message)},updateProfile:async e=>{const{data:{user:t}}=await $.auth.getUser();if(!t)throw new H("User not authenticated");const{data:r,error:s}=await $.from("profiles").update(e).eq("id",t.id).select().single();if(s)throw new H(s.message);return r}},W={ERROR:0,WARN:1,INFO:2,DEBUG:3,TRACE:4},G={ERROR:"#ff4444",WARN:"#ffaa00",INFO:"#4488ff",DEBUG:"#888888",TRACE:"#cccccc"};const V=new class{constructor(){this.level=W.INFO,this.context={}}setContext(e){this.context={...this.context,...e}}clearContext(){this.context={}}_shouldLog(e){return W[e]<=this.level}_formatMessage(e,t,r={}){return{timestamp:(new Date).toISOString(),level:e,message:t,context:{...this.context,...r},userAgent:navigator?.userAgent||"unknown",url:window?.location?.href||"unknown"}}_logToConsole(e,t){const{timestamp:r,message:s,context:a}=t,i=G[e],n=`%c[${e}] ${r.split("T")[1].split(".")[0]}`;Object.keys(a).length>0?(console.groupCollapsed(n,`color: ${i}; font-weight: bold`,s),console.table(a),console.groupEnd()):console.log(n,`color: ${i}; font-weight: bold`,s)}_logToRemote(e){e.level}error(e,t={},r=null){if(!this._shouldLog("ERROR"))return;const s={...t,...r&&{errorName:r.name,errorMessage:r.message,errorStack:r.stack}},a=this._formatMessage("ERROR",e,s);return this._logToConsole("ERROR",a),this._logToRemote(a),a}warn(e,t={}){if(!this._shouldLog("WARN"))return;const r=this._formatMessage("WARN",e,t);return this._logToConsole("WARN",r),r}info(e,t={}){if(!this._shouldLog("INFO"))return;const r=this._formatMessage("INFO",e,t);return this._logToConsole("INFO",r),r}debug(e,t={}){if(!this._shouldLog("DEBUG"))return;const r=this._formatMessage("DEBUG",e,t);return this._logToConsole("DEBUG",r),r}trace(e,t={}){if(!this._shouldLog("TRACE"))return;const r=this._formatMessage("TRACE",e,t);return this._logToConsole("TRACE",r),r}success(e,t={}){if(!this._shouldLog("INFO"))return;const r=this._formatMessage("INFO",e,t),{timestamp:s,context:a}=r,i=`%c[SUCCESS] ${s.split("T")[1].split(".")[0]}`;return Object.keys(a).length>0?(console.groupCollapsed(i,"color: #22c55e; font-weight: bold",e),console.table(a),console.groupEnd()):console.log(i,"color: #22c55e; font-weight: bold",e),r}time(e){return console.time(e),e}timeEnd(e,t={}){console.timeEnd(e),this.debug(`Performance: ${e} completed`,t)}quizStart(e,t,r={}){this.setContext({userId:t,quizType:e}),this.info("Quiz session started",{quizType:e,userId:t,config:r,sessionId:r.sessionId})}quizError(e,t,r={}){this.error(`Quiz operation failed: ${e}`,r,t)}questionFetch(e,t,r=null){const s={operation:e,questionsFound:t?.length||0,...r&&{duration:r}};0===t?.length?this.warn("No questions found for fetch operation",s):this.info("Questions fetched successfully",s)}userHistoryUpdate(e,t,r=null){t?this.debug("User history updated",{questionId:e}):this.warn("User history update failed",{questionId:e},r)}fallbackUsed(e,t,r){this.warn("Fallback strategy used",{primaryOperation:e,fallbackOperation:t,reason:r})}},Y=async(e,t)=>{const r=performance.now(),s=`${e}_${Date.now()}_${Math.random().toString(36).substr(2,5)}`;V.time(s);try{const e=await t(),a=performance.now()-r;return V.timeEnd(s,{duration:`${a.toFixed(2)}ms`}),e}catch(a){const e=performance.now()-r;throw V.timeEnd(s,{duration:`${e.toFixed(2)}ms`,error:!0}),a}},B={FAST:8e3,STANDARD:12e3,CRITICAL:3e4},K={maxAttempts:3,baseDelay:1e3,maxDelay:1e4,backoffFactor:2},J=async(e,t={})=>{const{timeout:r=B.STANDARD,retries:s=K.maxAttempts,retryDelay:a=K.baseDelay,queryType:i="unknown",fallback:n=null,onRetry:o=null}=t;let l=null;for(let d=1;d<=s;d++)try{const t=new Promise((e,t)=>{setTimeout(()=>{t(new Error(`Query timeout after ${r}ms (${i})`))},r)}),s=await Promise.race([e(),t]);return d>1&&V.success(`Query succeeded on attempt ${d}`,{queryType:i,attempt:d,timeout:r}),s}catch(c){if(l=c,V.warn(`Query attempt ${d} failed`,{queryType:i,attempt:d,error:c.message,timeout:r,willRetry:d<s}),d>=s)break;const e=Math.min(a*Math.pow(K.backoffFactor,d-1),K.maxDelay);o&&o(d,c,e),await new Promise(t=>setTimeout(t,e))}if(V.error(`Query failed after ${s} attempts`,{queryType:i,timeout:r,finalError:l?.message},l),null!==n)return V.info("Using fallback value for failed query",{queryType:i,fallback:n}),n;throw l},Z=(e,t={})=>J(e,{timeout:B.STANDARD,retries:3,queryType:"user-data",fallback:null,...t}),X=(e,t={})=>J(e,{timeout:B.FAST,retries:3,queryType:"categories",fallback:[],...t}),ee=s.createContext(),te=()=>{const e=s.useContext(ee);if(!e)throw new Error("useAuth must be used within an AuthProvider");return e},re=({children:e})=>{const[t,r]=s.useState(null),[i,n]=s.useState(!0),[o,l]=s.useState(null);s.useEffect(()=>{if(!Q)return V.warn("Supabase not configured. Authentication will not work."),void n(!1);n(!0);const{data:{subscription:e}}=$.auth.onAuthStateChange(async(e,t)=>{const s=setTimeout(()=>{V.warn(`[AuthContext:onAuthStateChange] Auth state change handler timeout for event: ${e}`),n(!1)},15e3);try{V.debug(`[AuthContext:onAuthStateChange] Auth event: ${e}`,{session:t});const s=t?.user;r(s??null),s?await d(s.id,e):l(null)}catch(a){V.error("[AuthContext:onAuthStateChange] Error in auth state change handler:",{error:a,event:e,message:a.message}),l(null)}finally{clearTimeout(s),n(!1)}});return c(),()=>{e?.unsubscribe()}},[]);const c=async()=>{try{V.debug("[AuthContext:getInitialSession] Triggering initial session check..."),await $.auth.getSession()}catch(e){V.error("[AuthContext:getInitialSession] Error triggering initial session check:",{error:e,message:e.message}),n(!1)}},d=async(e,t="unknown")=>{if(!e)return V.error(`[AuthContext:fetchUserProfile] Called with undefined/null userId (context: ${t})`),null;V.debug(`[AuthContext:fetchUserProfile] Fetching user profile for ID: ${e} (context: ${t})`);try{return await((e,t={})=>J(e,{timeout:B.CRITICAL,retries:1,queryType:"authentication",fallback:null,...t}))(async()=>{const{data:r,error:s}=await $.from("profiles").select("*").eq("id",e).maybeSingle();if(s){if(V.error(`[AuthContext:fetchUserProfile] Error in initial profile fetch (context: ${t}):`,{error:s,userId:e,code:s.code,message:s.message,details:s.details}),"PGRST116"===s.code)return V.warn(`[AuthContext:fetchUserProfile] No profile found for user (context: ${t})`,{userId:e}),l(null),null;if("PGRST301"===s.code||"42501"===s.code)return V.error(`[AuthContext:fetchUserProfile] Permission denied (RLS) (context: ${t})`,{userId:e}),l(null),null;throw s}if(r)return V.info(`[AuthContext:fetchUserProfile] Profile found and loaded (context: ${t}):`,{userId:e,profileId:r.id,email:r.email,hasCountry:!!r.countries,hasGrade:!!r.achievement_grades}),l(r),r;{V.warn(`[AuthContext:fetchUserProfile] No profile found for user, attempting to create... (context: ${t})`,e),await u(e),V.debug(`[AuthContext:fetchUserProfile] Retrying profile fetch after creation... (context: ${t})`);const r=$.from("profiles").select("*").eq("id",e).maybeSingle(),{data:s,error:a}=await Promise.race([r,new Promise((e,t)=>setTimeout(()=>t(new Error("Retry fetch timeout")),1e4))]);if(a){if(V.error(`[AuthContext:fetchUserProfile] Error in retry profile fetch (context: ${t}):`,{error:a,userId:e,code:a.code,message:a.message}),"PGRST116"===a.code)return V.warn(`[AuthContext:fetchUserProfile] No profile found for user after creation (context: ${t})`,{userId:e}),l(null),null;if("PGRST301"===a.code||"42501"===a.code)return V.error(`[AuthContext:fetchUserProfile] Permission denied (RLS) after creation (context: ${t})`,{userId:e}),l(null),null;throw a}return s?V.info(`[AuthContext:fetchUserProfile] Profile successfully created and fetched (context: ${t}):`,{userId:e,profileId:s.id,email:s.email}):V.error(`[AuthContext:fetchUserProfile] Profile creation appeared to succeed but profile still not found (context: ${t})`),l(s),s}},{queryType:`profile-${t}`,fallback:null})}catch(r){const s=JSON.stringify(r,Object.getOwnPropertyNames(r));return V.error(`[AuthContext:fetchUserProfile] Unexpected error (context: ${t}): errorString=${s}`,{error:r,userId:e,errorType:r.constructor?.name,message:r.message,code:r.code,details:r.details,stack:r.stack}),l(null),null}},u=async e=>{V.debug("[AuthContext] Creating user profile for:",e);try{V.debug("[AuthContext] Getting authenticated user data...");const{data:{user:t},error:r}=await $.auth.getUser();if(r)throw V.error("[AuthContext] Error getting authenticated user:",r),r;if(!t){const e=new Error("No authenticated user found");throw V.error("[AuthContext] No authenticated user:",e),e}V.info("[AuthContext] Authenticated user found:",{id:t.id,email:t.email,metadata:t.user_metadata}),V.debug("[AuthContext] Attempting to manually trigger profile creation...");const{error:s}=await $.rpc("create_user_profile_manual",{user_id:e,user_email:t.email,user_display_name:t.user_metadata?.display_name||t.email?.split("@")[0]||"User",user_full_name:t.user_metadata?.full_name||t.email?.split("@")[0]||"User"});if(s){V.warn("[AuthContext] RPC function not available, trying direct insert with SECURITY DEFINER context..."),V.debug("[AuthContext] Getting default country...");const{data:r,error:s}=await $.from("countries").select("id").eq("code","US").maybeSingle();s&&V.error("[AuthContext] Error getting default country:",s),V.debug("[AuthContext] Default country found:",r);const a={id:e,email:t.email,display_name:t.user_metadata?.display_name||t.email?.split("@")[0]||"User",full_name:t.user_metadata?.full_name||t.email?.split("@")[0]||"User",country_id:r?.id||null,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()};V.debug("[AuthContext] Attempting profile insert with data:",a);const{error:i}=await $.from("profiles").insert(a);if(i)throw V.error("[AuthContext] Profile insert failed:",{error:i,code:i.code,message:i.message,details:i.details,hint:i.hint,profileData:a}),i}V.info("[AuthContext] Profile created successfully for user:",e)}catch(t){throw V.error("[AuthContext] Critical error creating profile:",{error:t,userId:e,errorType:t.constructor.name,code:t.code,message:t.message,details:t.details,hint:t.hint,stack:t.stack}),t}},h={user:t,profile:o,loading:i,signOut:async()=>{await M.signOut(),r(null),l(null)},refreshProfile:()=>t?.id?d(t.id,"refreshProfile"):V.warn("[AuthContext:refreshProfile] Tried to refresh profile with missing user id"),isConfigured:Q};return a.jsx(ee.Provider,{value:h,children:e})},se=s.createContext(),ae=({children:e})=>{const[t,r]=s.useState(()=>{const e=localStorage.getItem("theme");return e?"dark"===e:window.matchMedia("(prefers-color-scheme: dark)").matches});s.useEffect(()=>{localStorage.setItem("theme",t?"dark":"light"),t?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")},[t]);return a.jsx(se.Provider,{value:{isDarkMode:t,toggleTheme:()=>{r(e=>!e)}},children:e})};class ie extends s.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null,retryCount:0}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({errorInfo:t}),V.error("Uncaught error in React component",{errorInfo:t,componentStack:t.componentStack,errorBoundary:this.props.fallbackComponent||"default",retryCount:this.state.retryCount},e)}handleRetry=()=>{this.setState(e=>({hasError:!1,error:null,errorInfo:null,retryCount:e.retryCount+1}))};handleGoHome=()=>{window.location.href="/"};render(){if(this.state.hasError){if(this.props.fallback)return this.props.fallback(this.state.error,this.handleRetry);const{error:e}=this.state,t=e?.message?.includes("fetch")||e?.message?.includes("Network"),r=e?.message?.includes("auth")||e?.message?.includes("unauthorized");return a.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900 p-4",children:a.jsxs("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg text-center max-w-md w-full",children:[a.jsxs("div",{className:"mb-6",children:[a.jsx(i,{className:"w-16 h-16 text-red-500 mx-auto mb-4"}),a.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:t?"Connection Error":r?"Authentication Error":"Something went wrong"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:t?"Please check your internet connection and try again.":r?"Please sign in again to continue.":"We encountered an unexpected error. Please try again."}),!1]}),a.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[a.jsxs("button",{onClick:this.handleRetry,className:"flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",disabled:this.state.retryCount>=3,children:[a.jsx(n,{className:"w-4 h-4"}),this.state.retryCount>=3?"Max retries reached":"Try Again"]}),a.jsxs("button",{onClick:this.handleGoHome,className:"flex items-center justify-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:[a.jsx(o,{className:"w-4 h-4"}),"Go Home"]})]})]})})}return this.props.children}}class ne extends s.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({errorInfo:t}),V.error("Authentication error boundary triggered",{errorInfo:t,authFlow:this.props.authFlow||"unknown",userAgent:navigator.userAgent},e)}handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null})};handleGoToLogin=()=>{window.location.href="/login"};render(){if(this.state.hasError){const{error:e}=this.state,t=e?.message?.includes("session")||e?.message?.includes("token"),r=e?.message?.includes("fetch")||e?.message?.includes("Network");return a.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4",children:a.jsxs("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-xl shadow-xl text-center max-w-md w-full",children:[a.jsxs("div",{className:"mb-6",children:[a.jsx(i,{className:"w-12 h-12 text-orange-500 mx-auto mb-4"}),a.jsx("h1",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:t?"Session Expired":r?"Connection Error":"Authentication Error"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:t?"Your session has expired. Please sign in again.":r?"Unable to connect to our servers. Please check your connection.":"We encountered an issue with authentication. Please try again."})]}),a.jsxs("div",{className:"flex flex-col gap-3",children:[!t&&a.jsxs("button",{onClick:this.handleRetry,className:"flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:[a.jsx(n,{className:"w-4 h-4"}),"Try Again"]}),a.jsxs("button",{onClick:this.handleGoToLogin,className:"flex items-center justify-center gap-2 bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:[a.jsx(l,{className:"w-4 h-4"}),t?"Sign In Again":"Go to Login"]})]})]})})}return this.props.children}}class oe extends s.Component{constructor(e){super(e),this.state={hasError:!1,error:null,retryCount:0,lastQueryKey:e.queryKey||null}}static getDerivedStateFromProps(e,t){return e.queryKey&&e.queryKey!==t.lastQueryKey?{hasError:!1,error:null,retryCount:0,lastQueryKey:e.queryKey}:null}static getDerivedStateFromError(e){return"AbortError"===e?.name||e?.message?.includes("AbortError")||e?.message?.includes("cancelled")||e?.message?.includes("Network request failed")&&e?.message?.includes("timeout")?{hasError:!1,error:null}:{hasError:!0,error:e}}componentDidCatch(e,t){e?.name?.includes("AbortError")||e?.message?.includes("cancelled")||e?.message?.includes("timeout")||V.error("Query error boundary triggered",{errorInfo:t,queryType:this.props.queryType||"unknown",retryCount:this.state.retryCount,errorType:e?.name||"Unknown",isNetworkError:e?.message?.includes("fetch")||e?.message?.includes("Network")},e)}handleRetry=()=>{const{error:e}=this.state,t=e?.message?.includes("fetch")||e?.message?.includes("Network"),r=e?.message?.includes("timeout")||e?.message?.includes("Timeout");setTimeout(()=>{this.setState(e=>({hasError:!1,error:null,retryCount:e.retryCount+1})),this.props.onRetry&&this.props.onRetry()},t||r?1e3:0)};render(){if(this.state.hasError){if(this.props.fallback)return this.props.fallback(this.state.error,this.handleRetry);const{error:e}=this.state,t=e?.message?.includes("timeout")||e?.message?.includes("Timeout"),r=e?.message?.includes("fetch")||e?.message?.includes("Network"),s=this.state.retryCount<3;return a.jsx("div",{className:"flex flex-col items-center justify-center p-8 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700",children:a.jsxs("div",{className:"text-center max-w-sm",children:[a.jsxs("div",{className:"mb-4",children:[r?a.jsx(c,{className:"w-12 h-12 text-red-500 mx-auto mb-3"}):a.jsx(d,{className:"w-12 h-12 text-orange-500 mx-auto mb-3"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:t?"Request Timeout":r?"Connection Error":"Data Loading Error"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4",children:t?"The request took too long to complete.":r?"Unable to connect to the server.":"Failed to load the requested data."})]}),s&&a.jsxs("button",{onClick:this.handleRetry,className:"flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors mx-auto",children:[a.jsx(n,{className:"w-4 h-4"}),"Try Again"]}),!s&&a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Maximum retry attempts reached. Please refresh the page."})]})})}return this.props.children}}class le extends s.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({errorInfo:t}),V.error("Quiz error boundary triggered",{errorInfo:t,quizType:this.props.quizType||"unknown",questionId:this.props.questionId,sessionId:this.props.sessionId},e)}handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null})};handleGoHome=()=>{window.location.href="/"};handleGoToQuizTab=()=>{window.location.href="/quiz"};render(){if(this.state.hasError){const{error:e}=this.state,t=e?.message?.includes("question")||e?.message?.includes("Question"),r=e?.message?.includes("session")||e?.message?.includes("Session");return a.jsx("div",{className:"flex flex-col items-center justify-center min-h-[400px] bg-gradient-to-br from-purple-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 rounded-xl p-8",children:a.jsxs("div",{className:"bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg text-center max-w-md w-full",children:[a.jsxs("div",{className:"mb-6",children:[a.jsx(i,{className:"w-16 h-16 text-purple-500 mx-auto mb-4"}),a.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:t?"Question Loading Error":r?"Quiz Session Error":"Quiz Error"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:t?"Unable to load the current question. This might be a temporary issue.":r?"There was an issue with your quiz session. Your progress may not be saved.":"An unexpected error occurred during the quiz. Don't worry, your progress is safe."})]}),a.jsxs("div",{className:"flex flex-col gap-3",children:[a.jsxs("button",{onClick:this.handleRetry,className:"flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:[a.jsx(u,{className:"w-4 h-4"}),"Try Again"]}),a.jsxs("button",{onClick:this.handleGoToQuizTab,className:"flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:[a.jsx(h,{className:"w-4 h-4"}),"Back to Quiz Selection"]}),a.jsxs("button",{onClick:this.handleGoHome,className:"flex items-center justify-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:[a.jsx(o,{className:"w-4 h-4"}),"Go Home"]})]})]})})}return this.props.children}}const ce="https://bkuowoowlmwranfoliea.supabase.co";function de(e,t={}){if(!e)return e||"https://via.placeholder.com/150";const r=new URL(e);if(!r.hostname.endsWith("supabase.co")||r.searchParams.has("width"))return e;const{width:s,height:a,quality:i=80,resize:n="cover"}=t,o=new URLSearchParams;s&&o.append("width",s),a&&o.append("height",a),o.append("quality",i),o.append("resize",n);const l=r.pathname.split("/"),c=l[l.length-2],d=l[l.length-1];if(!c||!d)return e;return`${new URL(`/storage/v1/render/image/public/${c}/${d}`,ce).href}?${o.toString()}`}const ue=({onSidebarToggle:e,showSidebarToggle:t=!1,isCondensed:r=!1,user:i,profile:n})=>{const o=m(),{isDarkMode:l,toggleTheme:c}=(()=>{const e=s.useContext(se);if(!e)throw new Error("useTheme must be used within a ThemeProvider");return e})(),d=e=>{navigator.vibrate&&navigator.vibrate(5),e()};return a.jsx(g.header,{initial:{y:-50,opacity:0},animate:{y:0,opacity:1},className:"bg-white/98 dark:bg-gray-900/98 backdrop-blur-xl shadow-sm border-b border-gray-200/30 dark:border-gray-700/30 sticky top-0 z-40 transition-all duration-300 py-2",style:{paddingTop:r?"8px":"max(8px, env(safe-area-inset-top))",paddingLeft:"env(safe-area-inset-left)",paddingRight:"env(safe-area-inset-right)"},children:a.jsx("div",{className:"flex items-center justify-end px-3 w-full "+(r?"max-w-none":"max-w-full"),children:a.jsxs("div",{className:"flex items-center gap-1",children:[t&&a.jsx(g.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:e,className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-xl transition-all duration-200",children:a.jsx(x,{size:20,className:"text-gray-600 dark:text-gray-300"})}),a.jsxs(g.div,{whileHover:{scale:1.05},whileTap:{scale:.95},initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{delay:.2},className:"flex items-center gap-1 bg-gradient-to-r from-orange-500 via-orange-600 to-red-500 text-white px-2.5 py-1 rounded-full text-xs font-bold shadow-lg shadow-orange-500/25",children:[a.jsx(g.div,{animate:{rotate:[0,-10,10,-5,5,0],scale:[1,1.1,1]},transition:{duration:2,repeat:1/0,repeatDelay:3},children:a.jsx(p,{size:12,className:"drop-shadow-sm"})}),a.jsx("span",{className:"tracking-tight",children:n?.total_points?.toLocaleString()||"0"})]}),a.jsxs(g.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>d(()=>{}),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-xl transition-all duration-200 relative active:bg-gray-200 dark:active:bg-gray-700",children:[a.jsx(f,{size:16,className:"text-gray-600 dark:text-gray-300",strokeWidth:2}),a.jsx(g.div,{animate:{scale:[1,1.2,1],opacity:[1,.8,1]},transition:{duration:2,repeat:1/0,repeatDelay:1},className:"absolute top-1.5 right-1.5 w-2 h-2 bg-red-500 rounded-full shadow-sm"})]}),a.jsx(g.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>d(c),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-xl transition-all duration-200 active:bg-gray-200 dark:active:bg-gray-700",children:a.jsx(g.div,{animate:{rotate:l?180:0},transition:{type:"spring",stiffness:200,damping:15},children:l?a.jsx(y,{size:16,className:"text-yellow-500 drop-shadow-sm",strokeWidth:2}):a.jsx(b,{size:16,className:"text-gray-600 dark:text-gray-300",strokeWidth:2})})}),a.jsxs(g.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>d(()=>o("/profile")),className:"relative",children:[a.jsx("div",{className:"w-8 h-8 rounded-full overflow-hidden shadow-lg ring-2 ring-white/50 dark:ring-gray-700/50 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center",children:i?.avatar_url?a.jsx("img",{src:de(i.avatar_url,{width:80,height:80}),alt:"Profile",className:"w-full h-full object-cover transition-transform duration-300 hover:scale-110"}):a.jsx(w,{size:18,className:"text-white"})}),a.jsx("div",{className:"absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-green-500 rounded-full border-2 border-white dark:border-gray-900 shadow-sm"})]})]})})})},he=()=>{const e=j(),t=m(),r=[{icon:o,label:"Home",path:"/",color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-500",gradient:"from-blue-500 to-blue-600"},{icon:h,label:"Learn",path:"/learn",color:"text-emerald-600 dark:text-emerald-400",bgColor:"bg-emerald-500",gradient:"from-emerald-500 to-emerald-600"},{icon:N,label:"Quiz",path:"/quiz",color:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-500",gradient:"from-purple-500 to-purple-600"},{icon:p,label:"Leaders",path:"/leaderboard",color:"text-orange-600 dark:text-orange-400",bgColor:"bg-orange-500",gradient:"from-orange-500 to-orange-600"}];return a.jsx("div",{className:"fixed bottom-0 left-0 right-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-t border-gray-200/50 dark:border-gray-700/50 px-1 py-0.5 z-50",children:a.jsx("div",{className:"flex justify-around items-center max-w-md mx-auto",children:r.map(r=>{const s=r.icon,i="/"===(n=r.path)?"/"===e.pathname:e.pathname.startsWith(n);var n;return a.jsxs(g.button,{onClick:()=>t(r.path),className:"relative flex flex-col items-center justify-center px-1.5 py-1 min-w-0 flex-1",whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:17},children:[a.jsx(g.div,{className:"p-1 rounded-md transition-all duration-200",animate:{scale:i?1.05:1,y:i?-1:0},transition:{type:"spring",stiffness:400,damping:17},children:a.jsx(s,{className:`w-4 h-4 transition-colors duration-200 ${i?r.color:"text-gray-500 dark:text-gray-400"}`})}),a.jsx("span",{className:`text-[9px] font-medium mt-0.5 transition-colors duration-200 ${i?r.color:"text-gray-500 dark:text-gray-400"}`,children:r.label})]},r.path)})})})},me=({isOpen:e,onToggle:t,isTablet:r})=>{const s=j(),i=m(),{user:n,profile:l}=te(),c=[{icon:o,label:"Home",path:"/",color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-500",gradient:"from-blue-500 to-blue-600"},{icon:h,label:"Quiz",path:"/quiz",color:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-500",gradient:"from-purple-500 to-purple-600"},{icon:v,label:"Learn",path:"/learn",color:"text-emerald-600 dark:text-emerald-400",bgColor:"bg-emerald-500",gradient:"from-emerald-500 to-emerald-600"},{icon:p,label:"Leaderboard",path:"/leaderboard",color:"text-orange-600 dark:text-orange-400",bgColor:"bg-orange-500",gradient:"from-orange-500 to-orange-600"},{icon:_,label:"Statistics",path:"/stats",color:"text-green-600 dark:text-green-400",bgColor:"bg-green-500",gradient:"from-green-500 to-green-600"}];return n?.user_metadata?.full_name?.split(" ")[0],a.jsxs(g.div,{initial:{x:-20,opacity:0},animate:{x:0,opacity:1},exit:{x:-20,opacity:0},transition:{type:"spring",stiffness:300,damping:30},className:"h-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-r border-gray-200/50 dark:border-gray-700/50 flex flex-col w-full "+(r?"fixed left-0 top-0 z-50 w-72":""),children:[a.jsxs("div",{className:"p-3 border-b border-gray-200/30 dark:border-gray-700/30",children:[a.jsxs(g.div,{initial:{y:-10,opacity:0},animate:{y:0,opacity:1},transition:{delay:.1},className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center",children:a.jsx(v,{size:20,className:"text-white"})}),a.jsxs("div",{children:[a.jsx("h1",{className:"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"USMLE Trivia"}),a.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Study Smart, Score High"})]})]}),r&&a.jsx("button",{onClick:t,className:"absolute top-3 right-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",children:a.jsx(k,{size:18,className:"text-gray-500 dark:text-gray-400"})})]}),a.jsx("nav",{className:"flex-1 p-3",children:a.jsx("div",{className:"space-y-1",children:c.map(e=>{const t=e.icon,r="/"===(n=e.path)?"/"===s.pathname:s.pathname.startsWith(n);var n;return a.jsxs(g.button,{onClick:()=>i(e.path),className:"w-full flex items-center space-x-2 px-3 py-2.5 rounded-lg transition-all duration-200 group "+(r?"text-gray-900 dark:text-white bg-gray-100/50 dark:bg-gray-800/50":"text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-200"),whileHover:{scale:1.01},whileTap:{scale:.99},children:[a.jsx("div",{className:"p-1.5 rounded-md transition-all duration-200 "+(r?`${e.color} bg-gray-200 dark:bg-gray-700`:"bg-gray-100 dark:bg-gray-700 group-hover:bg-gray-200 dark:group-hover:bg-gray-600"),children:a.jsx(t,{size:16})}),a.jsx("span",{className:"font-medium text-sm",children:e.label})]},e.path)})})}),a.jsx("div",{className:"p-3 border-t border-gray-200/30 dark:border-gray-700/30",children:a.jsxs(g.button,{onClick:()=>i("/profile"),className:"w-full flex items-center space-x-2 px-3 py-2.5 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-200 transition-all duration-200",whileHover:{scale:1.01},whileTap:{scale:.99},children:[a.jsx("div",{className:"p-1.5 rounded-md bg-gray-100 dark:bg-gray-700",children:a.jsx(w,{size:16})}),a.jsx("span",{className:"font-medium text-sm",children:"Profile"})]})})]})},ge=[],xe=[],pe=({children:e})=>{const{user:t,profile:r}=te(),[i,n]=s.useState(!1),[o,l]=s.useState(!0),c=j();s.useEffect(()=>{const e=()=>{const e=window.innerWidth<768;l(e),!e&&window.innerWidth>=1024?n(!0):e&&n(!1)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);const d=()=>{n(!i)},u=!ge.includes(c.pathname),h=!xe.includes(c.pathname);return a.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300",style:{paddingLeft:"env(safe-area-inset-left)",paddingRight:"env(safe-area-inset-right)"},children:o?a.jsxs("div",{className:"flex flex-col min-h-screen",children:[u&&a.jsx(ue,{user:t,profile:r}),a.jsx("main",{className:"flex-1 relative",children:a.jsx(g.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,ease:[.25,.46,.45,.94]},className:"container mx-auto px-3 py-2 max-w-full sm:max-w-screen-sm min-h-full",children:e})}),h&&a.jsx(he,{})]}):a.jsxs("div",{className:"flex min-h-screen w-full overflow-hidden",children:[i&&a.jsx("div",{className:"w-[15%] min-w-[180px] max-w-[200px] flex-shrink-0",children:a.jsx(me,{isOpen:i,onToggle:d,isTablet:window.innerWidth<1024})}),a.jsxs("div",{className:(i?"w-[85%]":"w-full")+" flex flex-col min-h-screen overflow-hidden transition-all duration-300",children:[u&&a.jsx(ue,{onSidebarToggle:d,showSidebarToggle:window.innerWidth<1024,isCondensed:!1,user:t,profile:r}),a.jsx("main",{className:"flex-1 overflow-auto",children:a.jsx(g.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,ease:[.25,.46,.45,.94]},className:"h-full px-4 py-4",children:a.jsx("div",{className:"max-w-full h-full",children:e})})})]}),i&&window.innerWidth<1024&&a.jsx(g.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 z-50",onClick:()=>n(!1)})]})})},fe=()=>a.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-primary-600 via-secondary-600 to-purple-700 flex items-center justify-center",children:[a.jsxs("div",{className:"text-center",children:[a.jsx(g.div,{initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0},transition:{type:"spring",stiffness:260,damping:20,duration:1.2},className:"mb-8",children:a.jsxs("div",{className:"relative mx-auto w-24 h-24 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30",children:[a.jsx(E,{className:"w-12 h-12 text-white"}),a.jsx("div",{className:"absolute -top-2 -right-2",children:a.jsx("div",{className:"w-6 h-6 bg-accent-400 rounded-full flex items-center justify-center",children:a.jsx(T,{className:"w-3 h-3 text-white"})})})]})}),a.jsxs(g.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5,duration:.6},className:"mb-4",children:[a.jsx("h1",{className:"text-4xl font-bold text-white mb-2",children:"USMLE Trivia"}),a.jsx("p",{className:"text-white/80 text-lg",children:"Master Medicine, One Question at a Time"})]}),a.jsxs(g.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8,duration:.6},className:"flex flex-wrap justify-center gap-4 mb-8",children:[a.jsxs("div",{className:"flex items-center space-x-2 bg-white/20 rounded-full px-4 py-2 backdrop-blur-sm",children:[a.jsx(h,{className:"w-4 h-4 text-white"}),a.jsx("span",{className:"text-white text-sm font-medium",children:"Study Smart"})]}),a.jsxs("div",{className:"flex items-center space-x-2 bg-white/20 rounded-full px-4 py-2 backdrop-blur-sm",children:[a.jsx(p,{className:"w-4 h-4 text-white"}),a.jsx("span",{className:"text-white text-sm font-medium",children:"Track Progress"})]})]}),a.jsx(g.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:1.2,duration:.4},className:"flex justify-center",children:a.jsx("div",{className:"flex space-x-2",children:[0,1,2].map(e=>a.jsx(g.div,{animate:{scale:[1,1.2,1],opacity:[.5,1,.5]},transition:{duration:1.5,repeat:1/0,delay:.2*e},className:"w-2 h-2 bg-white rounded-full"},e))})})]}),a.jsx("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:a.jsx(g.div,{animate:{backgroundPosition:["0% 0%","100% 100%"]},transition:{duration:20,repeat:1/0,repeatType:"reverse",ease:"linear"},className:"absolute inset-0 opacity-10",style:{backgroundImage:"radial-gradient(circle at 25% 25%, white 2px, transparent 2px),\n                             radial-gradient(circle at 75% 75%, white 2px, transparent 2px)",backgroundSize:"40px 40px"}})})]}),ye=({children:e})=>{const{user:t,loading:r}=te(),s=j();if(r)return a.jsx(fe,{});if(!t){return["/login","/welcome","/signup","/forgot-password"].includes(s.pathname)?a.jsx(fe,{}):a.jsx(C,{to:"/welcome",state:{from:s},replace:!0})}return e},be=()=>a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-expo-950",children:a.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[a.jsxs("div",{className:"relative",children:[a.jsx("div",{className:"w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"}),a.jsx("div",{className:"absolute inset-0 w-12 h-12 border-4 border-transparent border-t-blue-400 rounded-full animate-spin opacity-60",style:{animationDelay:"-0.15s"}})]}),a.jsxs("div",{className:"text-center",children:[a.jsx("p",{className:"text-lg font-semibold text-gray-700 dark:text-gray-300 animate-pulse",children:"Loading..."}),a.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:"Preparing your USMLE experience"})]})]})}),we={FETCH_QUESTIONS_ERROR:{title:"Question Loading Failed",message:"Unable to load quiz questions. Please check your connection and try again.",severity:"error",actions:["retry","goHome"]},USER_HISTORY_ERROR:{title:"Progress Tracking Issue",message:"Your progress may not be saved properly, but you can continue the quiz.",severity:"warning",actions:["continue"]},SESSION_CREATION_ERROR:{title:"Quiz Setup Failed",message:"Unable to start the quiz session. Please try again.",severity:"error",actions:["retry","goHome"]},RESPONSE_RECORDING_ERROR:{title:"Answer Not Saved",message:"Your answer may not have been recorded. You can continue, but progress might be lost.",severity:"warning",actions:["continue","retry"]},INSUFFICIENT_QUESTIONS_ERROR:{title:"Not Enough Questions",message:"There aren't enough questions available for your selection. Try adjusting your filters.",severity:"info",actions:["goBack","tryDifferentSettings"]},AUTHENTICATION_ERROR:{title:"Authentication Required",message:"Please log in to continue with your quiz.",severity:"error",actions:["login","goHome"]},NETWORK_ERROR:{title:"Connection Problem",message:"Check your internet connection and try again.",severity:"error",actions:["retry","offline"]},UNKNOWN_ERROR:{title:"Something Went Wrong",message:"An unexpected error occurred. Please try again.",severity:"error",actions:["retry","goHome"]}},je={retry:{label:"Try Again",variant:"primary",handler:e=>e.retry?.()},goHome:{label:"Go Home",variant:"secondary",handler:e=>e.navigate?.("/")},goBack:{label:"Go Back",variant:"secondary",handler:e=>e.navigate?.(-1)},continue:{label:"Continue Anyway",variant:"secondary",handler:e=>e.continue?.()},login:{label:"Log In",variant:"primary",handler:e=>e.navigate?.("/login")},offline:{label:"Use Offline Mode",variant:"secondary",handler:e=>e.enableOfflineMode?.()},tryDifferentSettings:{label:"Change Settings",variant:"secondary",handler:e=>e.navigate?.(-1)}};const Ne=new class{constructor(){this.notifications=new Map,this.retryAttempts=new Map,this.maxRetries=3,this.retryDelay=1e3}show(e,t=null,r={}){const s=we[e]||we.UNKNOWN_ERROR,a=this._generateId(),i={id:a,type:e,title:s.title,message:t||s.message,severity:s.severity,actions:s.actions.map(e=>({...je[e],key:e})),context:r,timestamp:Date.now(),dismissed:!1};return this.notifications.set(a,i),V.info("User notification shown",{notificationId:a,type:e,severity:s.severity,hasCustomMessage:!!t}),"error"!==s.severity&&setTimeout(()=>this.dismiss(a),1e4),i}dismiss(e){const t=this.notifications.get(e);t&&(t.dismissed=!0,this.notifications.delete(e),V.debug("Notification dismissed",{notificationId:e}))}dismissAll(){const e=this.notifications.size;this.notifications.clear(),V.debug("All notifications dismissed",{count:e})}getAll(){return Array.from(this.notifications.values()).filter(e=>!e.dismissed)}async handleWithRetry(e,t,r={}){const s=`${t}_${Date.now()}`;this.retryAttempts.set(s,0);const a=async()=>{const i=this.retryAttempts.get(s)||0;try{V.debug("Attempting operation",{operationName:t,attempt:i+1});const r=await e();return this.retryAttempts.delete(s),V.info("Operation succeeded",{operationName:t,attempts:i+1}),r}catch(n){const e=i+1;if(this.retryAttempts.set(s,e),V.warn("Operation failed",{operationName:t,attempt:e,error:n.message}),e>=this.maxRetries)throw this.retryAttempts.delete(s),this._handleFinalError(n,t,r),n;{const r=this.retryDelay*Math.pow(2,e-1);return V.info("Scheduling retry",{operationName:t,delay:r,attempt:e+1}),await this._delay(r),a()}}};return a()}_handleFinalError(e,t,r){const s=this._classifyError(e,t),a=this.show(s,null,{...r,retry:()=>this.handleWithRetry(r.operation,t,r),originalError:e});V.error("Operation failed after all retries",{operationName:t,errorType:s,notificationId:a.id},e)}_classifyError(e,t){if(e.message?.includes("fetch")||e.message?.includes("network"))return"NETWORK_ERROR";if(e.message?.includes("auth")||401===e.status)return"AUTHENTICATION_ERROR";if(e.message?.includes("not enough")||e.message?.includes("no questions"))return"INSUFFICIENT_QUESTIONS_ERROR";switch(t){case"fetchQuestions":case"fetchQuestionsForUser":return"FETCH_QUESTIONS_ERROR";case"createQuizSession":return"SESSION_CREATION_ERROR";case"recordQuizResponse":return"RESPONSE_RECORDING_ERROR";case"updateUserQuestionHistory":return"USER_HISTORY_ERROR";default:return"UNKNOWN_ERROR"}}_generateId(){return`notification_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}_delay(e){return new Promise(t=>setTimeout(t,e))}showSuccess(e,t=5e3){const r=this._generateId(),s={id:r,type:"SUCCESS",title:"Success",message:e,severity:"success",actions:[],timestamp:Date.now(),dismissed:!1};return this.notifications.set(r,s),setTimeout(()=>this.dismiss(r),t),V.info("Success notification shown",{message:e}),s}showInfo(e,t=8e3){const r=this._generateId(),s={id:r,type:"INFO",title:"Information",message:e,severity:"info",actions:[],timestamp:Date.now(),dismissed:!1};return this.notifications.set(r,s),setTimeout(()=>this.dismiss(r),t),V.info("Info notification shown",{message:e}),s}},ve=()=>{const[e,t]=s.useState([]);s.useEffect(()=>{const e=setInterval(()=>{const e=Ne.getAll();t(e)},100);return()=>clearInterval(e)},[]);const r=e=>{switch(e){case"error":return a.jsx(d,{className:"w-5 h-5 text-red-500"});case"warning":return a.jsx(i,{className:"w-5 h-5 text-yellow-500"});case"success":return a.jsx(I,{className:"w-5 h-5 text-green-500"});default:return a.jsx(S,{className:"w-5 h-5 text-blue-500"})}},n=e=>{switch(e){case"error":return"bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800";case"warning":return"bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800";case"success":return"bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800";default:return"bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800"}},o=e=>{t(t=>t.filter(t=>t.id!==e))};return 0===e.length?null:a.jsxs("div",{className:"fixed top-4 right-4 z-50 space-y-2 max-w-md",children:[a.jsx(R,{children:e.map(e=>a.jsx(g.div,{initial:{opacity:0,x:300,scale:.8},animate:{opacity:1,x:0,scale:1},exit:{opacity:0,x:300,scale:.8},transition:{duration:.3,type:"spring",stiffness:300,damping:25},className:`\n              p-4 rounded-lg border shadow-lg backdrop-blur-sm\n              ${n(e.severity)}\n            `,children:a.jsxs("div",{className:"flex items-start gap-3",children:[r(e.severity),a.jsxs("div",{className:"flex-1 min-w-0",children:[a.jsx("h4",{className:"font-semibold text-gray-900 dark:text-gray-100 text-sm",children:e.title}),a.jsx("p",{className:"text-gray-700 dark:text-gray-300 text-sm mt-1",children:e.message}),e.actions&&e.actions.length>0&&a.jsx("div",{className:"flex gap-2 mt-3",children:e.actions.map(t=>a.jsx("button",{onClick:()=>((e,t)=>{e.handler(t.context),o(t.id)})(t,e),className:`\n                          px-3 py-1.5 text-xs font-medium rounded-md transition-colors\n                          ${"primary"===t.variant?"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600":"bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"}\n                        `,children:t.label},t.key))})]}),a.jsx("button",{onClick:()=>o(e.id),className:"text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors",children:a.jsx(k,{className:"w-4 h-4"})})]})},e.id))}),e.length>1&&a.jsxs(g.button,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:()=>{Ne.dismissAll(),t([])},className:"w-full mt-2 px-3 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 \r\n                     bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-md border border-gray-200 dark:border-gray-700\r\n                     hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:["Dismiss All (",e.length,")"]})]})},ke=s.lazy(()=>A(()=>import("./Home-BcI44W-m.js"),__vite__mapDeps([0,1,2]))),_e=s.lazy(()=>A(()=>import("./QuizTab-BiuxmGKi.js"),__vite__mapDeps([3,1,2]))),Ee=s.lazy(()=>A(()=>import("./Profile-B3-isjiX.js"),__vite__mapDeps([4,1,5]))),Te=s.lazy(()=>A(()=>import("./Leaderboard-BiS9ssft.js"),__vite__mapDeps([6,1,5]))),Ce=s.lazy(()=>A(()=>import("./Learn-BCkyiAhC.js"),__vite__mapDeps([7,1]))),Re=s.lazy(()=>A(()=>import("./QuickQuiz-DzKMkY9F.js"),__vite__mapDeps([8,1,9]))),Se=s.lazy(()=>A(()=>import("./TimedTest-BgzNRu-r.js"),__vite__mapDeps([10,1,9]))),Ie=s.lazy(()=>A(()=>import("./TimedTestSetup-CP2ciNQ3.js"),__vite__mapDeps([11,1]))),Ae=s.lazy(()=>A(()=>import("./Results-CrUtkTSt.js"),__vite__mapDeps([12,1]))),Oe=s.lazy(()=>A(()=>import("./CustomQuiz-CsC8T2nG.js"),__vite__mapDeps([13,1,9]))),Pe=s.lazy(()=>A(()=>import("./CustomQuizSetup-P2D1dkYp.js"),__vite__mapDeps([14,1]))),ze=s.lazy(()=>A(()=>import("./Login-Bs3CmNZi.js"),__vite__mapDeps([15,1,16]))),qe=s.lazy(()=>A(()=>import("./SignUp-7rcT1V5H.js"),__vite__mapDeps([17,1,16]))),Ue=s.lazy(()=>A(()=>import("./ForgotPassword-BVoEhR5k.js"),__vite__mapDeps([18,1]))),De=s.lazy(()=>A(()=>import("./Welcome-Dx_SOJiM.js"),__vite__mapDeps([19,1]))),Le=s.lazy(()=>A(()=>import("./Privacy-DLpaQhVa.js"),__vite__mapDeps([20,1]))),Fe=s.lazy(()=>A(()=>import("./Terms-0ZT972W1.js"),__vite__mapDeps([21,1])));function $e(){return a.jsxs(ie,{children:[a.jsx(ve,{}),a.jsxs(O,{children:[a.jsx(P,{path:"/login",element:a.jsx(ne,{authFlow:"login",children:a.jsx(s.Suspense,{fallback:a.jsx(fe,{}),children:a.jsx(ze,{})})})}),a.jsx(P,{path:"/signup",element:a.jsx(ne,{authFlow:"signup",children:a.jsx(s.Suspense,{fallback:a.jsx(fe,{}),children:a.jsx(qe,{})})})}),a.jsx(P,{path:"/forgot-password",element:a.jsx(ne,{authFlow:"forgot-password",children:a.jsx(s.Suspense,{fallback:a.jsx(fe,{}),children:a.jsx(Ue,{})})})}),a.jsx(P,{path:"/welcome",element:a.jsx(ne,{authFlow:"welcome",children:a.jsx(s.Suspense,{fallback:a.jsx(fe,{}),children:a.jsx(De,{})})})}),a.jsx(P,{path:"/privacy",element:a.jsx(s.Suspense,{fallback:a.jsx(be,{}),children:a.jsx(Le,{})})}),a.jsx(P,{path:"/terms",element:a.jsx(s.Suspense,{fallback:a.jsx(be,{}),children:a.jsx(Fe,{})})}),a.jsx(P,{path:"/*",element:a.jsx(ye,{children:a.jsx(oe,{queryType:"app-data",children:a.jsx(pe,{children:a.jsx(s.Suspense,{fallback:a.jsx(be,{}),children:a.jsxs(O,{children:[a.jsx(P,{path:"/",element:a.jsx(oe,{queryType:"home-data",children:a.jsx(ke,{})})}),a.jsx(P,{path:"/quiz",element:a.jsx(oe,{queryType:"quiz-categories",children:a.jsx(_e,{})})}),a.jsx(P,{path:"/quiz/:categoryId",element:a.jsx(le,{quizType:"quick-quiz",children:a.jsx(Re,{})})}),a.jsx(P,{path:"/profile",element:a.jsx(oe,{queryType:"profile-data",children:a.jsx(Ee,{})})}),a.jsx(P,{path:"/leaderboard",element:a.jsx(oe,{queryType:"leaderboard-data",children:a.jsx(Te,{})})}),a.jsx(P,{path:"/learn",element:a.jsx(Ce,{})}),a.jsx(P,{path:"/quick-quiz",element:a.jsx(le,{quizType:"quick-quiz",children:a.jsx(Re,{})})}),a.jsx(P,{path:"/timed-test-setup",element:a.jsx(le,{quizType:"timed-test-setup",children:a.jsx(Ie,{})})}),a.jsx(P,{path:"/timed-test",element:a.jsx(le,{quizType:"timed-test",children:a.jsx(Se,{})})}),a.jsx(P,{path:"/custom-quiz-setup",element:a.jsx(le,{quizType:"custom-quiz-setup",children:a.jsx(Pe,{})})}),a.jsx(P,{path:"/custom-quiz",element:a.jsx(le,{quizType:"custom-quiz",children:a.jsx(Oe,{})})}),a.jsx(P,{path:"/results",element:a.jsx(le,{quizType:"results",children:a.jsx(Ae,{})})})]})})})})})})]})]})}z.createRoot(document.getElementById("root")).render(a.jsx(s.StrictMode,{children:a.jsx(q,{client:D,children:a.jsx(U,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:a.jsx(re,{children:a.jsxs(ae,{children:[a.jsx($e,{}),!1]})})})})}));export{be as L,X as a,Y as b,M as c,de as g,V as l,$ as s,te as u,Z as w};
