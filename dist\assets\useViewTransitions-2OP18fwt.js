import{u as t,r as e}from"./vendor-DfmD63KD.js";const o={userActivity:t=>["userActivity",t],userStats:t=>["userStats",t],recentActivity:t=>["recentActivity",t],categories:()=>["categories"],categoriesWithProgress:t=>["categories","withProgress",t],userProgress:t=>["userProgress",t],questions:(t,e)=>["questions",t,e],quizSession:t=>["quizSession",t]};function r(t){if(!t)return"Unknown";const e=new Date,o=new Date(t),r=Math.floor((e-o)/1e3);return r<60?"Just now":r<3600?`${Math.floor(r/60)}m ago`:r<86400?`${Math.floor(r/3600)}h ago`:r<604800?`${Math.floor(r/86400)}d ago`:o.toLocaleDateString()}function n(t){const e={cardiology:"bg-red-500",neurology:"bg-purple-500",dermatology:"bg-orange-500",endocrinology:"bg-blue-500",gastroenterology:"bg-green-500",hematology:"bg-pink-500","infectious disease":"bg-yellow-500","obstetrics and gynecology":"bg-indigo-500",oncology:"bg-gray-500",orthopedics:"bg-teal-500",default:"bg-gray-400"};return e[t?.toLowerCase()||""]||e.default}const s=()=>{const o=t(),r=e.useCallback((t,e={})=>{const{replace:r=!1,state:n=null,transitionName:s="page-transition",duration:a=300}=e;if(!document.startViewTransition)return void o(t,{replace:r,state:n});document.documentElement.classList.add(`transition-${s}`);const i=document.startViewTransition(()=>{o(t,{replace:r,state:n})});return i.finished.finally(()=>{document.documentElement.classList.remove(`transition-${s}`)}),i},[o]),n=e.useCallback((t,e,o={})=>r("/quiz",{state:{categoryId:t,categoryName:e,...o},transitionName:"quiz-enter"}),[r]),s=e.useCallback(()=>r("/",{transitionName:"home-enter"}),[r]),a=e.useCallback(()=>r("/profile",{transitionName:"profile-enter"}),[r]);return{transitionTo:r,transitionToQuiz:n,transitionToHome:s,transitionToProfile:a,isSupported:!!document.startViewTransition}};export{n as a,r as g,o as q,s as u};
