{"name": "usmle-trivia", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:report": "playwright show-report", "test:auth": "playwright test tests/auth-flow.spec.js", "test:routes": "playwright test tests/protected-routes.spec.js", "test:ui-tests": "playwright test tests/ui-interactions.spec.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.15.1", "@supabase/supabase-js": "^2.45.6", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "@tanstack/react-query-persist-client": "^5.81.2", "dotenv": "^17.2.0", "framer-motion": "^11.11.17", "healthicons-react": "^3.3.0", "lucide-react": "^0.460.0", "puppeteer": "^24.12.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.28.0", "recharts": "^3.0.2", "use-sound": "^5.0.0", "zustand": "^5.0.6"}, "devDependencies": {"@playwright/test": "^1.53.1", "@types/node": "^20.17.9", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.20", "eslint": "^8.55.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.4.49", "rollup-plugin-visualizer": "^6.0.3", "supabase": "^2.26.9", "tailwindcss": "^3.4.15", "terser": "^5.43.1", "vite": "^5.0.8", "vite-plugin-compression": "^0.5.1"}, "description": "A modern, mobile-first web application for USMLE (United States Medical Licensing Examination) preparation built with React, Vite, Supabase, and Tailwind CSS.", "main": "playwright.config.js", "directories": {"doc": "docs"}, "keywords": [], "author": "", "license": "ISC"}