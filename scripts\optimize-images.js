const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const glob = require('glob');

/**
 * Image optimization script for the USMLE Trivia App
 * This script optimizes images in the src/assets directory
 */

// Configuration
const config = {
  inputDir: 'src/assets',
  outputDir: 'src/assets/optimized',
  quality: 80, // JPEG quality (0-100)
  sizes: [
    { width: 1920, suffix: 'large' },
    { width: 1280, suffix: 'medium' },
    { width: 640, suffix: 'small' },
    { width: 320, suffix: 'thumbnail' }
  ],
  formats: ['webp', 'avif', 'jpg'] // Output formats
};

// Create output directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Find all images
const imageFiles = glob.sync(`${config.inputDir}/**/*.{jpg,jpeg,png,gif}`);

console.log(`Found ${imageFiles.length} images to optimize`);

// Process each image
(async () => {
  for (const file of imageFiles) {
    const filename = path.basename(file, path.extname(file));
    console.log(`Processing ${filename}...`);
    
    try {
      // Load image
      const image = sharp(file);
      const metadata = await image.metadata();
      
      // Process each size
      for (const size of config.sizes) {
        // Skip if the original is smaller than the target size
        if (metadata.width <= size.width) continue;
        
        // Process each format
        for (const format of config.formats) {
          const outputPath = path.join(
            config.outputDir,
            `${filename}-${size.suffix}.${format}`
          );
          
          // Resize and convert
          await image
            .resize(size.width)
            [format]({ quality: config.quality })
            .toFile(outputPath);
          
          console.log(`Created ${outputPath}`);
        }
      }
    } catch (error) {
      console.error(`Error processing ${file}:`, error);
    }
  }
  
  console.log('Image optimization complete!');
})();

// Note: To use this script, you need to install the following dependencies:
// npm install --save-dev sharp glob