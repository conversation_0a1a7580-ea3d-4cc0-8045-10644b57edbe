import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_PUBLISHABLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Test database schema and tables
 */
async function testDatabaseSchema() {
  console.log('Testing database schema...\n');
  
  try {
    // Test 1: Check profiles table
    console.log('1. Testing profiles table...');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username, total_questions, correct_answers, quiz_completed, streak_days')
      .limit(1);
    
    if (profilesError) {
      console.error('❌ Profiles table error:', profilesError.message);
    } else {
      console.log('✅ Profiles table accessible');
      console.log('Sample data:', profiles);
    }
    
    // Test 2: Check user_statistics table
    console.log('\n2. Testing user_statistics table...');
    const { data: stats, error: statsError } = await supabase
      .from('user_statistics')
      .select('*')
      .limit(1);
    
    if (statsError) {
      console.error('❌ User statistics table error:', statsError.message);
    } else {
      console.log('✅ User statistics table accessible');
      console.log('Sample data:', stats);
    }
    
    // Test 3: Check user_achievements table
    console.log('\n3. Testing user_achievements table...');
    const { data: achievements, error: achievementsError } = await supabase
      .from('user_achievements')
      .select('*')
      .limit(1);
    
    if (achievementsError) {
      console.error('❌ User achievements table error:', achievementsError.message);
    } else {
      console.log('✅ User achievements table accessible');
      console.log('Sample data:', achievements);
    }
    
    // Test 4: Check leaderboard table
    console.log('\n4. Testing leaderboard table...');
    const { data: leaderboard, error: leaderboardError } = await supabase
      .from('leaderboard')
      .select('*')
      .limit(1);
    
    if (leaderboardError) {
      console.error('❌ Leaderboard table error:', leaderboardError.message);
    } else {
      console.log('✅ Leaderboard table accessible');
      console.log('Sample data:', leaderboard);
    }
    
    // Test 5: Check leaderboard_with_users view
    console.log('\n5. Testing leaderboard_with_users view...');
    const { data: leaderboardView, error: leaderboardViewError } = await supabase
      .from('leaderboard_with_users')
      .select('*')
      .limit(1);
    
    if (leaderboardViewError) {
      console.error('❌ Leaderboard view error:', leaderboardViewError.message);
    } else {
      console.log('✅ Leaderboard view accessible');
      console.log('Sample data:', leaderboardView);
    }
    
    // Test 6: Check user_seen_questions table
    console.log('\n6. Testing user_seen_questions table...');
    const { data: seenQuestions, error: seenQuestionsError } = await supabase
      .from('user_seen_questions')
      .select('*')
      .limit(1);
    
    if (seenQuestionsError) {
      console.error('❌ User seen questions table error:', seenQuestionsError.message);
    } else {
      console.log('✅ User seen questions table accessible');
      console.log('Sample data:', seenQuestions);
    }
    
    // Test 7: Check user_activity_log table
    console.log('\n7. Testing user_activity_log table...');
    const { data: activityLog, error: activityLogError } = await supabase
      .from('user_activity_log')
      .select('*')
      .limit(1);
    
    if (activityLogError) {
      console.error('❌ User activity log table error:', activityLogError.message);
    } else {
      console.log('✅ User activity log table accessible');
      console.log('Sample data:', activityLog);
    }
    
    // Test 8: Check tag_relationships view
    console.log('\n8. Testing tag_relationships view...');
    const { data: tagRelationships, error: tagRelationshipsError } = await supabase
      .from('tag_relationships')
      .select('*')
      .limit(1);
    
    if (tagRelationshipsError) {
      console.error('❌ Tag relationships view error:', tagRelationshipsError.message);
    } else {
      console.log('✅ Tag relationships view accessible');
      console.log('Sample data:', tagRelationships);
    }
    
    // Test 9: Check tag_hierarchy view
    console.log('\n9. Testing tag_hierarchy view...');
    const { data: tagHierarchy, error: tagHierarchyError } = await supabase
      .from('tag_hierarchy')
      .select('*')
      .limit(1);
    
    if (tagHierarchyError) {
      console.error('❌ Tag hierarchy view error:', tagHierarchyError.message);
    } else {
      console.log('✅ Tag hierarchy view accessible');
      console.log('Sample data:', tagHierarchy);
    }
    
    console.log('\n=== Database Schema Test Complete ===');
    
  } catch (error) {
    console.error('Error testing database schema:', error);
  }
}

// Run the test
testDatabaseSchema();