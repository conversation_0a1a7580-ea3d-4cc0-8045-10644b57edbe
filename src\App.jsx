import React, { lazy, Suspense } from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { RealTimeProvider } from './contexts/RealTimeContext';
import LoadingScreen from './components/ui/LoadingScreen';
import ErrorBoundary from './components/ui/ErrorBoundary';
import Layout from './components/layout/Layout';
import NotificationSystem from './components/ui/NotificationSystem';
import LeaderboardNotifications from './components/leaderboard/LeaderboardNotifications';
import NetworkErrorHandler from './components/ui/NetworkErrorHandler';

// Lazy-loaded components with retry logic
const lazyWithRetry = (componentImport) => {
  return lazy(() => {
    return new Promise((resolve, reject) => {
      // Try to import the component
      componentImport()
        .then(resolve)
        .catch((error) => {
          // If it fails, try again after a short delay
          console.error(`Error loading component: ${error.message}`);
          setTimeout(() => {
            console.log('Retrying component load...');
            componentImport().then(resolve).catch(reject);
          }, 1000);
        });
    });
  });
};

// Lazy-loaded components
const Home = lazyWithRetry(() => import('./pages/Home'));
const QuizTab = lazyWithRetry(() => import('./pages/QuizTab'));
const Leaderboard = lazyWithRetry(() => import('./pages/Leaderboard'));
const Profile = lazyWithRetry(() => import('./pages/Profile'));
const QuizPage = lazyWithRetry(() => import('./pages/QuizPage'));
const ResultsPage = lazyWithRetry(() => import('./pages/ResultsPage'));
const CustomQuizSetup = lazyWithRetry(() => import('./pages/CustomQuizSetup'));
const Login = lazyWithRetry(() => import('./pages/Login'));
const Register = lazyWithRetry(() => import('./pages/Register'));
const ForgotPassword = lazyWithRetry(() => import('./pages/ForgotPassword'));

function App() {
  return (
    <RealTimeProvider>
      <BrowserRouter>
        <ErrorBoundary>
          <Suspense fallback={<LoadingScreen />}>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              
              <Route path="/" element={<Layout />}>
                <Route index element={<Home />} />
                <Route path="quiz-tab" element={<QuizTab />} />
                <Route path="leaderboard" element={<Leaderboard />} />
                <Route path="profile" element={<Profile />} />
                <Route path="quiz/:quizType" element={<QuizPage />} />
                <Route path="results" element={<ResultsPage />} />
                <Route path="custom-quiz" element={<CustomQuizSetup />} />
              </Route>
              
              {/* Catch-all route for 404 */}
              <Route path="*" element={
                <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 via-green-900/20 to-emerald-900/20 p-4">
                  <div className="w-full max-w-md bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-lg shadow-lg p-6 text-center">
                    <h2 className="text-2xl font-bold text-white mb-2">Page Not Found</h2>
                    <p className="text-gray-300 mb-4">The page you're looking for doesn't exist or has been moved.</p>
                    <button 
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-white"
                      onClick={() => window.location.href = '/'}
                    >
                      Go Home
                    </button>
                  </div>
                </div>
              } />
            </Routes>
          </Suspense>
        </ErrorBoundary>
        
        {/* Notification system */}
        <NotificationSystem />
        
        {/* Real-time leaderboard notifications */}
        <LeaderboardNotifications />
        
        {/* Network error handler */}
        <NetworkErrorHandler />
      </BrowserRouter>
    </RealTimeProvider>
  );
}

export default App;