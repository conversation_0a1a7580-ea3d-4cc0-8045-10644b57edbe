import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy, X } from 'lucide-react';
import { supabase } from '../../lib/supabase';
import { useRealTime } from '../../contexts/RealTimeContext';

/**
 * LeaderboardNotifications Component
 * Shows real-time notifications for leaderboard changes
 */
const LeaderboardNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  const { isConnected } = useRealTime();
  
  // Set up real-time subscription for leaderboard updates
  useEffect(() => {
    if (!isConnected) return;
    
    const leaderboardSubscription = supabase
      .channel('leaderboard_notifications')
      .on('postgres_changes', 
        { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'leaderboard',
          filter: `user_id=neq.${supabase.auth.user()?.id || 'none'}`
        },
        (payload) => {
          handleLeaderboardUpdate(payload.new);
        }
      )
      .subscribe();
    
    return () => {
      supabase.removeChannel(leaderboardSubscription);
    };
  }, [isConnected]);
  
  // Handle leaderboard update
  const handleLeaderboardUpdate = async (entry) => {
    try {
      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('username, avatar_url')
        .eq('id', entry.user_id)
        .single();
      
      if (profileError) throw profileError;
      
      // Create notification
      const notification = {
        id: Date.now(),
        username: profile.username || 'Anonymous User',
        avatar: profile.avatar_url,
        score: entry.score,
        maxScore: entry.max_score,
        accuracy: entry.accuracy,
        quizType: entry.quiz_type,
        timestamp: new Date()
      };
      
      // Add notification
      setNotifications(prev => [notification, ...prev].slice(0, 5));
      
      // Auto-remove notification after 5 seconds
      setTimeout(() => {
        setNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 5000);
    } catch (error) {
      console.error('Error handling leaderboard update:', error);
    }
  };
  
  // Remove notification
  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };
  
  return (
    <div className="fixed bottom-20 right-4 z-50 space-y-2 max-w-xs w-full">
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8, transition: { duration: 0.2 } }}
            className="bg-gray-800 bg-opacity-90 backdrop-blur-sm border border-gray-700 rounded-lg shadow-lg p-3 flex items-start"
          >
            <div className="flex-shrink-0 mr-3">
              {notification.avatar ? (
                <img
                  src={notification.avatar}
                  alt={notification.username}
                  className="w-10 h-10 rounded-full"
                />
              ) : (
                <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center">
                  <Trophy className="w-5 h-5 text-white" />
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white truncate">
                {notification.username}
              </p>
              <p className="text-xs text-gray-300">
                Scored {notification.score}/{notification.maxScore} ({notification.accuracy}%) on a{' '}
                {notification.quizType === 'quick'
                  ? 'Quick Quiz'
                  : notification.quizType === 'timed'
                  ? 'Timed Test'
                  : 'Custom Quiz'}
              </p>
              <p className="text-xs text-gray-400 mt-1">
                {new Date(notification.timestamp).toLocaleTimeString()}
              </p>
            </div>
            <button
              onClick={() => removeNotification(notification.id)}
              className="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-200"
              aria-label="Dismiss"
            >
              <X className="w-4 h-4" />
            </button>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default LeaderboardNotifications;