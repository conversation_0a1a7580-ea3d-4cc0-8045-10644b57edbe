import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronUp, Filter, RefreshCw, Medal, Award, Trophy } from 'lucide-react';
import { useLeaderboardData } from '../../hooks/useLeaderboardData';
import LoadingState from '../ui/LoadingState';
import EmptyState from '../ui/EmptyState';
import FallbackError from '../ui/FallbackError';

/**
 * LeaderboardTable Component
 * Displays leaderboard data with filtering and sorting options
 */
const LeaderboardTable = () => {
  const [showFilters, setShowFilters] = useState(false);
  
  const {
    leaderboardData,
    loading,
    error,
    userRank,
    filters,
    pagination,
    updateFilters,
    refreshData,
    changePage,
    getAvailableTimeFrames,
    getAvailableQuizTypes,
    getAvailableDifficulties
  } = useLeaderboardData();
  
  // Handle filter changes
  const handleFilterChange = (key, value) => {
    updateFilters({ [key]: value });
  };
  
  // Get medal icon based on rank
  const getMedalIcon = (rank) => {
    if (rank === 1) return <Trophy className="w-5 h-5 text-yellow-400" />;
    if (rank === 2) return <Medal className="w-5 h-5 text-gray-300" />;
    if (rank === 3) return <Award className="w-5 h-5 text-amber-600" />;
    return null;
  };
  
  // Format time taken
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  // Loading state
  if (loading && !leaderboardData.length) {
    return <LoadingState message="Loading leaderboard data..." />;
  }
  
  // Error state
  if (error) {
    return (
      <FallbackError 
        error={error} 
        message="Failed to load leaderboard data" 
        onRetry={refreshData} 
      />
    );
  }
  
  // Empty state
  if (!loading && !leaderboardData.length) {
    return (
      <EmptyState 
        title="No leaderboard entries found" 
        message="Be the first to complete a quiz and appear on the leaderboard!" 
        icon="🏆"
      />
    );
  }
  
  return (
    <div className="w-full">
      {/* Filters */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-xl font-bold text-white">Leaderboard</h2>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="p-2 rounded-lg bg-gray-800 bg-opacity-50 hover:bg-opacity-70 transition-all"
              aria-label="Toggle filters"
            >
              <Filter className="w-5 h-5 text-gray-300" />
            </button>
            <button
              onClick={refreshData}
              className="p-2 rounded-lg bg-gray-800 bg-opacity-50 hover:bg-opacity-70 transition-all"
              aria-label="Refresh leaderboard"
            >
              <RefreshCw className="w-5 h-5 text-gray-300" />
            </button>
          </div>
        </div>
        
        {userRank && (
          <div className="mb-4 p-3 bg-blue-900/30 border border-blue-700/50 rounded-lg">
            <p className="text-sm text-blue-200">
              Your current rank: <span className="font-bold text-white">#{userRank}</span>
            </p>
          </div>
        )}
        
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="overflow-hidden"
            >
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3 p-4 bg-gray-800 bg-opacity-50 rounded-lg">
                {/* Time Frame Filter */}
                <div>
                  <label className="block text-sm text-gray-300 mb-1">Time Frame</label>
                  <select
                    value={filters.timeFrame}
                    onChange={(e) => handleFilterChange('timeFrame', e.target.value)}
                    className="w-full bg-gray-700 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {getAvailableTimeFrames().map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                {/* Quiz Type Filter */}
                <div>
                  <label className="block text-sm text-gray-300 mb-1">Quiz Type</label>
                  <select
                    value={filters.quizType || ''}
                    onChange={(e) => handleFilterChange('quizType', e.target.value || null)}
                    className="w-full bg-gray-700 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {getAvailableQuizTypes().map((option) => (
                      <option key={option.value || 'all'} value={option.value || ''}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                {/* Difficulty Filter */}
                <div>
                  <label className="block text-sm text-gray-300 mb-1">Difficulty</label>
                  <select
                    value={filters.difficulty || ''}
                    onChange={(e) => handleFilterChange('difficulty', e.target.value || null)}
                    className="w-full bg-gray-700 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {getAvailableDifficulties().map((option) => (
                      <option key={option.value || 'all'} value={option.value || ''}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                {/* Results Per Page */}
                <div>
                  <label className="block text-sm text-gray-300 mb-1">Results Per Page</label>
                  <select
                    value={filters.limit}
                    onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
                    className="w-full bg-gray-700 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                  </select>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {/* Leaderboard Table */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="text-left border-b border-gray-700">
              <th className="px-4 py-3 text-gray-300 font-medium">Rank</th>
              <th className="px-4 py-3 text-gray-300 font-medium">User</th>
              <th className="px-4 py-3 text-gray-300 font-medium">Score</th>
              <th className="px-4 py-3 text-gray-300 font-medium hidden md:table-cell">Accuracy</th>
              <th className="px-4 py-3 text-gray-300 font-medium hidden md:table-cell">Time</th>
              <th className="px-4 py-3 text-gray-300 font-medium hidden lg:table-cell">Quiz Type</th>
              <th className="px-4 py-3 text-gray-300 font-medium hidden lg:table-cell">Date</th>
            </tr>
          </thead>
          <tbody>
            {leaderboardData.map((entry) => (
              <motion.tr
                key={entry.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className={`border-b border-gray-800 hover:bg-gray-800/30 transition-colors ${
                  entry.userId === userRank?.userId ? 'bg-blue-900/20' : ''
                }`}
              >
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    {getMedalIcon(entry.rank)}
                    <span className={`ml-1 ${entry.rank <= 3 ? 'font-bold' : ''}`}>
                      {entry.rank}
                    </span>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center">
                    {entry.avatar ? (
                      <img
                        src={entry.avatar}
                        alt={entry.username}
                        className="w-8 h-8 rounded-full mr-2"
                      />
                    ) : (
                      <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center mr-2">
                        <span className="text-sm font-medium text-gray-300">
                          {entry.username.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                    <span className="font-medium text-white">{entry.username}</span>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <span className="font-medium text-white">{entry.score}</span>
                  <span className="text-gray-400 text-sm ml-1">/ {entry.maxScore}</span>
                </td>
                <td className="px-4 py-3 hidden md:table-cell">
                  <div className="flex items-center">
                    <div className="w-16 h-2 bg-gray-700 rounded-full overflow-hidden mr-2">
                      <div
                        className="h-full bg-green-500"
                        style={{ width: `${entry.accuracy}%` }}
                      ></div>
                    </div>
                    <span className="text-gray-300">{entry.accuracy}%</span>
                  </div>
                </td>
                <td className="px-4 py-3 hidden md:table-cell text-gray-300">
                  {formatTime(entry.timeTaken)}
                </td>
                <td className="px-4 py-3 hidden lg:table-cell">
                  <span className="px-2 py-1 rounded-md text-xs font-medium bg-gray-700 text-gray-300">
                    {entry.quizType === 'quick'
                      ? 'Quick Quiz'
                      : entry.quizType === 'timed'
                      ? 'Timed Test'
                      : 'Custom Quiz'}
                  </span>
                </td>
                <td className="px-4 py-3 hidden lg:table-cell text-gray-400 text-sm">
                  {entry.date}
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-between items-center mt-4">
          <div className="text-sm text-gray-400">
            Showing {(pagination.currentPage - 1) * pagination.entriesPerPage + 1} to{' '}
            {Math.min(pagination.currentPage * pagination.entriesPerPage, pagination.totalEntries)} of{' '}
            {pagination.totalEntries} entries
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => changePage(pagination.currentPage - 1)}
              disabled={pagination.currentPage === 1}
              className={`p-2 rounded-md ${
                pagination.currentPage === 1
                  ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              aria-label="Previous page"
            >
              <ChevronUp className="w-4 h-4" />
            </button>
            <span className="text-gray-300">
              Page {pagination.currentPage} of {pagination.totalPages}
            </span>
            <button
              onClick={() => changePage(pagination.currentPage + 1)}
              disabled={pagination.currentPage === pagination.totalPages}
              className={`p-2 rounded-md ${
                pagination.currentPage === pagination.totalPages
                  ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              aria-label="Next page"
            >
              <ChevronDown className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default LeaderboardTable;