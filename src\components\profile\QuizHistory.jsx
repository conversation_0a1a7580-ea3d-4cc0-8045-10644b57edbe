import React, { useState, useEffect } from 'react';
import { <PERSON>, Clock, <PERSON><PERSON><PERSON>, Filter, RefreshCw } from 'lucide-react';
import { supabase } from '../../lib/supabase';
import { handleError } from '../../utils/errorHandling';
import LoadingState from '../ui/LoadingState';
import EmptyState from '../ui/EmptyState';
import FallbackError from '../ui/FallbackError';

/**
 * QuizHistory Component
 * Displays the user's quiz history
 */
const QuizHistory = () => {
  const [quizHistory, setQuizHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    quizType: null,
    timeFrame: 'all'
  });
  const [showFilters, setShowFilters] = useState(false);
  
  // Fetch quiz history
  useEffect(() => {
    const fetchQuizHistory = async () => {
      try {
        setLoading(true);
        
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          setQuizHistory([]);
          return;
        }
        
        // Build query
        let query = supabase
          .from('leaderboard')
          .select(`
            id,
            quiz_type,
            score,
            max_score,
            accuracy,
            time_taken,
            created_at,
            category_id,
            difficulty,
            question_count,
            tags:category_id(name)
          `)
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });
        
        // Apply filters
        if (filters.quizType) {
          query = query.eq('quiz_type', filters.quizType);
        }
        
        if (filters.timeFrame === 'day') {
          query = query.gte('created_at', new Date(Date.now() - 86400000).toISOString());
        } else if (filters.timeFrame === 'week') {
          query = query.gte('created_at', new Date(Date.now() - 604800000).toISOString());
        } else if (filters.timeFrame === 'month') {
          query = query.gte('created_at', new Date(Date.now() - 2592000000).toISOString());
        }
        
        // Execute query
        const { data, error: fetchError } = await query;
        
        if (fetchError) throw fetchError;
        
        // Format data
        const formattedHistory = data.map(entry => ({
          id: entry.id,
          quizType: entry.quiz_type,
          score: entry.score,
          maxScore: entry.max_score,
          accuracy: entry.accuracy,
          timeTaken: entry.time_taken,
          date: new Date(entry.created_at),
          categoryName: entry.tags?.name || 'General',
          difficulty: entry.difficulty || 'mixed',
          questionCount: entry.question_count
        }));
        
        setQuizHistory(formattedHistory);
      } catch (err) {
        console.error('Error fetching quiz history:', err);
        setError(handleError(err, 'fetchQuizHistory').message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchQuizHistory();
  }, [filters]);
  
  // Handle filter change
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };
  
  // Format time taken
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  // Get quiz type label
  const getQuizTypeLabel = (quizType) => {
    switch (quizType) {
      case 'quick':
        return 'Quick Quiz';
      case 'timed':
        return 'Timed Test';
      case 'custom':
        return 'Custom Quiz';
      default:
        return 'Unknown';
    }
  };
  
  // Get difficulty badge color
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-900/50 text-green-300';
      case 'medium':
        return 'bg-yellow-900/50 text-yellow-300';
      case 'hard':
        return 'bg-red-900/50 text-red-300';
      default:
        return 'bg-blue-900/50 text-blue-300';
    }
  };
  
  // Loading state
  if (loading && !quizHistory.length) {
    return <LoadingState message="Loading your quiz history..." />;
  }
  
  // Error state
  if (error) {
    return (
      <FallbackError 
        error={error} 
        message="Failed to load your quiz history" 
      />
    );
  }
  
  // Empty state
  if (!loading && !quizHistory.length) {
    return (
      <EmptyState 
        title="No quiz history found" 
        message="Complete some quizzes to see your history here." 
        icon="📝"
      />
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold text-white">Quiz History</h3>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="p-2 rounded-lg bg-gray-700 bg-opacity-50 hover:bg-opacity-70 transition-all"
              aria-label="Toggle filters"
            >
              <Filter className="w-5 h-5 text-gray-300" />
            </button>
            <button
              onClick={() => {
                setFilters({ quizType: null, timeFrame: 'all' });
              }}
              className="p-2 rounded-lg bg-gray-700 bg-opacity-50 hover:bg-opacity-70 transition-all"
              aria-label="Refresh history"
            >
              <RefreshCw className="w-5 h-5 text-gray-300" />
            </button>
          </div>
        </div>
        
        {/* Filters */}
        {showFilters && (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 p-4 bg-gray-700 bg-opacity-50 rounded-lg mb-4">
            {/* Quiz Type Filter */}
            <div>
              <label className="block text-sm text-gray-300 mb-1">Quiz Type</label>
              <select
                value={filters.quizType || ''}
                onChange={(e) => handleFilterChange('quizType', e.target.value || null)}
                className="w-full bg-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Types</option>
                <option value="quick">Quick Quiz</option>
                <option value="timed">Timed Test</option>
                <option value="custom">Custom Quiz</option>
              </select>
            </div>
            
            {/* Time Frame Filter */}
            <div>
              <label className="block text-sm text-gray-300 mb-1">Time Frame</label>
              <select
                value={filters.timeFrame}
                onChange={(e) => handleFilterChange('timeFrame', e.target.value)}
                className="w-full bg-gray-600 text-white rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Time</option>
                <option value="day">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
              </select>
            </div>
          </div>
        )}
        
        {/* Quiz History List */}
        <div className="space-y-4">
          {quizHistory.map((quiz) => (
            <div 
              key={quiz.id} 
              className="bg-gray-700 bg-opacity-50 rounded-lg p-4"
            >
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h4 className="font-medium text-white">{getQuizTypeLabel(quiz.quizType)}</h4>
                  <p className="text-sm text-gray-300">{quiz.categoryName}</p>
                </div>
                <div className="flex items-center">
                  <span className={`text-xs px-2 py-1 rounded-md ${getDifficultyColor(quiz.difficulty)}`}>
                    {quiz.difficulty.charAt(0).toUpperCase() + quiz.difficulty.slice(1)}
                  </span>
                </div>
              </div>
              
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-2">
                {/* Score */}
                <div className="flex items-center">
                  <BarChart className="w-4 h-4 text-blue-400 mr-2" />
                  <div>
                    <p className="text-xs text-gray-400">Score</p>
                    <p className="text-sm font-medium text-white">
                      {quiz.score} / {quiz.maxScore}
                    </p>
                  </div>
                </div>
                
                {/* Accuracy */}
                <div className="flex items-center">
                  <div className="w-4 h-4 rounded-full bg-green-500 mr-2 flex items-center justify-center">
                    <span className="text-xs text-white">%</span>
                  </div>
                  <div>
                    <p className="text-xs text-gray-400">Accuracy</p>
                    <p className="text-sm font-medium text-white">{quiz.accuracy}%</p>
                  </div>
                </div>
                
                {/* Time Taken */}
                <div className="flex items-center">
                  <Clock className="w-4 h-4 text-purple-400 mr-2" />
                  <div>
                    <p className="text-xs text-gray-400">Time</p>
                    <p className="text-sm font-medium text-white">{formatTime(quiz.timeTaken)}</p>
                  </div>
                </div>
                
                {/* Date */}
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 text-red-400 mr-2" />
                  <div>
                    <p className="text-xs text-gray-400">Date</p>
                    <p className="text-sm font-medium text-white">
                      {quiz.date.toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
              
              {/* Progress Bar */}
              <div className="w-full h-2 bg-gray-600 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-blue-500" 
                  style={{ width: `${(quiz.score / quiz.maxScore) * 100}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default QuizHistory;