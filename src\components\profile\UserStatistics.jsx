import React from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON>, 
  CheckCircle, 
  Clock, 
  Award, 
  Calendar, 
  TrendingUp 
} from 'lucide-react';
import { useUserProfile } from '../../hooks/useUserProfile';
import LoadingState from '../ui/LoadingState';
import FallbackError from '../ui/FallbackError';

/**
 * UserStatistics Component
 * Displays user statistics and achievements
 */
const UserStatistics = () => {
  const { profile, statistics, achievements, loading, error } = useUserProfile();
  
  // Loading state
  if (loading) {
    return <LoadingState message="Loading your statistics..." />;
  }
  
  // Error state
  if (error) {
    return (
      <FallbackError 
        error={error} 
        message="Failed to load your statistics" 
      />
    );
  }
  
  // Not authenticated state
  if (!profile) {
    return (
      <div className="bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-lg p-6 mb-6">
        <div className="text-center py-8">
          <h3 className="text-xl font-bold text-white mb-2">
            Sign in to view your statistics
          </h3>
          <p className="text-gray-300">
            Create an account or sign in to track your progress and achievements.
          </p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-lg p-6">
        <h3 className="text-xl font-bold text-white mb-4">Statistics Overview</h3>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Questions */}
          <div className="bg-gray-700 bg-opacity-50 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <BarChart className="w-5 h-5 text-blue-400 mr-2" />
              <h4 className="text-sm font-medium text-gray-300">Total Questions</h4>
            </div>
            <p className="text-2xl font-bold text-white">{statistics.totalQuestions}</p>
          </div>
          
          {/* Accuracy */}
          <div className="bg-gray-700 bg-opacity-50 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <CheckCircle className="w-5 h-5 text-green-400 mr-2" />
              <h4 className="text-sm font-medium text-gray-300">Accuracy</h4>
            </div>
            <p className="text-2xl font-bold text-white">{statistics.accuracy}%</p>
          </div>
          
          {/* Quizzes Completed */}
          <div className="bg-gray-700 bg-opacity-50 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <Clock className="w-5 h-5 text-purple-400 mr-2" />
              <h4 className="text-sm font-medium text-gray-300">Quizzes Completed</h4>
            </div>
            <p className="text-2xl font-bold text-white">{statistics.quizCompleted}</p>
          </div>
          
          {/* Current Streak */}
          <div className="bg-gray-700 bg-opacity-50 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <Calendar className="w-5 h-5 text-red-400 mr-2" />
              <h4 className="text-sm font-medium text-gray-300">Current Streak</h4>
            </div>
            <p className="text-2xl font-bold text-white">{statistics.streakDays} days</p>
          </div>
        </div>
      </div>
      
      {/* Category Breakdown */}
      {statistics.categoryBreakdown.length > 0 && (
        <div className="bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-lg p-6">
          <h3 className="text-xl font-bold text-white mb-4">Category Breakdown</h3>
          
          <div className="space-y-4">
            {statistics.categoryBreakdown.map((category, index) => (
              <div key={index} className="bg-gray-700 bg-opacity-50 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium text-white">{category.category}</h4>
                  <span className="text-sm text-gray-300">{category.accuracy}% accuracy</span>
                </div>
                
                <div className="w-full h-2 bg-gray-600 rounded-full overflow-hidden mb-2">
                  <div 
                    className="h-full bg-blue-500" 
                    style={{ width: `${category.accuracy}%` }}
                  ></div>
                </div>
                
                <div className="flex justify-between text-xs text-gray-400">
                  <span>{category.correct} / {category.attempted} correct</span>
                  <span>
                    {Math.floor(category.timeSpent / 60)} min {category.timeSpent % 60} sec spent
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Achievements */}
      <div className="bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-lg p-6">
        <h3 className="text-xl font-bold text-white mb-4">Achievements</h3>
        
        {achievements.length === 0 ? (
          <div className="text-center py-8">
            <Award className="w-12 h-12 text-gray-500 mx-auto mb-3" />
            <p className="text-gray-300">
              Complete quizzes to earn achievements
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {achievements.map((achievement) => (
              <motion.div
                key={achievement.achievement_id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-gray-700 bg-opacity-50 rounded-lg p-4 text-center"
              >
                <div className="text-3xl mb-2">{achievement.icon}</div>
                <h4 className="font-medium text-white text-sm mb-1">{achievement.name}</h4>
                <p className="text-xs text-gray-300">{achievement.description}</p>
                <p className="text-xs text-gray-400 mt-2">
                  {new Date(achievement.earned_at).toLocaleDateString()}
                </p>
              </motion.div>
            ))}
          </div>
        )}
      </div>
      
      {/* Performance Trends */}
      <div className="bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-white">Performance Trends</h3>
          <TrendingUp className="w-5 h-5 text-blue-400" />
        </div>
        
        {/* Placeholder for performance charts - to be implemented */}
        <div className="bg-gray-700 bg-opacity-50 rounded-lg p-6 text-center">
          <p className="text-gray-300">
            Performance trends will be available after completing more quizzes.
          </p>
        </div>
      </div>
    </div>
  );
};

export default UserStatistics;