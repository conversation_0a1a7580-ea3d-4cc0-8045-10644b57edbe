import React from 'react';
import { motion } from 'framer-motion';

/**
 * Quiz Progress Bar Component
 * Shows progress through the quiz with current question and total
 * 
 * @param {Object} props - Component props
 * @param {number} props.current - Current question number
 * @param {number} props.total - Total number of questions
 * @param {number|null} props.secondsLeft - Seconds left for current question (optional)
 * @returns {JSX.Element} Quiz progress bar component
 */
const QuizProgressBar = ({ current, total, secondsLeft = null }) => {
  const progress = (current / total) * 100;
  
  return (
    <div className="mb-6">
      <div className="flex justify-between items-center mb-2">
        <div className="text-sm font-medium text-gray-300">
          Question {current} of {total}
        </div>
        
        {secondsLeft !== null && (
          <div className="text-sm font-medium text-gray-300">
            {Math.floor(secondsLeft / 60)}:{(secondsLeft % 60).toString().padStart(2, '0')}
          </div>
        )}
      </div>
      
      <div className="h-2 w-full bg-gray-700 rounded-full overflow-hidden">
        <motion.div
          className="h-2 bg-blue-500 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.5 }}
        />
      </div>
    </div>
  );
};

export default QuizProgressBar;