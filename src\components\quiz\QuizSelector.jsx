import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Zap, Timer, Settings, BookOpen, BarChart3 } from 'lucide-react';

/**
 * QuizSelector Component
 * Displays available quiz types with descriptions and allows users to select one
 */
const QuizSelector = ({ isNewUser = false }) => {
  const navigate = useNavigate();

  // Define quiz types with their details
  const quizTypes = [
    {
      id: 'quick',
      icon: Zap,
      title: 'Quick Quiz',
      subtitle: '10 questions • Auto-advance',
      description: 'Fast-paced quiz with automatic progression. Perfect for a quick study session.',
      color: 'bg-purple-600',
      iconColor: 'text-purple-600',
      timeEstimate: '~10 min',
      difficulty: 'Mixed',
      action: () => navigate('/quiz/quick'),
      recommended: isNewUser,
      badge: '🟣'
    },
    {
      id: 'timed',
      icon: Timer,
      title: 'Timed Test',
      subtitle: '20 questions • 30 minutes',
      description: 'Simulates exam conditions with a time limit. Great for building test-taking skills.',
      color: 'bg-blue-600',
      iconColor: 'text-blue-600',
      timeEstimate: '30 min',
      difficulty: 'Progressive',
      action: () => navigate('/quiz/timed'),
      recommended: !isNewUser,
      badge: '🔵'
    },
    {
      id: 'custom',
      icon: Settings,
      title: 'Custom Quiz',
      subtitle: 'Configurable • Personalized',
      description: 'Create a quiz tailored to your needs with specific categories and difficulty levels.',
      color: 'bg-green-600',
      iconColor: 'text-green-600',
      timeEstimate: 'Variable',
      difficulty: 'Custom',
      action: () => navigate('/custom-quiz-setup'),
      recommended: false,
      badge: '🟢'
    }
  ];

  // Additional resources
  const resources = [
    {
      id: 'learn',
      icon: BookOpen,
      title: 'Study Materials',
      subtitle: 'Review concepts & explanations',
      description: 'Access comprehensive study materials to strengthen your understanding.',
      color: 'bg-amber-600',
      iconColor: 'text-amber-600',
      timeEstimate: 'Self-paced',
      difficulty: 'All levels',
      action: () => navigate('/learn'),
      recommended: false
    },
    {
      id: 'stats',
      icon: BarChart3,
      title: 'My Progress',
      subtitle: 'Statistics & achievements',
      description: 'Track your performance, view achievements, and identify areas for improvement.',
      color: 'bg-orange-600',
      iconColor: 'text-orange-600',
      timeEstimate: '~2 min',
      difficulty: null,
      action: () => navigate('/profile'),
      recommended: false
    }
  ];

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="py-8 px-4 max-w-6xl mx-auto">
      {/* Main heading */}
      <motion.h1 
        className="text-3xl font-bold text-gray-900 dark:text-white mb-6 text-center"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        Choose Your Quiz Mode
      </motion.h1>
      
      {/* Quiz types section */}
      <motion.div 
        className="mb-12"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
          Quiz Types
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {quizTypes.map((quiz) => (
            <motion.div
              key={quiz.id}
              variants={itemVariants}
              whileHover={{ y: -5, scale: 1.02 }}
              className="relative bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-blue-200 dark:hover:border-blue-800"
              onClick={quiz.action}
            >
              {/* Recommended badge */}
              {quiz.recommended && (
                <div className="absolute -top-2 -right-2 bg-yellow-400 text-yellow-900 text-xs font-bold px-2 py-1 rounded-full shadow-md">
                  ⭐ Recommended
                </div>
              )}
              
              {/* Header with icon and metadata */}
              <div className="flex items-start justify-between mb-4">
                <div className={`p-3 rounded-xl ${quiz.color} bg-opacity-10`}>
                  <quiz.icon className={`w-6 h-6 ${quiz.iconColor}`} />
                </div>
                <div className="text-right">
                  <div className="text-xs text-gray-500 dark:text-gray-400">{quiz.timeEstimate}</div>
                  {quiz.difficulty && (
                    <div className="text-xs text-gray-600 dark:text-gray-300 font-medium">{quiz.difficulty}</div>
                  )}
                </div>
              </div>
              
              {/* Title and description */}
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                {quiz.badge && <span className="mr-2">{quiz.badge}</span>}
                {quiz.title}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">{quiz.subtitle}</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">{quiz.description}</p>
              
              {/* Start button */}
              <button 
                className={`mt-4 w-full py-2 rounded-lg text-white font-medium ${quiz.color} hover:opacity-90 transition-opacity`}
                onClick={(e) => {
                  e.stopPropagation();
                  quiz.action();
                }}
              >
                Start Now
              </button>
            </motion.div>
          ))}
        </div>
      </motion.div>
      
      {/* Additional resources section */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        transition={{ delay: 0.3 }}
      >
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
          Additional Resources
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {resources.map((resource) => (
            <motion.div
              key={resource.id}
              variants={itemVariants}
              whileHover={{ y: -5, scale: 1.02 }}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer border-2 border-transparent hover:border-blue-200 dark:hover:border-blue-800"
              onClick={resource.action}
            >
              {/* Header with icon and metadata */}
              <div className="flex items-start justify-between mb-4">
                <div className={`p-3 rounded-xl ${resource.color} bg-opacity-10`}>
                  <resource.icon className={`w-6 h-6 ${resource.iconColor}`} />
                </div>
                <div className="text-right">
                  <div className="text-xs text-gray-500 dark:text-gray-400">{resource.timeEstimate}</div>
                  {resource.difficulty && (
                    <div className="text-xs text-gray-600 dark:text-gray-300 font-medium">{resource.difficulty}</div>
                  )}
                </div>
              </div>
              
              {/* Title and description */}
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">{resource.title}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">{resource.subtitle}</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">{resource.description}</p>
              
              {/* Action button */}
              <button 
                className={`mt-4 w-full py-2 rounded-lg text-white font-medium ${resource.color} hover:opacity-90 transition-opacity`}
                onClick={(e) => {
                  e.stopPropagation();
                  resource.action();
                }}
              >
                View
              </button>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default QuizSelector;