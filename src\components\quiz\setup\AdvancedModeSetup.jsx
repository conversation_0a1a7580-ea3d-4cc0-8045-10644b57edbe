import React from 'react'

/**
 * Advanced Mode Setup Component
 * Provides detailed filtering options for power users
 */
const AdvancedModeSetup = ({
  subjects,
  systems,
  topics,
  selectedSubject,
  selectedSystem,
  selectedTopic,
  onSubjectChange,
  onSystemChange,
  onTopicChange,
  difficulty,
  onDifficultyChange,
  questionCount,
  onQuestionCountChange,
  timing,
  onTimingChange,
  questionCounts,
  availableQuestions,
  difficultyOptions,
  timingOptions,
  loading
}) => {
  if (loading) {
    return (
      <div className="space-y-6">
        {/* Loading skeleton */}
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="animate-pulse">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/4 mb-2"></div>
            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Subject */}
      <div>
        <label htmlFor="subject" className="block font-medium text-gray-800 dark:text-gray-200 mb-1">
          Subject <span className="text-red-500">*</span>
        </label>
        <select
          id="subject"
          value={selectedSubject}
          onChange={(e) => onSubjectChange(e.target.value)}
          className="w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2"
          required
        >
          <option value="">Select a subject</option>
          {subjects.map((s) => (
            <option key={s.id} value={s.id}>
              {s.name} ({s.questionCount || 0} questions)
            </option>
          ))}
        </select>
        {subjects.length === 0 && (
          <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
            No subjects available. Please check your database connection.
          </p>
        )}
      </div>

      {/* System */}
      <div>
        <label htmlFor="system" className="block font-medium text-gray-800 dark:text-gray-200 mb-1">
          System <span className="text-red-500">*</span>
        </label>
        <select
          id="system"
          value={selectedSystem}
          onChange={(e) => onSystemChange(e.target.value)}
          className="w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2"
          disabled={!selectedSubject}
          required
        >
          <option value="">Select a system</option>
          {systems.map((s) => (
            <option key={s.id} value={s.id}>
              {s.name} ({s.questionCount || 0} questions)
            </option>
          ))}
        </select>
        {selectedSubject && systems.length === 0 && (
          <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
            No systems available for the selected subject.
          </p>
        )}
      </div>

      {/* Topic */}
      <div>
        <label htmlFor="topic" className="block font-medium text-gray-800 dark:text-gray-200 mb-1">
          Topic (Optional)
        </label>
        <select
          id="topic"
          value={selectedTopic}
          onChange={(e) => onTopicChange(e.target.value)}
          className="w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2"
          disabled={!selectedSystem}
        >
          <option value="">All topics</option>
          {topics.map((t) => (
            <option key={t.id} value={t.id}>
              {t.name} ({t.questionCount || 0} questions)
            </option>
          ))}
        </select>
        {selectedSystem && topics.length === 0 && (
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            No specific topics available for this system.
          </p>
        )}
      </div>

      {/* Difficulty */}
      <div>
        <label htmlFor="difficulty" className="block font-medium text-gray-800 dark:text-gray-200 mb-1">
          Difficulty
        </label>
        <select
          id="difficulty"
          value={difficulty}
          onChange={(e) => onDifficultyChange(e.target.value)}
          className="w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2"
        >
          {difficultyOptions.map((opt) => (
            <option key={opt.value} value={opt.value}>
              {opt.label}
            </option>
          ))}
        </select>
      </div>

      {/* Number of Questions */}
      <div>
        <label htmlFor="questionCount" className="block font-medium text-gray-800 dark:text-gray-200 mb-1">
          Number of Questions
        </label>
        <div className="flex items-center space-x-4">
          <input
            id="questionCount"
            type="range"
            min={1}
            max={40}
            value={questionCount}
            onChange={(e) => onQuestionCountChange(Number(e.target.value))}
            className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
          />
          <input
            type="number"
            min={1}
            max={40}
            value={questionCount}
            onChange={(e) => onQuestionCountChange(Number(e.target.value))}
            className="w-16 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2 text-center"
          />
        </div>
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>1 question</span>
          <span>40 questions</span>
        </div>
      </div>

      {/* Timing */}
      <div>
        <label htmlFor="timing" className="block font-medium text-gray-800 dark:text-gray-200 mb-1">
          Timing
        </label>
        <select
          id="timing"
          value={timing}
          onChange={(e) => onTimingChange(e.target.value)}
          className="w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 text-gray-900 dark:text-white p-2"
        >
          {timingOptions.map((opt) => (
            <option key={opt.value} value={opt.value}>
              {opt.label}
            </option>
          ))}
        </select>
      </div>

      {/* Available Questions Display */}
      <div className={`rounded-lg p-4 ${
        selectedSubject && selectedSystem
          ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
          : 'bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800'
      }`}>
        <div className="flex items-center justify-between">
          <span className={`text-sm font-medium ${
            selectedSubject && selectedSystem
              ? 'text-blue-800 dark:text-blue-200'
              : 'text-gray-800 dark:text-gray-200'
          }`}>
            Available Questions:
          </span>
          <span className={`text-lg font-bold ${
            selectedSubject && selectedSystem
              ? 'text-blue-600 dark:text-blue-400'
              : 'text-gray-600 dark:text-gray-400'
          }`}>
            {availableQuestions}
          </span>
        </div>
        
        {!selectedSubject && (
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            Select a subject to see available questions.
          </p>
        )}
        
        {selectedSubject && !selectedSystem && (
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            Select a system to see available questions.
          </p>
        )}
        
        {selectedSubject && selectedSystem && availableQuestions === 0 && (
          <p className="text-xs text-red-600 dark:text-red-400 mt-1">
            No questions available for this combination. Try selecting different options.
          </p>
        )}
        
        {availableQuestions > 0 && questionCount > availableQuestions && (
          <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
            Requested {questionCount} questions, but only {availableQuestions} available.
            Quiz will include all available questions.
          </p>
        )}
      </div>
    </div>
  )
}

export default AdvancedModeSetup
