import React from 'react';

/**
 * EmptyState Component
 * Displays a message when no data is available
 * @param {Object} props - Component props
 * @param {string} props.title - Title to display
 * @param {string} props.message - Message to display
 * @param {string} props.icon - Emoji or icon to display
 * @param {Function} props.action - Optional action to perform
 * @param {string} props.actionLabel - Label for the action button
 * @returns {JSX.Element} Empty state component
 */
const EmptyState = ({ 
  title = 'No data available', 
  message = 'There is no data to display at this time.', 
  icon = '📭',
  action,
  actionLabel = 'Try Again'
}) => {
  return (
    <div className="w-full flex flex-col items-center justify-center py-12">
      <div className="text-center">
        <div className="text-5xl mb-4">{icon}</div>
        <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
        <p className="text-gray-300 mb-6 max-w-md mx-auto">{message}</p>
        
        {action && (
          <button
            onClick={action}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
          >
            {actionLabel}
          </button>
        )}
      </div>
    </div>
  );
};

export default EmptyState;