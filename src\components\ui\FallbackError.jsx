import React from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';

/**
 * FallbackError Component
 * Displays an error message with retry option
 * @param {Object} props - Component props
 * @param {Error} props.error - Error object
 * @param {string} props.message - Error message to display
 * @param {Function} props.onRetry - Function to call when retry button is clicked
 * @returns {JSX.Element} Error fallback component
 */
const FallbackError = ({ 
  error, 
  message = 'Something went wrong', 
  onRetry 
}) => {
  return (
    <div className="w-full flex flex-col items-center justify-center py-12">
      <div className="bg-red-900/30 border border-red-800/50 rounded-lg p-6 max-w-md w-full">
        <div className="flex items-center mb-4">
          <AlertCircle className="w-6 h-6 text-red-400 mr-2" />
          <h3 className="text-lg font-bold text-white">{message}</h3>
        </div>
        
        <div className="text-sm text-red-200 mb-4">
          {error?.message || 'An unexpected error occurred'}
        </div>
        
        {onRetry && (
          <button
            onClick={onRetry}
            className="flex items-center justify-center w-full px-4 py-2 bg-red-700 hover:bg-red-600 text-white rounded-md transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </button>
        )}
      </div>
    </div>
  );
};

export default FallbackError;