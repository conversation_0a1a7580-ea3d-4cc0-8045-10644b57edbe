import React, { useState, useEffect, useRef } from 'react';

/**
 * Lazy Image Component
 * Loads images only when they enter the viewport
 * 
 * @param {Object} props - Component props
 * @param {string} props.src - Image source URL
 * @param {string} props.alt - Image alt text
 * @param {string} props.className - Additional CSS classes
 * @param {Object} props.imgProps - Additional image props
 * @returns {JSX.Element} Lazy image component
 */
const LazyImage = ({ 
  src, 
  alt = '', 
  className = '', 
  placeholderColor = '#1f2937',
  imgProps = {} 
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef(null);
  
  // Set up intersection observer to detect when image enters viewport
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '200px', // Load images 200px before they enter viewport
        threshold: 0.01
      }
    );
    
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }
    
    return () => {
      if (imgRef.current) {
        observer.unobserve(imgRef.current);
      }
    };
  }, []);
  
  // Handle image load
  const handleLoad = () => {
    setIsLoaded(true);
  };
  
  // Generate responsive image sources if available
  const generateSrcSet = () => {
    if (!src || !src.includes('.')) return undefined;
    
    const baseName = src.substring(0, src.lastIndexOf('.'));
    const extension = src.substring(src.lastIndexOf('.'));
    
    // Check if optimized versions exist
    if (src.includes('/assets/')) {
      return `
        ${baseName}-small.webp 640w,
        ${baseName}-medium.webp 1280w,
        ${baseName}-large.webp 1920w
      `;
    }
    
    return undefined;
  };
  
  return (
    <div 
      ref={imgRef}
      className={`relative overflow-hidden ${className}`}
      style={{ 
        backgroundColor: placeholderColor,
        aspectRatio: imgProps.width && imgProps.height ? `${imgProps.width}/${imgProps.height}` : 'auto'
      }}
    >
      {isInView && (
        <img
          src={src}
          alt={alt}
          srcSet={generateSrcSet()}
          sizes="(max-width: 640px) 100vw, (max-width: 1280px) 50vw, 33vw"
          loading="lazy"
          onLoad={handleLoad}
          className={`w-full h-full object-cover transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          {...imgProps}
        />
      )}
    </div>
  );
};

export default LazyImage;