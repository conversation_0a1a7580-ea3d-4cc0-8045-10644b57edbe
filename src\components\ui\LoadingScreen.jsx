import React from 'react';
import { motion } from 'framer-motion';

/**
 * Loading Screen Component
 * Displays a loading animation while content is being loaded
 */
const LoadingScreen = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 via-green-900/20 to-emerald-900/20">
      <motion.div 
        className="flex flex-col items-center"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="relative mb-6">
          <motion.div 
            className="h-16 w-16 rounded-full border-4 border-blue-500 border-t-transparent"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
          <motion.div 
            className="absolute inset-0 h-16 w-16 rounded-full border-4 border-green-500 border-b-transparent"
            animate={{ rotate: -180 }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
          />
        </div>
        
        <motion.h2 
          className="text-xl font-bold text-white mb-2"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          Loading
        </motion.h2>
        
        <p className="text-gray-400">Please wait while we prepare your content...</p>
      </motion.div>
    </div>
  );
};

export default LoadingScreen;