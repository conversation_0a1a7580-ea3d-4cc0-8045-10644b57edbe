import React from 'react';

/**
 * LoadingState Component
 * Displays a loading indicator with an optional message
 * @param {Object} props - Component props
 * @param {string} props.message - Loading message to display
 * @returns {JSX.Element} Loading state component
 */
const LoadingState = ({ message = 'Loading...' }) => {
  return (
    <div className="w-full flex flex-col items-center justify-center py-12">
      <div className="animate-pulse flex flex-col items-center">
        <div className="h-12 w-12 bg-blue-500 rounded-full animate-spin mb-4 flex items-center justify-center">
          <div className="h-6 w-6 bg-blue-300 rounded-full"></div>
        </div>
        <p className="text-gray-300 text-lg">{message}</p>
      </div>
    </div>
  );
};

export default LoadingState;