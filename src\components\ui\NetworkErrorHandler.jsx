import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * Network Error Handler Component
 * Monitors network connectivity and displays a banner when offline
 * 
 * @returns {JSX.Element} Network error handler component
 */
const NetworkErrorHandler = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showBanner, setShowBanner] = useState(false);
  
  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      // Show reconnected message briefly
      setShowBanner(true);
      setTimeout(() => setShowBanner(false), 3000);
    };
    
    const handleOffline = () => {
      setIsOnline(false);
      setShowBanner(true);
    };
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  // Don't render anything if online and banner not showing
  if (isOnline && !showBanner) {
    return null;
  }
  
  return (
    <AnimatePresence>
      {showBanner && (
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{ duration: 0.3 }}
          className={`fixed top-0 left-0 right-0 z-50 p-4 ${
            isOnline 
              ? 'bg-green-600 bg-opacity-90' 
              : 'bg-red-600 bg-opacity-90'
          } backdrop-blur-md text-white text-center`}
        >
          {isOnline ? (
            <p className="font-medium">
              ✅ You're back online! Data will now sync automatically.
            </p>
          ) : (
            <p className="font-medium">
              ⚠️ You're offline. Some features may not work until you reconnect.
            </p>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default NetworkErrorHandler;