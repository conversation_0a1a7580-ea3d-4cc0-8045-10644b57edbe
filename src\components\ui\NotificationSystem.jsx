import React, { useState, useEffect } from 'react';
import { useRealTime } from '../../contexts/RealTimeContext';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * Notification system component
 * @returns {JSX.Element} Notification system component
 */
const NotificationSystem = () => {
  const { notifications, markNotificationAsRead } = useRealTime();
  const [visibleNotifications, setVisibleNotifications] = useState([]);
  
  // Show only the most recent 3 unread notifications
  useEffect(() => {
    const unreadNotifications = notifications
      .filter(notification => !notification.read)
      .slice(0, 3);
    
    setVisibleNotifications(unreadNotifications);
  }, [notifications]);
  
  // Auto-dismiss notifications after 5 seconds
  useEffect(() => {
    const timers = visibleNotifications.map(notification => {
      return setTimeout(() => {
        markNotificationAsRead(notification.id);
      }, 5000);
    });
    
    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [visibleNotifications, markNotificationAsRead]);
  
  // Handle notification dismiss
  const handleDismiss = (id) => {
    markNotificationAsRead(id);
  };
  
  // Get notification background color based on type
  const getNotificationColor = (type) => {
    switch (type) {
      case 'achievement':
        return 'bg-yellow-900 border-yellow-700';
      case 'leaderboard':
        return 'bg-blue-900 border-blue-700';
      case 'streak':
        return 'bg-green-900 border-green-700';
      case 'error':
        return 'bg-red-900 border-red-700';
      default:
        return 'bg-gray-800 border-gray-700';
    }
  };
  
  return (
    <div className="fixed bottom-4 right-4 z-50 flex flex-col space-y-2 max-w-sm">
      <AnimatePresence>
        {visibleNotifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, y: 50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9, transition: { duration: 0.2 } }}
            className={`p-4 rounded-lg shadow-lg border ${getNotificationColor(notification.type)} backdrop-blur-md`}
          >
            <div className="flex items-start">
              <div className="flex-shrink-0 mr-3">
                <span className="text-2xl">{notification.icon || '🔔'}</span>
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-white">{notification.title}</h3>
                <p className="text-sm text-gray-300">{notification.message}</p>
                {notification.action && (
                  <button 
                    className="mt-2 text-sm text-blue-400 hover:text-blue-300"
                    onClick={notification.action.onClick}
                  >
                    {notification.action.label}
                  </button>
                )}
              </div>
              <button 
                className="ml-4 text-gray-400 hover:text-white"
                onClick={() => handleDismiss(notification.id)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default NotificationSystem;