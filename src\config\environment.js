/**
 * Environment configuration for the USMLE Trivia App
 * This file centralizes environment-specific settings and feature flags
 */

// Environment configurations
const environments = {
  development: {
    apiUrl: 'http://localhost:3000',
    supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
    supabaseKey: import.meta.env.VITE_SUPABASE_PUBLISHABLE_KEY || import.meta.env.VITE_SUPABASE_ANON_KEY,
    debug: true,
    featureFlags: {
      realTimeUpdates: true,
      customQuiz: true,
      leaderboard: true,
      achievements: true
    }
  },
  staging: {
    apiUrl: 'https://staging-api.usmletrivia.com',
    supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
    supabaseKey: import.meta.env.VITE_SUPABASE_PUBLISHABLE_KEY || import.meta.env.VITE_SUPABASE_ANON_KEY,
    debug: true,
    featureFlags: {
      realTimeUpdates: true,
      customQuiz: true,
      leaderboard: true,
      achievements: true
    }
  },
  production: {
    apiUrl: 'https://api.usmletrivia.com',
    supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
    supabaseKey: import.meta.env.VITE_SUPABASE_PUBLISHABLE_KEY || import.meta.env.VITE_SUPABASE_ANON_KEY,
    debug: false,
    featureFlags: {
      realTimeUpdates: true,
      customQuiz: true,
      leaderboard: true,
      achievements: true
    }
  }
};

/**
 * Determine the current environment
 * @returns {string} Current environment name
 */
const getEnvironment = () => {
  if (import.meta.env.PROD) {
    return window.location.hostname.includes('staging') ? 'staging' : 'production';
  }
  return 'development';
};

// Export configuration for current environment
export const config = environments[getEnvironment()];

/**
 * Check if a feature is enabled
 * @param {string} featureName - Name of the feature to check
 * @returns {boolean} Whether the feature is enabled
 */
export const isFeatureEnabled = (featureName) => {
  return config.featureFlags[featureName] || false;
};

/**
 * Log message only in debug mode
 * @param {string} message - Message to log
 * @param {any} data - Optional data to log
 */
export const debugLog = (message, data) => {
  if (config.debug) {
    if (data) {
      console.log(`🔍 [Debug] ${message}`, data);
    } else {
      console.log(`🔍 [Debug] ${message}`);
    }
  }
};