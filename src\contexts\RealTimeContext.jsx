import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import { setupRealTimeSubscriptions } from '../lib/databaseUtils';

// Create context
const RealTimeContext = createContext();

/**
 * Provider component for real-time updates
 * @param {Object} props - Component props
 * @returns {JSX.Element} Provider component
 */
export const RealTimeProvider = ({ children }) => {
  const [onlineUsers, setOnlineUsers] = useState({});
  const [notifications, setNotifications] = useState([]);
  const [isConnected, setIsConnected] = useState(false);
  
  // Set up real-time subscriptions
  useEffect(() => {
    let cleanup = () => {};
    
    const setupSubscriptions = async () => {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        // Set up subscriptions
        cleanup = setupRealTimeSubscriptions(user.id);
        setIsConnected(true);
      } else {
        setIsConnected(false);
      }
    };
    
    setupSubscriptions();
    
    // Listen for auth state changes
    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_IN') {
        setupSubscriptions();
      } else if (event === 'SIGNED_OUT') {
        cleanup();
        setIsConnected(false);
        setOnlineUsers({});
      }
    });
    
    return () => {
      cleanup();
      authListener?.subscription?.unsubscribe();
    };
  }, []);
  
  // Add notification
  const addNotification = (notification) => {
    setNotifications(prev => [
      {
        id: Date.now(),
        timestamp: new Date(),
        read: false,
        ...notification
      },
      ...prev
    ]);
  };
  
  // Mark notification as read
  const markNotificationAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true } 
          : notification
      )
    );
  };
  
  // Clear notifications
  const clearNotifications = () => {
    setNotifications([]);
  };
  
  // Context value
  const value = {
    onlineUsers,
    notifications,
    isConnected,
    addNotification,
    markNotificationAsRead,
    clearNotifications
  };
  
  return (
    <RealTimeContext.Provider value={value}>
      {children}
    </RealTimeContext.Provider>
  );
};

/**
 * Hook for using real-time context
 * @returns {Object} Real-time context value
 */
export const useRealTime = () => {
  const context = useContext(RealTimeContext);
  
  if (context === undefined) {
    throw new Error('useRealTime must be used within a RealTimeProvider');
  }
  
  return context;
};

export default RealTimeContext;