import { useState, useEffect, useMemo } from 'react';
import { fetchCategories, getAvailableQuestionCount, fetchQuizQuestions } from '../lib/databaseUtils';

/**
 * Hook for setting up custom quizzes
 * @returns {Object} Custom quiz setup data and functions
 */
export const useCustomQuizSetup = () => {
  // Mode state
  const [isSimpleMode, setIsSimpleMode] = useState(true);
  
  // Category data
  const [categories, setCategories] = useState({
    subjects: [],
    systems: [],
    topics: []
  });
  
  // Loading and error states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Simple mode state
  const [selectedSubject, setSelectedSubject] = useState('');
  const [difficulty, setDifficulty] = useState('');
  
  // Advanced mode state
  const [selectedSystem, setSelectedSystem] = useState('');
  const [selectedTopic, setSelectedTopic] = useState('');
  const [timing, setTiming] = useState('timed');
  
  // Common state
  const [questionCount, setQuestionCount] = useState(10);
  
  // Available question count based on filters
  const [availableQuestionCount, setAvailableQuestionCount] = useState(0);
  
  // Fetch categories
  useEffect(() => {
    const getCategories = async () => {
      try {
        setLoading(true);
        const data = await fetchCategories();
        setCategories(data);
      } catch (err) {
        console.error('Error in useCustomQuizSetup:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    
    getCategories();
  }, []);
  
  // Create simple categories for the simple mode
  const simpleCategories = useMemo(() => {
    // Use the most popular subjects as simple categories
    return categories.subjects
      .filter(subject => subject.questionCount > 0)
      .slice(0, 6)
      .map(subject => ({
        id: subject.id,
        name: subject.name,
        icon: getSubjectIcon(subject.name),
        description: `${subject.questionCount} questions available`,
        questionCount: subject.questionCount
      }));
  }, [categories.subjects]);
  
  // Filter systems based on selected subject
  const filteredSystems = useMemo(() => {
    if (!selectedSubject) return [];
    
    // In a real implementation, we would fetch related systems from the database
    // For now, we'll just return all systems
    return categories.systems.filter(system => system.questionCount > 0);
  }, [selectedSubject, categories.systems]);
  
  // Filter topics based on selected system
  const filteredTopics = useMemo(() => {
    if (!selectedSystem) return [];
    
    // In a real implementation, we would fetch related topics from the database
    // For now, we'll just return all topics
    return categories.topics.filter(topic => topic.questionCount > 0);
  }, [selectedSystem, categories.topics]);
  
  // Calculate question counts for dropdowns
  const questionCounts = useMemo(() => {
    const counts = {
      subjects: {},
      systems: {},
      topics: {}
    };
    
    categories.subjects.forEach(subject => {
      counts.subjects[subject.id] = subject.questionCount;
    });
    
    categories.systems.forEach(system => {
      counts.systems[system.id] = system.questionCount;
    });
    
    categories.topics.forEach(topic => {
      counts.topics[topic.id] = topic.questionCount;
    });
    
    return counts;
  }, [categories]);
  
  // Update available question count when filters change
  useEffect(() => {
    const updateAvailableCount = async () => {
      try {
        let selectedTags = [];
        
        if (isSimpleMode) {
          // Simple mode: just use the selected subject
          if (selectedSubject) {
            selectedTags = [selectedSubject];
          }
        } else {
          // Advanced mode: use subject, system, and topic
          if (selectedSubject) {
            selectedTags.push(selectedSubject);
          }
          
          if (selectedSystem) {
            selectedTags.push(selectedSystem);
          }
          
          if (selectedTopic) {
            selectedTags.push(selectedTopic);
          }
        }
        
        if (selectedTags.length === 0) {
          setAvailableQuestionCount(0);
          return;
        }
        
        const count = await getAvailableQuestionCount({
          difficulty: difficulty || 'mixed',
          selectedTags
        });
        
        setAvailableQuestionCount(count);
      } catch (err) {
        console.error('Error updating available question count:', err);
        setError(err.message);
      }
    };
    
    updateAvailableCount();
  }, [isSimpleMode, selectedSubject, selectedSystem, selectedTopic, difficulty]);
  
  // Determine if the quiz can be started
  const canStart = useMemo(() => {
    if (isSimpleMode) {
      return Boolean(selectedSubject);
    } else {
      return Boolean(selectedSubject && selectedSystem && availableQuestionCount > 0);
    }
  }, [isSimpleMode, selectedSubject, selectedSystem, availableQuestionCount]);
  
  // Create navigation state for the quiz
  const createNavigationState = () => {
    const state = {
      quizType: 'custom',
      questionCount: Math.min(questionCount, availableQuestionCount || 10),
      difficulty: difficulty || 'mixed',
      timing: timing || 'timed',
      showExplanations: true
    };
    
    if (isSimpleMode) {
      state.subjectId = selectedSubject;
      
      // Find the subject name for display
      const subject = categories.subjects.find(s => s.id === selectedSubject);
      if (subject) {
        state.subjectName = subject.name;
      }
    } else {
      state.subjectId = selectedSubject;
      state.systemId = selectedSystem;
      state.topicId = selectedTopic || null;
      
      // Find names for display
      const subject = categories.subjects.find(s => s.id === selectedSubject);
      const system = categories.systems.find(s => s.id === selectedSystem);
      const topic = selectedTopic ? categories.topics.find(t => t.id === selectedTopic) : null;
      
      if (subject) state.subjectName = subject.name;
      if (system) state.systemName = system.name;
      if (topic) state.topicName = topic.name;
    }
    
    return state;
  };
  
  // Create custom quiz
  const createCustomQuiz = async () => {
    try {
      // Validate inputs
      if (questionCount > availableQuestionCount && availableQuestionCount > 0) {
        throw new Error(`Only ${availableQuestionCount} questions available with current filters`);
      }
      
      if (questionCount < 1) {
        throw new Error('Question count must be at least 1');
      }
      
      let selectedTags = [];
      
      if (isSimpleMode) {
        // Simple mode: just use the selected subject
        if (selectedSubject) {
          selectedTags = [selectedSubject];
        }
      } else {
        // Advanced mode: use subject, system, and topic
        if (selectedSubject) {
          selectedTags.push(selectedSubject);
        }
        
        if (selectedSystem) {
          selectedTags.push(selectedSystem);
        }
        
        if (selectedTopic) {
          selectedTags.push(selectedTopic);
        }
      }
      
      if (selectedTags.length === 0) {
        throw new Error('Please select at least one category');
      }
      
      // Fetch questions
      const questions = await fetchQuizQuestions({
        quizType: 'custom',
        difficulty: difficulty || 'mixed',
        questionCount: Math.min(questionCount, availableQuestionCount || 10),
        selectedTags
      });
      
      if (!questions || questions.length === 0) {
        throw new Error('No questions available for the selected criteria');
      }
      
      return {
        questions,
        settings: {
          quizType: 'custom',
          questionCount: questions.length,
          difficulty: difficulty || 'mixed',
          timing: timing || 'timed',
          showExplanations: true,
          subjectId: selectedSubject,
          systemId: selectedSystem || null,
          topicId: selectedTopic || null
        }
      };
      
    } catch (err) {
      console.error('Error creating custom quiz:', err);
      setError(err.message);
      return null;
    }
  };
  
  // Reset filters when changing modes
  useEffect(() => {
    if (isSimpleMode) {
      setSelectedSystem('');
      setSelectedTopic('');
    }
  }, [isSimpleMode]);
  
  // Reset topic when changing system
  useEffect(() => {
    setSelectedTopic('');
  }, [selectedSystem]);
  
  return {
    // Mode
    isSimpleMode,
    setIsSimpleMode,
    
    // Category data
    categories: {
      subjects: categories.subjects,
      systems: filteredSystems,
      topics: filteredTopics
    },
    simpleCategories,
    loading,
    error,
    
    // Simple mode state
    selectedSubject,
    setSelectedSubject,
    difficulty,
    setDifficulty,
    
    // Advanced mode state
    selectedSystem,
    setSelectedSystem,
    selectedTopic,
    setSelectedTopic,
    timing,
    setTiming,
    
    // Common state
    questionCount,
    setQuestionCount,
    
    // Derived data
    questionCounts,
    availableQuestions: availableQuestionCount,
    canStart,
    
    // Actions
    createNavigationState,
    createCustomQuiz
  };
};

// Helper function to get an emoji icon for a subject
function getSubjectIcon(subjectName) {
  const icons = {
    'Cardiology': '❤️',
    'Neurology': '🧠',
    'Pulmonology': '🫁',
    'Gastroenterology': '🍽️',
    'Endocrinology': '⚡',
    'Dermatology': '🧬',
    'Hematology': '🩸',
    'Immunology': '🛡️',
    'Infectious Disease': '🦠',
    'Nephrology': '🫘',
    'Oncology': '🔬',
    'Pediatrics': '👶',
    'Psychiatry': '🧠',
    'Rheumatology': '🦴',
    'Surgery': '🔪',
    'Obstetrics': '🤰',
    'Gynecology': '♀️',
    'Urology': '🚽',
    'Ophthalmology': '👁️',
    'Otolaryngology': '👂',
    'Emergency Medicine': '🚑',
    'Pharmacology': '💊',
    'Pathology': '🔬',
    'Radiology': '📷',
    'Anatomy': '🧍',
    'Physiology': '🧪',
    'Biochemistry': '⚗️',
    'Genetics': '🧬',
    'Microbiology': '🦠',
    'Epidemiology': '📊',
    'Biostatistics': '📈',
    'Ethics': '⚖️',
    'General Medicine': '🩺'
  };
  
  return icons[subjectName] || '📚';
}

export default useCustomQuizSetup;