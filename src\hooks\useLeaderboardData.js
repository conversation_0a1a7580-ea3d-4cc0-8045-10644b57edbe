import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { handleError } from '../utils/errorHandling';
import { startPerformanceMark, endPerformanceMark } from '../utils/performance';

/**
 * Hook for fetching and managing leaderboard data
 * @param {Object} options - Filter options
 * @returns {Object} Leaderboard data and state
 */
export const useLeaderboardData = (options = {}) => {
  const [leaderboardData, setLeaderboardData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userRank, setUserRank] = useState(null);
  const [filters, setFilters] = useState({
    quizType: options.quizType || null,
    categoryId: options.categoryId || null,
    difficulty: options.difficulty || null,
    timeFrame: options.timeFrame || 'all',
    limit: options.limit || 50,
    page: options.page || 1
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalEntries: 0,
    entriesPerPage: options.limit || 50
  });
  
  // Fetch leaderboard data
  const getLeaderboardData = useCallback(async () => {
    try {
      startPerformanceMark('fetchLeaderboard');
      setLoading(true);
      
      // Determine which view to use based on time frame
      let viewName = 'leaderboard_with_users';
      if (filters.timeFrame === 'day') {
        viewName = 'daily_leaderboard';
      } else if (filters.timeFrame === 'week') {
        viewName = 'weekly_leaderboard';
      } else if (filters.timeFrame === 'month') {
        viewName = 'monthly_leaderboard';
      }
      
      // Calculate pagination
      const from = (filters.page - 1) * filters.limit;
      const to = from + filters.limit - 1;
      
      // Build query
      let query = supabase
        .from(viewName)
        .select('*', { count: 'exact' });
      
      // Apply filters
      if (filters.quizType) {
        query = query.eq('quiz_type', filters.quizType);
      }
      
      if (filters.categoryId) {
        query = query.eq('category_id', filters.categoryId);
      }
      
      if (filters.difficulty) {
        query = query.eq('difficulty', filters.difficulty);
      }
      
      // If using the default view, apply time frame filter
      if (viewName === 'leaderboard_with_users') {
        if (filters.timeFrame === 'day') {
          query = query.gte('created_at', new Date(Date.now() - 86400000).toISOString());
        } else if (filters.timeFrame === 'week') {
          query = query.gte('created_at', new Date(Date.now() - 604800000).toISOString());
        } else if (filters.timeFrame === 'month') {
          query = query.gte('created_at', new Date(Date.now() - 2592000000).toISOString());
        }
      }
      
      // Order and paginate
      query = query
        .order('score', { ascending: false })
        .range(from, to);
      
      const { data, error: fetchError, count } = await query;
      
      if (fetchError) throw fetchError;
      
      // Format data for display
      const formattedEntries = data.map((entry, index) => ({
        id: entry.id,
        rank: entry.rank || from + index + 1,
        userId: entry.user_id,
        username: entry.username || 'Anonymous User',
        score: entry.score,
        maxScore: entry.max_score,
        accuracy: entry.accuracy,
        timeTaken: entry.time_taken,
        avatar: entry.avatar_url,
        country: entry.country || 'unknown',
        date: new Date(entry.created_at).toLocaleDateString(),
        quizType: entry.quiz_type,
        difficulty: entry.difficulty || 'mixed',
        questionCount: entry.question_count,
        categoryName: entry.category_name
      }));
      
      // Update state
      setLeaderboardData(formattedEntries);
      setPagination({
        currentPage: filters.page,
        totalPages: Math.ceil(count / filters.limit),
        totalEntries: count,
        entriesPerPage: filters.limit
      });
      
      // Get current user's rank
      await getUserRank();
      
      endPerformanceMark('fetchLeaderboard');
    } catch (err) {
      endPerformanceMark('fetchLeaderboard');
      const formattedError = handleError(err, 'fetchLeaderboard');
      console.error('Error in useLeaderboardData:', formattedError);
      setError(formattedError.message);
    } finally {
      setLoading(false);
    }
  }, [filters]);
  
  // Get user's rank
  const getUserRank = async () => {
    try {
      startPerformanceMark('getUserRank');
      
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        setUserRank(null);
        return;
      }
      
      // Use the database function to get user rank directly
      const { data, error } = await supabase.rpc('get_user_rank', {
        p_user_id: user.id,
        p_quiz_type: filters.quizType,
        p_difficulty: filters.difficulty,
        p_category_id: filters.categoryId,
        p_time_frame: filters.timeFrame
      });
      
      if (error) {
        console.error('Error calling get_user_rank function:', error);
        
        // Fallback method if the function call fails
        const userEntry = leaderboardData.find(entry => entry.userId === user.id);
        setUserRank(userEntry ? userEntry.rank : null);
      } else {
        setUserRank(data);
      }
      
      endPerformanceMark('getUserRank');
    } catch (err) {
      endPerformanceMark('getUserRank');
      const formattedError = handleError(err, 'getUserRank');
      console.error('Error getting user rank:', formattedError);
      // Don't set error state here to avoid disrupting the main leaderboard display
    }
  };
  
  // Initial data fetch
  useEffect(() => {
    getLeaderboardData();
  }, [getLeaderboardData]);
  
  // Set up real-time subscription for leaderboard updates
  useEffect(() => {
    const leaderboardSubscription = supabase
      .channel('leaderboard_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'leaderboard' },
        () => getLeaderboardData()
      )
      .subscribe();
    
    return () => {
      supabase.removeChannel(leaderboardSubscription);
    };
  }, [getLeaderboardData]);
  
  // Update filters
  const updateFilters = (newFilters) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      // Reset to page 1 if changing filter criteria
      page: newFilters.page || 
        (newFilters.quizType || newFilters.categoryId || 
         newFilters.difficulty || newFilters.timeFrame ? 1 : prev.page)
    }));
  };
  
  // Change page
  const changePage = (page) => {
    if (page < 1 || page > pagination.totalPages) return;
    updateFilters({ page });
  };
  
  // Get available time frames
  const getAvailableTimeFrames = () => [
    { value: 'day', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'all', label: 'All Time' }
  ];
  
  // Get available quiz types
  const getAvailableQuizTypes = () => [
    { value: null, label: 'All Types' },
    { value: 'quick', label: 'Quick Quiz' },
    { value: 'timed', label: 'Timed Test' },
    { value: 'custom', label: 'Custom Quiz' }
  ];
  
  // Get available difficulties
  const getAvailableDifficulties = () => [
    { value: null, label: 'All Difficulties' },
    { value: 'easy', label: 'Easy' },
    { value: 'medium', label: 'Medium' },
    { value: 'hard', label: 'Hard' },
    { value: 'mixed', label: 'Mixed' }
  ];
  
  return {
    leaderboardData,
    loading,
    error,
    userRank,
    filters,
    pagination,
    updateFilters,
    refreshData: getLeaderboardData,
    changePage,
    getAvailableTimeFrames,
    getAvailableQuizTypes,
    getAvailableDifficulties
  };
};

export default useLeaderboardData;