import { useState, useEffect, useCallback } from 'react';
import { fetchQuizQuestions } from '../lib/databaseUtils';

/**
 * Hook for fetching and managing quiz questions
 * @param {string} quizType - Type of quiz (quick, timed, custom)
 * @param {Object} options - Quiz options
 * @returns {Object} Quiz data and state
 */
export const useQuizData = (quizType, options = {}) => {
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [quizSettings, setQuizSettings] = useState({
    quizType,
    ...options
  });
  
  // Fetch questions
  const fetchQuestions = useCallback(async () => {
    try {
      setLoading(true);
      
      const fetchedQuestions = await fetchQuizQuestions({
        quizType,
        categoryId: options.categoryId,
        difficulty: options.difficulty,
        questionCount: options.questionCount,
        selectedTags: options.selectedTags
      });
      
      setQuestions(fetchedQuestions);
    } catch (err) {
      console.error('Error in useQuizData:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [quizType, options]);
  
  // Fetch questions on mount
  useEffect(() => {
    fetchQuestions();
  }, [fetchQuestions]);
  
  // Update quiz settings
  const updateQuizSettings = (newSettings) => {
    setQuizSettings(prev => ({
      ...prev,
      ...newSettings
    }));
  };
  
  return {
    questions,
    loading,
    error,
    quizSettings,
    updateQuizSettings,
    refetch: fetchQuestions
  };
};

export default useQuizData;