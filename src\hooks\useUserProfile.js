import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { handleError } from '../utils/errorHandling';
import { startPerformanceMark, endPerformanceMark } from '../utils/performance';

/**
 * Hook for fetching and managing user profile data
 * @returns {Object} User profile data and state
 */
export const useUserProfile = () => {
  const [profile, setProfile] = useState(null);
  const [statistics, setStatistics] = useState([]);
  const [achievements, setAchievements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Fetch user data
  useEffect(() => {
    let isMounted = true;
    let subscriptions = [];
    
    const getUserData = async () => {
      try {
        if (!isMounted) return;
        
        setLoading(true);
        
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          setProfile(null);
          setStatistics([]);
          setAchievements([]);
          setLoading(false);
          return;
        }
        
        // Fetch profile data
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        
        if (profileError) throw profileError;
        if (!isMounted) return;
        setProfile(profileData);
        
        // Fetch statistics
        const { data: statsData, error: statsError } = await supabase
          .from('user_statistics')
          .select(`
            id,
            category_id,
            questions_attempted,
            questions_correct,
            time_spent,
            last_attempted,
            tags:category_id(name, type)
          `)
          .eq('user_id', user.id);
        
        if (statsError) throw statsError;
        if (!isMounted) return;
        setStatistics(statsData || []);
        
        // Fetch achievements
        const { data: achievementsData, error: achievementsError } = await supabase
          .from('user_achievements')
          .select('*')
          .eq('user_id', user.id);
        
        if (achievementsError) throw achievementsError;
        if (!isMounted) return;
        
        // Map achievement IDs to full achievement data
        const mappedAchievements = (achievementsData || []).map(achievement => ({
          ...achievement,
          ...getAchievementDetails(achievement.achievement_id)
        }));
        
        setAchievements(mappedAchievements);
        
        // Set up real-time subscriptions if not already set up
        if (subscriptions.length === 0) {
          setupSubscriptions(user.id);
        }
      } catch (err) {
        console.error('Error in useUserProfile:', err);
        if (isMounted) {
          setError(handleError(err, 'useUserProfile').message);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };
    
    // Set up real-time subscriptions
    const setupSubscriptions = (userId) => {
      // Clean up any existing subscriptions
      cleanupSubscriptions();
      
      // Profile subscription
      const profileSubscription = supabase
        .channel('profile_changes')
        .on('postgres_changes', 
          { 
            event: '*', 
            schema: 'public', 
            table: 'profiles', 
            filter: `id=eq.${userId}` 
          },
          () => getUserData()
        )
        .subscribe();
      
      // Statistics subscription
      const statsSubscription = supabase
        .channel('stats_changes')
        .on('postgres_changes', 
          { 
            event: '*', 
            schema: 'public', 
            table: 'user_statistics', 
            filter: `user_id=eq.${userId}` 
          },
          () => getUserData()
        )
        .subscribe();
      
      // Achievements subscription
      const achievementsSubscription = supabase
        .channel('achievements_changes')
        .on('postgres_changes', 
          { 
            event: '*', 
            schema: 'public', 
            table: 'user_achievements', 
            filter: `user_id=eq.${userId}` 
          },
          () => getUserData()
        )
        .subscribe();
      
      // Store subscriptions for cleanup
      subscriptions = [
        profileSubscription,
        statsSubscription,
        achievementsSubscription
      ];
    };
    
    // Clean up subscriptions
    const cleanupSubscriptions = () => {
      subscriptions.forEach(subscription => {
        if (subscription) {
          supabase.removeChannel(subscription);
        }
      });
      subscriptions = [];
    };
    
    // Initial data fetch
    getUserData();
    
    // Cleanup function
    return () => {
      isMounted = false;
      cleanupSubscriptions();
    };
  }, []);
  
  // Get achievement details by ID
  const getAchievementDetails = (achievementId) => {
    const achievements = {
      'first_quiz_completed': {
        name: 'First Steps',
        description: 'Completed your first quiz',
        icon: '🎯'
      },
      'ten_quizzes_completed': {
        name: 'Dedicated Learner',
        description: 'Completed 10 quizzes',
        icon: '🔥'
      },
      'hundred_questions': {
        name: 'Century Club',
        description: 'Answered 100 questions',
        icon: '💯'
      },
      'perfect_score': {
        name: 'Perfect Score',
        description: 'Achieved 100% accuracy on a quiz',
        icon: '🏆'
      },
      'streak_3_days': {
        name: 'Consistency',
        description: 'Maintained a 3-day study streak',
        icon: '📆'
      },
      'streak_7_days': {
        name: 'Weekly Warrior',
        description: 'Maintained a 7-day study streak',
        icon: '🗓️'
      },
      'streak_30_days': {
        name: 'Monthly Master',
        description: 'Maintained a 30-day study streak',
        icon: '🏅'
      }
    };
    
    return achievements[achievementId] || {
      name: 'Unknown Achievement',
      description: 'Achievement details not found',
      icon: '❓'
    };
  };
  
  // Calculate derived statistics
  const derivedStats = {
    totalQuestions: profile?.total_questions || 0,
    correctAnswers: profile?.correct_answers || 0,
    accuracy: profile?.total_questions ? 
      ((profile.correct_answers / profile.total_questions) * 100).toFixed(1) : 0,
    quizCompleted: profile?.quiz_completed || 0,
    streakDays: profile?.streak_days || 0,
    categoryBreakdown: statistics?.map(stat => ({
      category: stat.tags?.name || 'Unknown',
      type: stat.tags?.type || 'subject',
      attempted: stat.questions_attempted,
      correct: stat.questions_correct,
      accuracy: stat.questions_attempted ? 
        ((stat.questions_correct / stat.questions_attempted) * 100).toFixed(1) : 0,
      timeSpent: stat.time_spent,
      lastAttempted: stat.last_attempted
    })) || []
  };
  
  // Update statistics after quiz completion
  const updateStatistics = async (quizResults) => {
    try {
      startPerformanceMark('updateStatistics');
      
      const { 
        quizType, 
        score, 
        maxScore, 
        timeTaken, 
        categoryId, 
        difficulty = 'mixed',
        questionCount
      } = quizResults;
      
      // Add to leaderboard (this will trigger database functions to update statistics)
      const { data, error } = await supabase
        .from('leaderboard')
        .insert({
          quiz_type: quizType,
          score,
          max_score: maxScore,
          accuracy: maxScore > 0 ? ((score / maxScore) * 100).toFixed(2) : 0,
          time_taken: timeTaken,
          category_id: categoryId,
          difficulty,
          question_count: questionCount
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Log activity
      await supabase
        .from('user_activity_log')
        .insert({
          activity_type: 'quiz_completed',
          activity_data: {
            quizType,
            score,
            maxScore,
            timeTaken,
            categoryId,
            difficulty,
            questionCount
          }
        });
      
      endPerformanceMark('updateStatistics');
      return true;
    } catch (err) {
      endPerformanceMark('updateStatistics');
      console.error('Error updating statistics:', err);
      setError(handleError(err, 'updateStatistics').message);
      return false;
    }
  };
  
  return {
    profile,
    statistics: derivedStats,
    achievements,
    loading,
    error,
    updateStatistics
  };
};

export default useUserProfile;