import { supabase } from './supabase';
import { executeQueryWithPagination } from '../utils/queryOptimization';
import { handleError } from '../utils/errorHandling';
import { startPerformanceMark, endPerformanceMark } from '../utils/performance';

/**
 * Database utility functions for the USMLE Trivia App
 * These functions provide a clean interface for interacting with the Supabase database
 */

// =============================================
// USER PROFILE FUNCTIONS
// =============================================

/**
 * Fetch user profile with statistics
 * @param {string} userId - User ID
 * @returns {Promise<Object>} User profile with statistics
 */
export const fetchUserProfile = async (userId) => {
  try {
    startPerformanceMark('fetchUserProfile');
    
    if (!userId) {
      const { data: { user } } = await supabase.auth.getUser();
      userId = user?.id;
      
      if (!userId) {
        throw new Error('User not authenticated');
      }
    }
    
    // Fetch profile data
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (profileError) throw profileError;
    
    endPerformanceMark('fetchUserProfile');
    return profile;
  } catch (error) {
    endPerformanceMark('fetchUserProfile');
    const formattedError = handleError(error, 'fetchUserProfile');
    throw formattedError;
  }
};

/**
 * Fetch user statistics by category
 * @param {string} userId - User ID
 * @returns {Promise<Array>} User statistics by category
 */
export const fetchUserStatistics = async (userId) => {
  try {
    startPerformanceMark('fetchUserStatistics');
    
    if (!userId) {
      const { data: { user } } = await supabase.auth.getUser();
      userId = user?.id;
      
      if (!userId) {
        throw new Error('User not authenticated');
      }
    }
    
    // Fetch statistics by category
    const { data: statistics, error: statsError } = await supabase
      .from('user_statistics')
      .select(`
        id,
        category_id,
        questions_attempted,
        questions_correct,
        time_spent,
        last_attempted,
        tags:category_id(name, type)
      `)
      .eq('user_id', userId);
    
    if (statsError) throw statsError;
    
    endPerformanceMark('fetchUserStatistics');
    return statistics;
  } catch (error) {
    endPerformanceMark('fetchUserStatistics');
    const formattedError = handleError(error, 'fetchUserStatistics');
    throw formattedError;
  }
};

/**
 * Fetch user achievements
 * @param {string} userId - User ID
 * @returns {Promise<Array>} User achievements
 */
export const fetchUserAchievements = async (userId) => {
  try {
    startPerformanceMark('fetchUserAchievements');
    
    if (!userId) {
      const { data: { user } } = await supabase.auth.getUser();
      userId = user?.id;
      
      if (!userId) {
        throw new Error('User not authenticated');
      }
    }
    
    // Fetch achievements
    const { data: achievements, error: achievementsError } = await supabase
      .from('user_achievements')
      .select('*')
      .eq('user_id', userId);
    
    if (achievementsError) throw achievementsError;
    
    // Map achievement IDs to full achievement data
    const mappedAchievements = achievements.map(achievement => ({
      ...achievement,
      ...getAchievementDetails(achievement.achievement_id)
    }));
    
    endPerformanceMark('fetchUserAchievements');
    return mappedAchievements;
  } catch (error) {
    endPerformanceMark('fetchUserAchievements');
    const formattedError = handleError(error, 'fetchUserAchievements');
    throw formattedError;
  }
};

/**
 * Get achievement details by ID
 * @param {string} achievementId - Achievement ID
 * @returns {Object} Achievement details
 */
export const getAchievementDetails = (achievementId) => {
  const achievements = {
    'first_quiz_completed': {
      name: 'First Steps',
      description: 'Completed your first quiz',
      icon: '🎯'
    },
    'ten_quizzes_completed': {
      name: 'Dedicated Learner',
      description: 'Completed 10 quizzes',
      icon: '🔥'
    },
    'hundred_questions': {
      name: 'Century Club',
      description: 'Answered 100 questions',
      icon: '💯'
    },
    'perfect_score': {
      name: 'Perfect Score',
      description: 'Achieved 100% accuracy on a quiz',
      icon: '🏆'
    }
  };
  
  return achievements[achievementId] || {
    name: 'Unknown Achievement',
    description: 'Achievement details not found',
    icon: '❓'
  };
};

// =============================================
// LEADERBOARD FUNCTIONS
// =============================================

/**
 * Fetch leaderboard data with filters
 * @param {Object} options - Filter options
 * @returns {Promise<Object>} Leaderboard entries and total count
 */
export const fetchLeaderboard = async (options = {}) => {
  try {
    startPerformanceMark('fetchLeaderboard');
    
    const { 
      quizType = null,
      categoryId = null, 
      difficulty = null,
      limit = 50,
      page = 1,
      timeFrame = 'all' // 'day', 'week', 'month', 'all'
    } = options;
    
    // Determine which view to use based on time frame
    let viewName = 'leaderboard_with_users';
    if (timeFrame === 'day') {
      viewName = 'daily_leaderboard';
    } else if (timeFrame === 'week') {
      viewName = 'weekly_leaderboard';
    } else if (timeFrame === 'month') {
      viewName = 'monthly_leaderboard';
    }
    
    // Create a query builder function
    const buildQuery = () => {
      let query = supabase.from(viewName).select('*');
      
      // Apply filters
      if (quizType) {
        query = query.eq('quiz_type', quizType);
      }
      
      if (categoryId) {
        query = query.eq('category_id', categoryId);
      }
      
      if (difficulty) {
        query = query.eq('difficulty', difficulty);
      }
      
      // If using the default view, apply time frame filter
      if (viewName === 'leaderboard_with_users') {
        if (timeFrame === 'day') {
          query = query.gte('created_at', new Date(Date.now() - 86400000).toISOString());
        } else if (timeFrame === 'week') {
          query = query.gte('created_at', new Date(Date.now() - 604800000).toISOString());
        } else if (timeFrame === 'month') {
          query = query.gte('created_at', new Date(Date.now() - 2592000000).toISOString());
        }
      }
      
      // Order by score
      return query.order('score', { ascending: false });
    };
    
    // Execute query with pagination
    const result = await executeQueryWithPagination(buildQuery, page, limit);
    
    // Format data for display
    const formattedEntries = result.data.map((entry, index) => ({
      id: entry.id,
      rank: entry.rank || ((page - 1) * limit) + index + 1,
      userId: entry.user_id,
      username: entry.username || 'Anonymous User',
      score: entry.score,
      maxScore: entry.max_score,
      accuracy: entry.accuracy,
      timeTaken: entry.time_taken,
      avatar: entry.avatar_url,
      country: entry.country || 'unknown',
      date: new Date(entry.created_at).toLocaleDateString(),
      quizType: entry.quiz_type,
      difficulty: entry.difficulty || 'mixed',
      questionCount: entry.question_count,
      categoryName: entry.category_name
    }));
    
    endPerformanceMark('fetchLeaderboard');
    
    return {
      entries: formattedEntries,
      totalCount: result.pagination.totalCount,
      pagination: result.pagination
    };
  } catch (error) {
    endPerformanceMark('fetchLeaderboard');
    const formattedError = handleError(error, 'fetchLeaderboard');
    throw formattedError;
  }
};

/**
 * Get user's rank on the leaderboard
 * @param {string} userId - User ID
 * @param {Object} options - Filter options
 * @returns {Promise<number|null>} User's rank or null if not found
 */
export const getUserRank = async (userId, options = {}) => {
  try {
    startPerformanceMark('getUserRank');
    
    if (!userId) {
      const { data: { user } } = await supabase.auth.getUser();
      userId = user?.id;
      
      if (!userId) {
        return null;
      }
    }
    
    const { 
      quizType = null,
      categoryId = null, 
      difficulty = null,
      timeFrame = 'all'
    } = options;
    
    // Use the database function to get user rank directly
    const { data, error } = await supabase.rpc('get_user_rank', {
      p_user_id: userId,
      p_quiz_type: quizType,
      p_difficulty: difficulty,
      p_category_id: categoryId,
      p_time_frame: timeFrame
    });
    
    if (error) {
      console.error('Error calling get_user_rank function:', error);
      
      // Fallback method if the function call fails
      const leaderboardData = await fetchLeaderboard(options);
      const userEntry = leaderboardData.entries.find(entry => entry.userId === userId);
      
      endPerformanceMark('getUserRank');
      return userEntry ? userEntry.rank : null;
    }
    
    endPerformanceMark('getUserRank');
    return data;
  } catch (error) {
    endPerformanceMark('getUserRank');
    const formattedError = handleError(error, 'getUserRank');
    return null;
  }
};

/**
 * Add entry to leaderboard
 * @param {Object} quizResult - Quiz result data
 * @returns {Promise<Object>} Created leaderboard entry
 */
export const addLeaderboardEntry = async (quizResult) => {
  try {
    startPerformanceMark('addLeaderboardEntry');
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    const { 
      quizType, 
      score, 
      maxScore, 
      timeTaken, 
      categoryId = null, 
      difficulty = 'mixed',
      questionCount
    } = quizResult;
    
    // Calculate accuracy
    const accuracy = maxScore > 0 ? ((score / maxScore) * 100).toFixed(2) : 0;
    
    // Add entry to leaderboard
    const { data, error } = await supabase
      .from('leaderboard')
      .insert({
        user_id: user.id,
        quiz_type: quizType,
        score,
        max_score: maxScore,
        accuracy,
        time_taken: timeTaken,
        category_id: categoryId,
        difficulty,
        question_count: questionCount
      })
      .select()
      .single();
    
    if (error) throw error;
    
    endPerformanceMark('addLeaderboardEntry');
    return data;
  } catch (error) {
    endPerformanceMark('addLeaderboardEntry');
    const formattedError = handleError(error, 'addLeaderboardEntry');
    throw formattedError;
  }
};

// =============================================
// QUIZ FUNCTIONS
// =============================================

/**
 * Fetch questions for a quiz
 * @param {Object} options - Quiz options
 * @returns {Promise<Array>} Quiz questions
 */
export const fetchQuizQuestions = async (options = {}) => {
  try {
    startPerformanceMark('fetchQuizQuestions');
    
    const {
      quizType = 'quick',
      categoryId = null,
      difficulty = 'mixed',
      questionCount = null,
      selectedTags = []
    } = options;
    
    // Get question count based on quiz type
    let count = questionCount;
    if (!count) {
      switch (quizType) {
        case 'quick':
          count = 10;
          break;
        case 'timed':
          count = 20;
          break;
        case 'custom':
          count = 10; // Default for custom
          break;
        default:
          count = 10;
      }
    }
    
    // Get user's previously seen questions
    const { data: { user } } = await supabase.auth.getUser();
    
    let seenQuestionIds = [];
    if (user) {
      const { data: seenData } = await supabase
        .from('user_seen_questions')
        .select('question_id')
        .eq('user_id', user.id)
        .limit(500); // Limit to avoid huge arrays
      
      if (seenData) {
        seenQuestionIds = seenData.map(item => item.question_id);
      }
    }
    
    // Build query
    let query = supabase
      .from('questions')
      .select(`
        id,
        question_text,
        options,
        correct_option_id,
        explanation,
        difficulty,
        question_tags(
          tag_id,
          tags(id, name, type)
        )
      `);
    
    // Apply category filter if specified
    if (categoryId) {
      query = query.eq('question_tags.tag_id', categoryId);
    }
    
    // Apply difficulty filter if specified
    if (difficulty && difficulty !== 'mixed') {
      query = query.eq('difficulty', difficulty);
    }
    
    // Apply tag filters if specified
    if (selectedTags && selectedTags.length > 0) {
      query = query.or(
        selectedTags.map(tagId => `question_tags.tag_id.eq.${tagId}`).join(',')
      );
    }
    
    // Exclude seen questions if possible
    if (seenQuestionIds.length > 0 && seenQuestionIds.length < 400) {
      query = query.not('id', 'in', `(${seenQuestionIds.join(',')})`);
    }
    
    // Order randomly and limit
    query = query
      .order('id', { ascending: false })
      .limit(count);
    
    const { data, error } = await query;
    
    if (error) throw error;
    
    // If not enough unseen questions, fetch any questions
    if (data.length < count) {
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('questions')
        .select(`
          id,
          question_text,
          options,
          correct_option_id,
          explanation,
          difficulty,
          question_tags(
            tag_id,
            tags(id, name, type)
          )
        `)
        .order('id', { ascending: false })
        .limit(count - data.length);
      
      if (fallbackError) throw fallbackError;
      
      // Combine data sets, avoiding duplicates
      const existingIds = data.map(q => q.id);
      const uniqueFallbackData = fallbackData.filter(q => !existingIds.includes(q.id));
      data.push(...uniqueFallbackData);
    }
    
    // Format questions for quiz
    const formattedQuestions = data.map(question => ({
      id: question.id,
      text: question.question_text,
      options: question.options,
      correctOptionId: question.correct_option_id,
      explanation: question.explanation,
      difficulty: question.difficulty,
      tags: question.question_tags.map(tag => ({
        id: tag.tags?.id,
        name: tag.tags?.name,
        type: tag.tags?.type
      })).filter(tag => tag.id) // Filter out null tags
    }));
    
    // Shuffle questions
    const shuffledQuestions = formattedQuestions.sort(() => Math.random() - 0.5);
    
    // Mark questions as seen
    if (user) {
      const seenQuestions = shuffledQuestions.map(q => ({
        user_id: user.id,
        question_id: q.id,
        seen_at: new Date().toISOString()
      }));
      
      // Use upsert to avoid duplicates
      await supabase
        .from('user_seen_questions')
        .upsert(seenQuestions, { onConflict: ['user_id', 'question_id'] });
    }
    
    endPerformanceMark('fetchQuizQuestions');
    return shuffledQuestions;
  } catch (error) {
    endPerformanceMark('fetchQuizQuestions');
    const formattedError = handleError(error, 'fetchQuizQuestions');
    throw formattedError;
  }
};

/**
 * Fetch available categories with question counts
 * @returns {Promise<Object>} Categories with question counts
 */
export const fetchCategories = async () => {
  try {
    startPerformanceMark('fetchCategories');
    
    // Fetch subjects
    const { data: subjectsData, error: subjectsError } = await supabase
      .from('tags')
      .select('id, name, type')
      .eq('type', 'subject')
      .order('name');
    
    if (subjectsError) throw subjectsError;
    
    // Fetch systems
    const { data: systemsData, error: systemsError } = await supabase
      .from('tags')
      .select('id, name, type')
      .eq('type', 'system')
      .order('name');
    
    if (systemsError) throw systemsError;
    
    // Fetch topics
    const { data: topicsData, error: topicsError } = await supabase
      .from('tags')
      .select('id, name, type')
      .eq('type', 'topic')
      .order('name');
    
    if (topicsError) throw topicsError;
    
    // Fetch question counts for each tag
    const { data: tagCounts, error: countError } = await supabase
      .from('tag_question_counts')
      .select('id, question_count');
    
    if (countError) throw countError;
    
    // Create a map of tag ID to question count
    const countMap = {};
    tagCounts.forEach(tag => {
      countMap[tag.id] = tag.question_count;
    });
    
    // Add question counts to tags
    const subjectsWithCounts = subjectsData.map(subject => ({
      ...subject,
      questionCount: countMap[subject.id] || 0
    }));
    
    const systemsWithCounts = systemsData.map(system => ({
      ...system,
      questionCount: countMap[system.id] || 0
    }));
    
    const topicsWithCounts = topicsData.map(topic => ({
      ...topic,
      questionCount: countMap[topic.id] || 0
    }));
    
    endPerformanceMark('fetchCategories');
    return {
      subjects: subjectsWithCounts,
      systems: systemsWithCounts,
      topics: topicsWithCounts
    };
  } catch (error) {
    endPerformanceMark('fetchCategories');
    const formattedError = handleError(error, 'fetchCategories');
    throw formattedError;
  }
};

/**
 * Get available question count based on filters
 * @param {Object} filters - Filter options
 * @returns {Promise<number>} Available question count
 */
export const getAvailableQuestionCount = async (filters = {}) => {
  try {
    startPerformanceMark('getAvailableQuestionCount');
    
    const {
      difficulty = 'mixed',
      selectedTags = []
    } = filters;
    
    if (selectedTags.length === 0) {
      // No filters selected, get total question count
      const { data, error } = await supabase
        .from('questions')
        .select('count');
      
      if (error) throw error;
      
      endPerformanceMark('getAvailableQuestionCount');
      return data[0].count;
    }
    
    // Build query to count questions matching filters
    let query = supabase
      .from('questions')
      .select('id', { count: 'exact' });
    
    // Apply difficulty filter if not mixed
    if (difficulty !== 'mixed') {
      query = query.eq('difficulty', difficulty);
    }
    
    // Apply tag filters
    if (selectedTags.length > 0) {
      query = query.or(
        selectedTags.map(tagId => `question_tags.tag_id.eq.${tagId}`).join(',')
      );
    }
    
    const { count, error } = await query;
    
    if (error) throw error;
    
    endPerformanceMark('getAvailableQuestionCount');
    return count || 0;
  } catch (error) {
    endPerformanceMark('getAvailableQuestionCount');
    const formattedError = handleError(error, 'getAvailableQuestionCount');
    return 0;
  }
};

// =============================================
// REAL-TIME FUNCTIONS
// =============================================

/**
 * Set up real-time subscriptions
 * @param {string} userId - User ID
 * @returns {Function} Cleanup function
 */
export const setupRealTimeSubscriptions = (userId) => {
  if (!userId) return () => {};
  
  // User activity subscription
  const userActivitySubscription = supabase
    .channel('user_activity')
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'user_activity_log',
        filter: `user_id=eq.${userId}`
      },
      (payload) => {
        console.log('User activity update:', payload);
        // Handle activity update
      }
    )
    .subscribe();
  
  // Live stats subscription
  const liveStatsSubscription = supabase
    .channel('live_stats')
    .on('postgres_changes',
      { 
        event: '*', 
        schema: 'public', 
        table: 'user_statistics',
        filter: `user_id=eq.${userId}`
      },
      (payload) => {
        console.log('Stats update:', payload);
        // Handle stats update
      }
    )
    .subscribe();
  
  // Leaderboard subscription
  const leaderboardSubscription = supabase
    .channel('leaderboard')
    .on('postgres_changes',
      { 
        event: '*', 
        schema: 'public', 
        table: 'leaderboard'
      },
      (payload) => {
        console.log('Leaderboard update:', payload);
        // Handle leaderboard update
      }
    )
    .subscribe();
  
  // User presence tracking
  const presenceChannel = supabase.channel('online_users');
  
  presenceChannel
    .on('presence', { event: 'sync' }, () => {
      const newState = presenceChannel.presenceState();
      console.log('Online users:', newState);
      // Update online users state
    })
    .on('presence', { event: 'join' }, ({ newPresences }) => {
      console.log('User joined:', newPresences);
      // Handle user join
    })
    .on('presence', { event: 'leave' }, ({ leftPresences }) => {
      console.log('User left:', leftPresences);
      // Handle user leave
    })
    .subscribe(async (status) => {
      if (status === 'SUBSCRIBED') {
        await presenceChannel.track({ user_id: userId, online_at: new Date().toISOString() });
      }
    });
  
  // Return cleanup function
  return () => {
    supabase.removeChannel(userActivitySubscription);
    supabase.removeChannel(liveStatsSubscription);
    supabase.removeChannel(leaderboardSubscription);
    supabase.removeChannel(presenceChannel);
  };
};

/**
 * Log user activity
 * @param {string} activityType - Type of activity
 * @param {Object} activityData - Activity data
 * @returns {Promise<Object>} Created activity log entry
 */
export const logUserActivity = async (activityType, activityData = {}) => {
  try {
    startPerformanceMark('logUserActivity');
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    const { data, error } = await supabase
      .from('user_activity_log')
      .insert({
        user_id: user.id,
        activity_type: activityType,
        activity_data: activityData
      })
      .select()
      .single();
    
    if (error) throw error;
    
    endPerformanceMark('logUserActivity');
    return data;
  } catch (error) {
    endPerformanceMark('logUserActivity');
    const formattedError = handleError(error, 'logUserActivity');
    throw formattedError;
  }
};