import { createClient } from '@supabase/supabase-js';
import { config, debugLog } from '../config/environment';

// Supabase configuration from environment
const supabaseUrl = config.supabaseUrl;
const supabaseKey = config.supabaseKey;

// Validate configuration
if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase environment variables are missing');
  console.error('URL:', supabaseUrl ? 'OK' : 'MISSING');
  console.error('KEY:', supabaseKey ? 'OK' : 'MISSING');
}

// Create Supabase client with fallback values for development
export const supabase = createClient(
  supabaseUrl || 'https://placeholder.supabase.co',
  supabaseKey || 'placeholder-key',
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    }
  }
);

// Configuration status
export const isConfigured = Boolean(supabaseUrl && supabaseKey);

/**
 * Test function to verify database connection
 * @returns {Promise<Object>} Connection test result
 */
export const testConnection = async () => {
  try {
    debugLog('Testing database connection...');
    debugLog('URL:', supabaseUrl);
    debugLog('Key present:', !!supabaseKey);
    
    // Simple test query
    const { data, error } = await supabase
      .from('tags')
      .select('id, name')
      .limit(1);
    
    if (error) {
      console.error('❌ [Supabase] Connection test failed:', error);
      return {
        success: false,
        message: `Database error: ${error.message}`,
        details: error
      };
    }
    
    debugLog('Connection test successful');
    debugLog('Sample data:', data);
    
    return {
      success: true,
      message: 'Database connection successful',
      data: data
    };
  } catch (error) {
    console.error('💥 [Supabase] Connection test exception:', error);
    return {
      success: false,
      message: `Connection failed: ${error.message}`,
      details: error
    };
  }
};

// Run test on module load in development
if (import.meta.env.DEV) {
  testConnection().then(result => {
    if (result.success) {
      debugLog('Database ready!');
    } else {
      console.error('🚨 [Supabase] Database connection failed on startup');
    }
  });
}
