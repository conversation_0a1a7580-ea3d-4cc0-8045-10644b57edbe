import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { ChevronRight } from 'lucide-react'
import { useCustomQuizSetup } from '../hooks/useCustomQuizSetup'
import ModeToggle from '../components/quiz/setup/ModeToggle'
import SimpleModeSetup from '../components/quiz/setup/SimpleModeSetup'
import AdvancedModeSetup from '../components/quiz/setup/AdvancedModeSetup'

const DIFFICULTY_OPTIONS = [
  { value: '', label: 'Mixed' },
  { value: 'easy', label: 'Easy' },
  { value: 'medium', label: 'Medium' },
  { value: 'hard', label: 'Hard' },
]

const TIMING_OPTIONS = [
  { value: 'timed', label: 'Timed (1 min/question)' },
  { value: 'self', label: 'Self-paced' },
]

/**
 * Custom Quiz Setup Component
 * Allows users to configure and start custom quizzes
 */
const CustomQuizSetup = () => {
  const navigate = useNavigate()
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const {
    // Mode
    isSimpleMode,
    setIsSimpleMode,
    
    // Category data
    categories,
    simpleCategories,
    loading,
    error,
    
    // Simple mode state
    selectedSubject,
    setSelectedSubject,
    difficulty,
    setDifficulty,
    
    // Advanced mode state
    selectedSystem,
    setSelectedSystem,
    selectedTopic,
    setSelectedTopic,
    timing,
    setTiming,
    
    // Common state
    questionCount,
    setQuestionCount,
    
    // Derived data
    questionCounts,
    availableQuestions,
    canStart,
    
    // Actions
    createNavigationState,
    createCustomQuiz
  } = useCustomQuizSetup()

  const handleStart = async (e) => {
    e.preventDefault()
    
    if (!canStart || isSubmitting) {
      return;
    }
    
    setIsSubmitting(true)
    
    try {
      // For direct navigation with state
      const navigationState = createNavigationState()
      navigate('/quiz/custom', { state: navigationState })
      
      // Alternative approach: fetch questions first and then navigate
      // const quizData = await createCustomQuiz();
      // if (quizData) {
      //   navigate('/quiz/custom', { state: quizData });
      // }
    } catch (err) {
      console.error('Error starting custom quiz:', err);
      setIsSubmitting(false);
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-green-900/20 to-emerald-900/20 flex items-center justify-center p-4">
      <form
        onSubmit={handleStart}
        className="w-full max-w-lg bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-2xl shadow-xl p-8 space-y-6"
        aria-label="Custom Quiz Setup"
      >
        <h1 className="text-2xl font-bold text-white mb-2">
          Custom Quiz Setup
        </h1>
        <p className="text-gray-300 mb-4">
          Select your desired filters and options to create a personalized quiz session.
        </p>

        <ModeToggle 
          isSimpleMode={isSimpleMode} 
          onModeChange={setIsSimpleMode} 
        />

        {error && (
          <div className="text-red-400 bg-red-900/20 p-4 rounded-lg border border-red-800">
            <p className="font-medium">Error</p>
            <p className="text-sm">{error}</p>
          </div>
        )}

        {isSimpleMode ? (
          <SimpleModeSetup
            simpleCategories={simpleCategories}
            selectedSubject={selectedSubject}
            onSubjectChange={setSelectedSubject}
            questionCount={questionCount}
            onQuestionCountChange={setQuestionCount}
            difficulty={difficulty}
            onDifficultyChange={setDifficulty}
            difficultyOptions={DIFFICULTY_OPTIONS}
          />
        ) : (
          <AdvancedModeSetup
            subjects={categories.subjects}
            systems={categories.systems}
            topics={categories.topics}
            selectedSubject={selectedSubject}
            selectedSystem={selectedSystem}
            selectedTopic={selectedTopic}
            onSubjectChange={setSelectedSubject}
            onSystemChange={setSelectedSystem}
            onTopicChange={setSelectedTopic}
            difficulty={difficulty}
            onDifficultyChange={setDifficulty}
            questionCount={questionCount}
            onQuestionCountChange={setQuestionCount}
            timing={timing}
            onTimingChange={setTiming}
            questionCounts={questionCounts}
            availableQuestions={availableQuestions}
            difficultyOptions={DIFFICULTY_OPTIONS}
            timingOptions={TIMING_OPTIONS}
            loading={loading}
          />
        )}

        <button
          type="submit"
          disabled={!canStart || (!isSimpleMode && availableQuestions === 0) || isSubmitting}
          className={`w-full flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-semibold text-white transition-all ${
            canStart && (isSimpleMode || availableQuestions > 0) && !isSubmitting
              ? 'bg-green-600 hover:bg-green-700 shadow-lg' 
              : 'bg-gray-600 opacity-50 cursor-not-allowed'
          }`}
        >
          {isSubmitting ? (
            <>
              <span className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full mr-2"></span>
              Preparing Quiz...
            </>
          ) : (
            <>
              Start Custom Quiz
              <ChevronRight className="w-5 h-5" />
            </>
          )}
        </button>
      </form>
    </div>
  )
}

export default CustomQuizSetup
