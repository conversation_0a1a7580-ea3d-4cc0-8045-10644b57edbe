import React from 'react';
import LeaderboardTable from '../components/leaderboard/LeaderboardTable';
import { useRealTime } from '../contexts/RealTimeContext';

/**
 * Leaderboard page component
 * @returns {JSX.Element} Leaderboard page component
 */
const Leaderboard = () => {
  const { isConnected } = useRealTime();
  
  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">Leaderboard</h1>
        <p className="text-gray-300">
          Compare your performance with other medical students worldwide.
          {isConnected && (
            <span className="ml-2 inline-flex items-center">
              <span className="h-2 w-2 bg-green-500 rounded-full mr-1"></span>
              <span className="text-green-400 text-sm">Live updates enabled</span>
            </span>
          )}
        </p>
      </div>
      
      <LeaderboardTable />
      
      <div className="mt-8 p-4 bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-lg shadow-lg">
        <h2 className="text-xl font-bold text-white mb-4">How Scoring Works</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 bg-gray-700 bg-opacity-40 rounded-lg">
            <h3 className="text-lg font-medium text-blue-300 mb-2">Accuracy</h3>
            <p className="text-gray-300">
              Your accuracy percentage affects your overall score. Higher accuracy means higher ranking.
            </p>
          </div>
          <div className="p-4 bg-gray-700 bg-opacity-40 rounded-lg">
            <h3 className="text-lg font-medium text-blue-300 mb-2">Difficulty</h3>
            <p className="text-gray-300">
              Harder questions are worth more points. Challenge yourself with difficult quizzes to climb the ranks.
            </p>
          </div>
          <div className="p-4 bg-gray-700 bg-opacity-40 rounded-lg">
            <h3 className="text-lg font-medium text-blue-300 mb-2">Consistency</h3>
            <p className="text-gray-300">
              Regular practice improves your streak and overall ranking. Try to maintain a daily study habit.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Leaderboard;