import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { User, <PERSON><PERSON><PERSON>, History, Settings, LogOut } from 'lucide-react';
import { supabase } from '../lib/supabase';
import UserStatistics from '../components/profile/UserStatistics';
import QuizHistory from '../components/profile/QuizHistory';
import LoadingState from '../components/ui/LoadingState';

/**
 * Profile Page Component
 * Displays user profile, statistics, and quiz history
 */
const Profile = () => {
  const [activeTab, setActiveTab] = useState('statistics');
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  
  // Fetch user data
  useEffect(() => {
    const getUser = async () => {
      try {
        setLoading(true);
        
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        setUser(user);
        
        if (user) {
          // Fetch profile data
          const { data: profileData } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();
          
          setProfile(profileData);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    getUser();
  }, []);
  
  // Handle sign out
  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      window.location.href = '/';
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };
  
  // Loading state
  if (loading) {
    return <LoadingState message="Loading profile..." />;
  }
  
  // Not authenticated state
  if (!user) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 via-green-900/20 to-emerald-900/20 p-4">
        <div className="w-full max-w-md bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-lg shadow-lg p-6 text-center">
          <h2 className="text-2xl font-bold text-white mb-2">Sign In Required</h2>
          <p className="text-gray-300 mb-4">Please sign in to view your profile.</p>
          <button 
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-white"
            onClick={() => window.location.href = '/login'}
          >
            Sign In
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-green-900/20 to-emerald-900/20 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Profile Header */}
        <div className="bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-lg p-6 mb-6">
          <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4">
            {/* Avatar */}
            <div className="w-24 h-24 rounded-full bg-blue-600 flex items-center justify-center">
              {profile?.avatar_url ? (
                <img 
                  src={profile.avatar_url} 
                  alt={profile.username || user.email} 
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <User className="w-12 h-12 text-white" />
              )}
            </div>
            
            {/* User Info */}
            <div className="flex-1 text-center sm:text-left">
              <h1 className="text-2xl font-bold text-white mb-1">
                {profile?.username || user.email.split('@')[0]}
              </h1>
              <p className="text-gray-300 mb-3">{user.email}</p>
              
              <div className="flex flex-wrap justify-center sm:justify-start gap-2">
                <span className="px-3 py-1 bg-blue-900/30 text-blue-300 text-sm rounded-full">
                  {profile?.streak_days || 0} Day Streak
                </span>
                <span className="px-3 py-1 bg-green-900/30 text-green-300 text-sm rounded-full">
                  {profile?.quiz_completed || 0} Quizzes
                </span>
                <span className="px-3 py-1 bg-purple-900/30 text-purple-300 text-sm rounded-full">
                  Member since {new Date(user.created_at || Date.now()).toLocaleDateString()}
                </span>
              </div>
            </div>
            
            {/* Sign Out Button */}
            <button
              onClick={handleSignOut}
              className="flex items-center px-3 py-1 bg-red-900/30 text-red-300 text-sm rounded-full hover:bg-red-900/50 transition-colors"
            >
              <LogOut className="w-4 h-4 mr-1" />
              Sign Out
            </button>
          </div>
        </div>
        
        {/* Tabs */}
        <div className="flex mb-6 bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-lg p-1">
          <button
            className={`flex-1 flex items-center justify-center gap-2 py-3 rounded-md transition-colors ${
              activeTab === 'statistics'
                ? 'bg-blue-600 text-white'
                : 'text-gray-300 hover:bg-gray-700'
            }`}
            onClick={() => setActiveTab('statistics')}
          >
            <BarChart className="w-5 h-5" />
            <span>Statistics</span>
          </button>
          <button
            className={`flex-1 flex items-center justify-center gap-2 py-3 rounded-md transition-colors ${
              activeTab === 'history'
                ? 'bg-blue-600 text-white'
                : 'text-gray-300 hover:bg-gray-700'
            }`}
            onClick={() => setActiveTab('history')}
          >
            <History className="w-5 h-5" />
            <span>History</span>
          </button>
          <button
            className={`flex-1 flex items-center justify-center gap-2 py-3 rounded-md transition-colors ${
              activeTab === 'settings'
                ? 'bg-blue-600 text-white'
                : 'text-gray-300 hover:bg-gray-700'
            }`}
            onClick={() => setActiveTab('settings')}
          >
            <Settings className="w-5 h-5" />
            <span>Settings</span>
          </button>
        </div>
        
        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
        >
          {activeTab === 'statistics' && <UserStatistics />}
          {activeTab === 'history' && <QuizHistory />}
          {activeTab === 'settings' && (
            <div className="bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-lg p-6">
              <h3 className="text-xl font-bold text-white mb-4">Settings</h3>
              <p className="text-gray-300">
                Profile settings will be available in a future update.
              </p>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default Profile;