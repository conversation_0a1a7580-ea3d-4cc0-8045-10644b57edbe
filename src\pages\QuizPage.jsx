import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, Home, Volume2, VolumeX } from 'lucide-react';
import { useQuizData } from '../hooks/useQuizData';
import { useUserProfile } from '../hooks/useUserProfile';
import { supabase } from '../lib/supabase';
import QuizProgressBar from '../components/quiz/QuizProgressBar';

const ICONS = ['🅰️', '🅱️', '🇨', '🇩'];

/**
 * Quiz Page Component
 * Handles all quiz types: quick, timed, and custom
 */
const QuizPage = () => {
  const { quizType } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  
  // Get quiz configuration from location state or use defaults
  const config = location.state || getDefaultConfig(quizType);
  
  // Get current user
  const [user, setUser] = useState(null);
  
  // Quiz state
  const [currentIdx, setCurrentIdx] = useState(0);
  const [selectedOption, setSelectedOption] = useState(null);
  const [isAnswered, setIsAnswered] = useState(false);
  const [timedOut, setTimedOut] = useState(false);
  const [answers, setAnswers] = useState([]);
  const [showResults, setShowResults] = useState(false);
  const [timeLeft, setTimeLeft] = useState(getTimePerQuestion(quizType, config));
  const [isMuted, setIsMuted] = useState(() => localStorage.getItem('quizMuted') === 'true');
  const [showConfetti, setShowConfetti] = useState(false);
  const [displayScore, setDisplayScore] = useState(0);
  const timerRef = useRef();
  
  // Get user profile
  const { updateStatistics } = useUserProfile();
  
  // Fetch quiz data
  const { 
    questions, 
    loading, 
    error, 
    quizSettings 
  } = useQuizData(quizType, config);
  
  // Get current user
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };
    
    getCurrentUser();
  }, []);
  
  // Timer logic for timed quizzes
  useEffect(() => {
    if (!quizSettings?.timePerQuestion || showResults || loading) return;
    
    const timePerQuestion = quizSettings.timePerQuestion;
    setTimeLeft(timePerQuestion);
    
    timerRef.current && clearInterval(timerRef.current);
    timerRef.current = setInterval(() => {
      setTimeLeft((t) => {
        if (t <= 1) {
          clearInterval(timerRef.current);
          setTimedOut(true);
          setIsAnswered(true);
          return 0;
        }
        return t - 1;
      });
    }, 1000);
    
    return () => clearInterval(timerRef.current);
  }, [currentIdx, showResults, loading, quizSettings]);
  
  // Animated feedback
  useEffect(() => {
    if (isAnswered && !timedOut && questions[currentIdx]) {
      if (selectedOption === questions[currentIdx]?.correctOptionId) {
        setShowConfetti(true);
        setTimeout(() => setShowConfetti(false), 1200);
        
        // Play sound if not muted
        if (!isMuted) {
          const audio = new Audio('/sounds/correct.mp3');
          audio.play().catch(e => console.log('Audio play error:', e));
        }
      } else if (!isMuted) {
        // Play incorrect sound if not muted
        const audio = new Audio('/sounds/incorrect.mp3');
        audio.play().catch(e => console.log('Audio play error:', e));
      }
    }
  }, [isAnswered, timedOut, selectedOption, questions, currentIdx, isMuted]);
  
  // Animated score reveal
  useEffect(() => {
    if (showResults) {
      let n = 0;
      const correct = answers.filter(a => a.isCorrect).length;
      const interval = setInterval(() => {
        n++;
        setDisplayScore(Math.min(n, correct));
        if (n >= correct) clearInterval(interval);
      }, 20);
      return () => clearInterval(interval);
    }
  }, [showResults, answers]);
  
  // Handle answer selection
  const handleOptionSelect = useCallback(
    (optionId) => {
      if (isAnswered || !questions[currentIdx]) return;
      
      setSelectedOption(optionId);
      setIsAnswered(true);
      setTimedOut(false);
      
      // For quick quiz, automatically go to next question after a delay
      if (quizType === 'quick') {
        setTimeout(() => {
          handleNext();
        }, 1000);
      }
    },
    [isAnswered, questions, currentIdx, quizType]
  );
  
  // Handle next question
  const handleNext = useCallback(() => {
    if (!questions[currentIdx]) return;
    
    setAnswers((prev) => [
      ...prev,
      {
        questionId: questions[currentIdx].id,
        selectedOption,
        isCorrect: selectedOption === questions[currentIdx].correctOptionId,
        timedOut,
        timeSpent: quizSettings?.timePerQuestion ? quizSettings.timePerQuestion - timeLeft : null,
      },
    ]);
    
    setSelectedOption(null);
    setIsAnswered(false);
    setTimedOut(false);
    
    if (currentIdx < questions.length - 1) {
      setCurrentIdx((idx) => idx + 1);
    } else {
      setShowResults(true);
      
      // Update user statistics
      if (user) {
        const correctCount = answers.filter(a => a.isCorrect).length + 
          (selectedOption === questions[currentIdx].correctOptionId ? 1 : 0);
        
        updateStatistics({
          quizType,
          score: correctCount,
          maxScore: questions.length,
          timeTaken: answers.reduce((total, answer) => total + (answer.timeSpent || 0), 0) +
            (quizSettings?.timePerQuestion ? quizSettings.timePerQuestion - timeLeft : 0),
          categoryId: quizSettings?.categoryId,
          difficulty: quizSettings?.difficulty || 'mixed',
          questionCount: questions.length
        });
      }
    }
  }, [currentIdx, questions, selectedOption, timedOut, quizSettings, timeLeft, user, answers, updateStatistics, quizType]);
  
  // Mute toggle
  const toggleMute = () => {
    setIsMuted((m) => {
      localStorage.setItem('quizMuted', !m);
      return !m;
    });
  };
  
  // Results summary
  const correctCount = answers.filter((a) => a.isCorrect).length + 
    (showResults && selectedOption === questions[currentIdx]?.correctOptionId ? 1 : 0);
  
  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 via-green-900/20 to-emerald-900/20">
        <div className="w-full max-w-xl mx-auto p-8 text-center">
          <div className="animate-pulse flex flex-col items-center">
            <div className="h-12 w-12 bg-blue-500 rounded-full animate-spin mb-4"></div>
            <h2 className="text-2xl font-bold text-white mb-2">Loading Quiz</h2>
            <p className="text-gray-300">Preparing your {quizType} quiz...</p>
          </div>
        </div>
      </div>
    );
  }
  
  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 via-green-900/20 to-emerald-900/20">
        <div className="w-full max-w-xl mx-auto p-8 text-center">
          <div className="flex flex-col items-center">
            <div className="h-12 w-12 bg-red-500 rounded-full flex items-center justify-center mb-4">
              <span className="text-white text-xl">!</span>
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">Error Loading Quiz</h2>
            <p className="text-red-300 mb-4">{error}</p>
            <button 
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-white"
              onClick={() => navigate(-1)}
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }
  
  // No questions state
  if (!questions || questions.length === 0) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 via-green-900/20 to-emerald-900/20">
        <div className="w-full max-w-xl mx-auto p-8 text-center">
          <div className="flex flex-col items-center">
            <div className="h-12 w-12 bg-yellow-500 rounded-full flex items-center justify-center mb-4">
              <span className="text-white text-xl">?</span>
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">No Questions Available</h2>
            <p className="text-gray-300 mb-4">
              No questions found for this quiz. Please try a different category or configuration.
            </p>
            <button 
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-white"
              onClick={() => navigate(-1)}
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }
  
  // Results state
  if (showResults) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 via-green-900/20 to-emerald-900/20 py-8">
        <motion.div 
          className="w-full max-w-xl mx-auto bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-2xl shadow-xl p-8 flex flex-col items-center"
          initial={{ opacity: 0, y: 24 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h2 className="text-2xl font-bold text-white mb-2">Quiz Complete!</h2>
          
          <div className="relative w-40 h-40 mb-6">
            <svg className="w-full h-full" viewBox="0 0 100 100">
              <circle 
                cx="50" 
                cy="50" 
                r="45" 
                fill="none" 
                stroke="#374151" 
                strokeWidth="10"
              />
              <circle 
                cx="50" 
                cy="50" 
                r="45" 
                fill="none" 
                stroke="#10B981" 
                strokeWidth="10"
                strokeDasharray={`${(displayScore / questions.length) * 283} 283`}
                strokeDashoffset="0"
                transform="rotate(-90 50 50)"
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <AnimatePresence>
                  <motion.span 
                    key={displayScore}
                    className="text-4xl font-bold text-white"
                    initial={{ scale: 0.7 }}
                    animate={{ scale: 1 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    {displayScore}
                  </motion.span>
                </AnimatePresence>
                <span className="text-gray-300 text-xl"> / {questions.length}</span>
              </div>
            </div>
          </div>
          
          <div className="w-full mb-6">
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-300">Accuracy</span>
              <span className="text-white font-medium">
                {Math.round((correctCount / questions.length) * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2.5">
              <div 
                className="bg-green-500 h-2.5 rounded-full" 
                style={{ width: `${(correctCount / questions.length) * 100}%` }}
              ></div>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 w-full">
            <button 
              className="flex-1 px-6 py-3 rounded-xl bg-blue-600 text-white font-semibold hover:bg-blue-700 transition"
              onClick={() => window.location.reload()}
            >
              Try Again
            </button>
            <button 
              className="flex-1 px-6 py-3 rounded-xl bg-gray-700 text-white font-semibold hover:bg-gray-600 transition"
              onClick={() => navigate('/')}
            >
              Home
            </button>
          </div>
        </motion.div>
      </div>
    );
  }
  
  // Current question
  const currentQuestion = questions[currentIdx];
  
  // Main quiz UI
  return (
    <div className="min-h-screen flex flex-col items-center justify-start bg-gradient-to-br from-gray-900 via-green-900/20 to-emerald-900/20 py-8">
      <div className="w-full max-w-xl mx-auto px-4 relative">
        {/* Timer bar */}
        {quizSettings?.timePerQuestion && (
          <div className="sticky top-0 z-20 mb-4">
            <motion.div className="h-3 w-full bg-gray-700 rounded-full overflow-hidden">
              <motion.div
                className={`h-3 rounded-full ${
                  timeLeft < 10 
                    ? 'bg-red-500' 
                    : timeLeft < quizSettings.timePerQuestion / 3 
                      ? 'bg-yellow-400' 
                      : 'bg-blue-500'
                }`}
                initial={{ width: '100%' }}
                animate={{ width: `${(timeLeft / quizSettings.timePerQuestion) * 100}%` }}
                transition={{ duration: 0.5 }}
              />
            </motion.div>
            <div className="flex justify-between text-xs mt-1 text-gray-300">
              <span>Time Left: <b>{Math.floor(timeLeft / 60)}:{(timeLeft % 60).toString().padStart(2, '0')}</b></span>
            </div>
          </div>
        )}
        
        {/* Progress bar */}
        <QuizProgressBar 
          current={currentIdx + 1} 
          total={questions.length} 
          secondsLeft={quizSettings?.timePerQuestion ? timeLeft : null}
        />
        
        {/* Navigation */}
        <div className="flex justify-between items-center mb-4">
          <button 
            onClick={() => navigate(-1)} 
            className="p-2 rounded-lg hover:bg-gray-800 transition-colors"
            aria-label="Go back"
          >
            <ArrowLeft className="w-6 h-6 text-gray-300" />
          </button>
          
          <div className="flex items-center gap-2">
            <button 
              onClick={toggleMute} 
              className="p-2 rounded-lg hover:bg-gray-800 transition-colors"
              aria-label={isMuted ? "Unmute" : "Mute"}
            >
              {isMuted ? (
                <VolumeX className="w-6 h-6 text-gray-300" />
              ) : (
                <Volume2 className="w-6 h-6 text-gray-300" />
              )}
            </button>
            
            <button 
              onClick={() => navigate('/')} 
              className="p-2 rounded-lg hover:bg-gray-800 transition-colors"
              aria-label="Go home"
            >
              <Home className="w-6 h-6 text-gray-300" />
            </button>
          </div>
        </div>
        
        {/* Question and options */}
        <AnimatePresence mode="wait">
          {currentQuestion && (
            <motion.div
              key={currentQuestion.id}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.3 }}
              className="relative"
            >
              {/* Confetti animation */}
              {showConfetti && (
                <div className="absolute inset-0 pointer-events-none z-40 flex items-center justify-center">
                  <span role="img" aria-label="confetti" className="text-5xl animate-bounce">🎉</span>
                </div>
              )}
              
              {/* Question header */}
              <div className="mb-2 font-semibold text-gray-300">
                Question {currentIdx + 1} / {questions.length}
              </div>
              
              {/* Question text */}
              <div className="mb-6 text-lg font-medium text-white">
                {currentQuestion.text}
              </div>
              
              {/* Options */}
              <div className="space-y-3 mb-4">
                {currentQuestion.options && currentQuestion.options.map((option, idx) => (
                  <motion.button
                    key={idx}
                    className={`w-full flex items-center gap-3 text-left p-4 rounded-xl border transition-all font-medium text-base focus:outline-none focus:ring-2 focus:ring-blue-400 ${
                      selectedOption === idx
                        ? (idx === currentQuestion.correctOptionId
                          ? 'bg-green-900/50 border-green-500 scale-105'
                          : 'bg-red-900/50 border-red-500')
                        : isAnswered && idx === currentQuestion.correctOptionId
                          ? 'bg-green-900/50 border-green-500'
                          : 'border-gray-700 hover:bg-gray-800'
                    } ${isAnswered ? 'pointer-events-none' : ''}`}
                    disabled={isAnswered}
                    onClick={() => handleOptionSelect(idx)}
                    aria-pressed={selectedOption === idx}
                    tabIndex={0}
                  >
                    <span className="text-2xl">{ICONS[idx] || '🔘'}</span>
                    <span className="text-gray-100">{option}</span>
                  </motion.button>
                ))}
              </div>
              
              {/* Explanation and next button */}
              <AnimatePresence>
                {isAnswered && (
                  <motion.div
                    initial={{ opacity: 0, y: 12 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -12 }}
                    className="mb-2"
                  >
                    {/* Feedback */}
                    {selectedOption === currentQuestion.correctOptionId ? (
                      <div className="text-green-400 font-semibold mb-2">Correct!</div>
                    ) : (
                      <div className="text-red-400 font-semibold mb-2">
                        Incorrect. Correct answer: <b>{currentQuestion.options[currentQuestion.correctOptionId]}</b>
                      </div>
                    )}
                    
                    {/* Explanation */}
                    {currentQuestion.explanation && quizSettings?.showExplanations && (
                      <div className="mt-2 text-sm text-gray-300 bg-gray-800 bg-opacity-50 rounded-lg p-4 mb-4">
                        <div className="font-medium text-gray-200 mb-1">Explanation:</div>
                        {currentQuestion.explanation}
                      </div>
                    )}
                    
                    {/* Next button (not shown for quick quiz) */}
                    {quizType !== 'quick' && (
                      <button
                        className="mt-4 w-full bg-blue-600 text-white py-3 rounded-xl font-semibold text-lg shadow-lg hover:bg-blue-700 transition"
                        onClick={handleNext}
                      >
                        {currentIdx < questions.length - 1 ? 'Next Question' : 'Finish Quiz'}
                      </button>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Helper function to get default configuration based on quiz type
function getDefaultConfig(quizType) {
  switch (quizType) {
    case 'quick':
      return {
        questionCount: 10,
        timePerQuestion: 60,
        showExplanations: false,
        difficulty: 'mixed'
      };
    case 'timed':
      return {
        questionCount: 20,
        timePerQuestion: 90,
        showExplanations: true,
        difficulty: 'mixed'
      };
    case 'custom':
      return {
        questionCount: 10,
        timePerQuestion: 60,
        showExplanations: true,
        difficulty: 'mixed'
      };
    default:
      return {
        questionCount: 10,
        timePerQuestion: 60,
        showExplanations: true,
        difficulty: 'mixed'
      };
  }
}

// Helper function to get time per question based on quiz type and config
function getTimePerQuestion(quizType, config) {
  if (quizType === 'quick') {
    return 60; // 60 seconds per question for quick quiz
  } else if (quizType === 'timed') {
    return 90; // 90 seconds per question for timed test
  } else if (config?.timing === 'timed') {
    return config.timePerQuestion || 60; // Use config time or default to 60 seconds
  } else {
    return null; // No timer for self-paced quizzes
  }
}

export default QuizPage;