import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Share2, <PERSON><PERSON><PERSON>, ArrowLeft, Home, Award } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useUserProfile } from '../hooks/useUserProfile';
import LoadingState from '../components/ui/LoadingState';
import EmptyState from '../components/ui/EmptyState';

/**
 * Results Page Component
 * Displays quiz results and statistics
 */
const ResultsPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { updateStatistics } = useUserProfile();
  
  // Get results from location state
  const [results, setResults] = useState(location.state?.results || null);
  const [loading, setLoading] = useState(false);
  const [displayScore, setDisplayScore] = useState(0);
  const [showShareOptions, setShowShareOptions] = useState(false);
  const [achievements, setAchievements] = useState([]);
  
  // If no results in location state, redirect to home
  useEffect(() => {
    if (!results && !location.state?.results) {
      navigate('/');
    } else if (location.state?.results) {
      setResults(location.state.results);
    }
  }, [location.state, navigate, results]);
  
  // Animated score reveal
  useEffect(() => {
    if (results) {
      let n = 0;
      const interval = setInterval(() => {
        n++;
        setDisplayScore(Math.min(n, results.correctCount));
        if (n >= results.correctCount) clearInterval(interval);
      }, 20);
      return () => clearInterval(interval);
    }
  }, [results]);
  
  // Check for new achievements
  useEffect(() => {
    const checkAchievements = async () => {
      if (!results) return;
      
      try {
        setLoading(true);
        
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          setLoading(false);
          return;
        }
        
        // Get user's achievements before this quiz
        const { data: previousAchievements } = await supabase
          .from('user_achievements')
          .select('achievement_id')
          .eq('user_id', user.id);
        
        // Get user's achievements after this quiz
        const { data: currentAchievements } = await supabase
          .from('user_achievements')
          .select('*')
          .eq('user_id', user.id);
        
        // Find new achievements
        const prevIds = previousAchievements?.map(a => a.achievement_id) || [];
        const newAchievements = currentAchievements?.filter(
          a => !prevIds.includes(a.achievement_id)
        ) || [];
        
        // Map achievement IDs to full achievement data
        const mappedAchievements = newAchievements.map(achievement => {
          const details = getAchievementDetails(achievement.achievement_id);
          return {
            ...achievement,
            ...details
          };
        });
        
        setAchievements(mappedAchievements);
      } catch (error) {
        console.error('Error checking achievements:', error);
      } finally {
        setLoading(false);
      }
    };
    
    checkAchievements();
  }, [results]);
  
  // Get achievement details by ID
  const getAchievementDetails = (achievementId) => {
    const achievements = {
      'first_quiz_completed': {
        name: 'First Steps',
        description: 'Completed your first quiz',
        icon: '🎯'
      },
      'ten_quizzes_completed': {
        name: 'Dedicated Learner',
        description: 'Completed 10 quizzes',
        icon: '🔥'
      },
      'hundred_questions': {
        name: 'Century Club',
        description: 'Answered 100 questions',
        icon: '💯'
      },
      'perfect_score': {
        name: 'Perfect Score',
        description: 'Achieved 100% accuracy on a quiz',
        icon: '🏆'
      },
      'streak_3_days': {
        name: 'Consistency',
        description: 'Maintained a 3-day study streak',
        icon: '📆'
      },
      'streak_7_days': {
        name: 'Weekly Warrior',
        description: 'Maintained a 7-day study streak',
        icon: '🗓️'
      },
      'streak_30_days': {
        name: 'Monthly Master',
        description: 'Maintained a 30-day study streak',
        icon: '🏅'
      }
    };
    
    return achievements[achievementId] || {
      name: 'Unknown Achievement',
      description: 'Achievement details not found',
      icon: '❓'
    };
  };
  
  // Format time
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  // Share results
  const shareResults = async (platform) => {
    if (!results) return;
    
    const shareText = `I scored ${results.correctCount}/${results.totalQuestions} (${Math.round((results.correctCount / results.totalQuestions) * 100)}%) on a ${results.quizType} quiz!`;
    
    switch (platform) {
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}`, '_blank');
        break;
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}&quote=${encodeURIComponent(shareText)}`, '_blank');
        break;
      case 'copy':
        try {
          await navigator.clipboard.writeText(shareText);
          alert('Results copied to clipboard!');
        } catch (err) {
          console.error('Failed to copy text: ', err);
        }
        break;
      default:
        break;
    }
    
    setShowShareOptions(false);
  };
  
  // Loading state
  if (loading) {
    return <LoadingState message="Loading your results..." />;
  }
  
  // No results state
  if (!results) {
    return (
      <EmptyState 
        title="No Results Found" 
        message="Complete a quiz to see your results here." 
        icon="📊"
      />
    );
  }
  
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 via-green-900/20 to-emerald-900/20 py-8 px-4">
      <motion.div 
        className="w-full max-w-xl mx-auto bg-gray-800 bg-opacity-50 backdrop-blur-md rounded-2xl shadow-xl p-8 flex flex-col items-center"
        initial={{ opacity: 0, y: 24 }}
        animate={{ opacity: 1, y: 0 }}
      >
        {/* Header */}
        <div className="flex justify-between items-center w-full mb-6">
          <button 
            onClick={() => navigate(-1)} 
            className="p-2 rounded-lg hover:bg-gray-700 transition-colors"
            aria-label="Go back"
          >
            <ArrowLeft className="w-5 h-5 text-gray-300" />
          </button>
          
          <h2 className="text-2xl font-bold text-white">Quiz Results</h2>
          
          <button 
            onClick={() => navigate('/')} 
            className="p-2 rounded-lg hover:bg-gray-700 transition-colors"
            aria-label="Go home"
          >
            <Home className="w-5 h-5 text-gray-300" />
          </button>
        </div>
        
        {/* Score circle */}
        <div className="relative w-40 h-40 mb-6">
          <svg className="w-full h-full" viewBox="0 0 100 100">
            <circle 
              cx="50" 
              cy="50" 
              r="45" 
              fill="none" 
              stroke="#374151" 
              strokeWidth="10"
            />
            <circle 
              cx="50" 
              cy="50" 
              r="45" 
              fill="none" 
              stroke="#10B981" 
              strokeWidth="10"
              strokeDasharray={`${(displayScore / results.totalQuestions) * 283} 283`}
              strokeDashoffset="0"
              transform="rotate(-90 50 50)"
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <AnimatePresence>
                <motion.span 
                  key={displayScore}
                  className="text-4xl font-bold text-white"
                  initial={{ scale: 0.7 }}
                  animate={{ scale: 1 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  {displayScore}
                </motion.span>
              </AnimatePresence>
              <span className="text-gray-300 text-xl"> / {results.totalQuestions}</span>
            </div>
          </div>
        </div>
        
        {/* Quiz details */}
        <div className="w-full mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-300">Accuracy</span>
            <span className="text-white font-medium">
              {Math.round((results.correctCount / results.totalQuestions) * 100)}%
            </span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2.5 mb-4">
            <div 
              className="bg-green-500 h-2.5 rounded-full" 
              style={{ width: `${(results.correctCount / results.totalQuestions) * 100}%` }}
            ></div>
          </div>
          
          {/* Stats grid */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-gray-700 bg-opacity-50 rounded-lg p-3">
              <div className="text-sm text-gray-400 mb-1">Quiz Type</div>
              <div className="text-white font-medium">
                {results.quizType === 'quick' 
                  ? 'Quick Quiz' 
                  : results.quizType === 'timed' 
                    ? 'Timed Test' 
                    : 'Custom Quiz'}
              </div>
            </div>
            
            <div className="bg-gray-700 bg-opacity-50 rounded-lg p-3">
              <div className="text-sm text-gray-400 mb-1">Difficulty</div>
              <div className="text-white font-medium capitalize">
                {results.difficulty || 'Mixed'}
              </div>
            </div>
            
            <div className="bg-gray-700 bg-opacity-50 rounded-lg p-3">
              <div className="text-sm text-gray-400 mb-1">Time Taken</div>
              <div className="text-white font-medium">
                {formatTime(results.timeTaken)}
              </div>
            </div>
            
            <div className="bg-gray-700 bg-opacity-50 rounded-lg p-3">
              <div className="text-sm text-gray-400 mb-1">Category</div>
              <div className="text-white font-medium">
                {results.categoryName || 'General'}
              </div>
            </div>
          </div>
        </div>
        
        {/* New achievements */}
        {achievements.length > 0 && (
          <div className="w-full mb-6">
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
              <Award className="w-5 h-5 mr-2 text-yellow-400" />
              New Achievements
            </h3>
            
            <div className="grid grid-cols-2 gap-3">
              {achievements.map((achievement) => (
                <motion.div
                  key={achievement.achievement_id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="bg-gray-700 bg-opacity-50 rounded-lg p-4 text-center"
                >
                  <div className="text-3xl mb-2">{achievement.icon}</div>
                  <h4 className="font-medium text-white text-sm mb-1">{achievement.name}</h4>
                  <p className="text-xs text-gray-300">{achievement.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        )}
        
        {/* Action buttons */}
        <div className="flex flex-col sm:flex-row gap-4 w-full">
          <button 
            className="flex-1 px-6 py-3 rounded-xl bg-blue-600 text-white font-semibold hover:bg-blue-700 transition flex items-center justify-center"
            onClick={() => navigate(`/quiz/${results.quizType}`, { state: results.config })}
          >
            <BarChart className="w-5 h-5 mr-2" />
            Try Again
          </button>
          
          <div className="flex-1 relative">
            <button 
              className="w-full px-6 py-3 rounded-xl bg-gray-700 text-white font-semibold hover:bg-gray-600 transition flex items-center justify-center"
              onClick={() => setShowShareOptions(!showShareOptions)}
            >
              <Share2 className="w-5 h-5 mr-2" />
              Share Results
            </button>
            
            {/* Share options dropdown */}
            <AnimatePresence>
              {showShareOptions && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full left-0 right-0 mt-2 bg-gray-800 rounded-lg shadow-lg overflow-hidden z-10"
                >
                  <button
                    className="w-full px-4 py-2 text-left hover:bg-gray-700 text-white flex items-center"
                    onClick={() => shareResults('twitter')}
                  >
                    <span className="mr-2">🐦</span> Twitter
                  </button>
                  <button
                    className="w-full px-4 py-2 text-left hover:bg-gray-700 text-white flex items-center"
                    onClick={() => shareResults('facebook')}
                  >
                    <span className="mr-2">📘</span> Facebook
                  </button>
                  <button
                    className="w-full px-4 py-2 text-left hover:bg-gray-700 text-white flex items-center"
                    onClick={() => shareResults('copy')}
                  >
                    <span className="mr-2">📋</span> Copy to Clipboard
                  </button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ResultsPage;