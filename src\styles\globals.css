@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Hide scrollbars but keep functionality */
.scrollbar-hide {
  /* IE and Edge */
  -ms-overflow-style: none;
  /* Firefox */
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  /* Chrome, Safari and Opera */
  display: none;
}

/* Global scrollbar hiding - optional for entire app */
html {
  /* IE and Edge */
  -ms-overflow-style: none;
  /* Firefox */
  scrollbar-width: none;
}

html::-webkit-scrollbar {
  /* Chrome, Safari and Opera */
  display: none;
}

/* Custom scrollbar for elements that want visible scrollbars */
.scrollbar-custom::-webkit-scrollbar {
  width: 6px;
  display: block;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Mobile-Native Improvements */
@media (max-width: 768px) {
  /* Improve touch targets */
  button, .btn {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Native scrolling behavior */
  * {
    -webkit-overflow-scrolling: touch;
  }
  
  /* Remove tap highlights */
  * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* Allow text selection for inputs and content */
  input, textarea, [contenteditable] {
    -webkit-user-select: text;
    -khtml-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
  
  /* Native feel for active states */
  .active\:scale-95:active {
    transform: scale(0.95);
  }
  
  /* Safe area for notched devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* iOS-style momentum scrolling */
.momentum-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
}

/* Native-style card shadows */
.card-shadow {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

.card-shadow-lg {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Enhanced Native Mobile Styles */

/* Better viewport and font rendering */
html {
  -webkit-text-size-adjust: 100%;
  text-rendering: optimizeLegibility;
  overscroll-behavior: none;
}

body {
  overscroll-behavior: none;
  touch-action: manipulation;
}

/* Native-like component styles */
.native-button {
  @apply rounded-2xl font-semibold tracking-tight transition-all duration-200 shadow-sm active:scale-95;
  transform-origin: center;
}

.native-button-primary {
  @apply bg-gradient-to-r from-blue-500 to-blue-600 text-white;
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
}

.native-button-secondary {
  @apply bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700;
}

.native-card {
  @apply bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl rounded-3xl border border-gray-200/30 dark:border-gray-700/30;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.native-list-item {
  @apply bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl border border-gray-200/20 dark:border-gray-700/20 transition-all duration-200;
  transform-origin: center;
}

.native-list-item:active {
  transform: scale(0.98);
  background: rgba(0, 0, 0, 0.02);
}

/* Enhanced animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-4px) scale(1.02); }
}

@keyframes pulse-glow {
  0%, 100% { 
    opacity: 1; 
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  50% { 
    opacity: 0.8; 
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

@keyframes bounce-gentle {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0px);
  }
  40%, 43% {
    transform: translateY(-5px);
  }
  70% {
    transform: translateY(-2px);
  }
  90% {
    transform: translateY(-1px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s infinite;
}

/* Better dark mode scrollbars */
.dark ::-webkit-scrollbar-track {
  background: #1f2937;
}

.dark ::-webkit-scrollbar-thumb {
  background: #374151;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
}

/* Safe area support for various devices */
@supports (padding: max(0px)) {
  .safe-top {
    padding-top: max(12px, env(safe-area-inset-top));
  }
  
  .safe-bottom {
    padding-bottom: max(8px, env(safe-area-inset-bottom));
  }
  
  .safe-left {
    padding-left: max(16px, env(safe-area-inset-left));
  }
  
  .safe-right {
    padding-right: max(16px, env(safe-area-inset-right));
  }
}

/* Enhanced mobile interactions */
@media (max-width: 768px) {
  /* Better button touch targets */
  .mobile-touch-target {
    min-height: 48px;
    min-width: 48px;
  }
  
  /* Native-like active states */
  .mobile-active:active {
    transform: scale(0.95);
    transition: transform 0.1s ease-out;
  }
  
  /* Prevent text selection on UI elements */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* Allow text selection for content */
  .allow-select {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
  
  /* iOS-style momentum scrolling */
  .ios-scroll {
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;
  }
}

/* Utility classes for native feel */
.spring-transform {
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.native-shadow {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(0, 0, 0, 0.05);
}

.native-shadow-dark {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* View Transition API styles for smooth page transitions */
@view-transition {
  navigation: auto;
}

/* Fallback for browsers without View Transition API support */
@media (prefers-reduced-motion: no-preference) {
  html:not(.transition-page-transition) {
    view-transition-name: none;
  }
}

/* Page transition animations */
::view-transition-old(root) {
  animation: fade-out 0.3s ease-out;
}

::view-transition-new(root) {
  animation: fade-in 0.3s ease-in;
}

/* Quiz enter transition */
.transition-quiz-enter ::view-transition-old(root) {
  animation: slide-out-left 0.3s ease-out;
}

.transition-quiz-enter ::view-transition-new(root) {
  animation: slide-in-right 0.3s ease-in;
}

/* Home enter transition */
.transition-home-enter ::view-transition-old(root) {
  animation: slide-out-right 0.3s ease-out;
}

.transition-home-enter ::view-transition-new(root) {
  animation: slide-in-left 0.3s ease-in;
}

/* Profile enter transition */
.transition-profile-enter ::view-transition-old(root) {
  animation: slide-out-up 0.3s ease-out;
}

.transition-profile-enter ::view-transition-new(root) {
  animation: slide-in-down 0.3s ease-in;
}

/* Keyframe animations */
@keyframes fade-out {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slide-out-left {
  from { transform: translateX(0); opacity: 1; }
  to { transform: translateX(-100%); opacity: 0; }
}

@keyframes slide-in-right {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slide-out-right {
  from { transform: translateX(0); opacity: 1; }
  to { transform: translateX(100%); opacity: 0; }
}

@keyframes slide-in-left {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slide-out-up {
  from { transform: translateY(0); opacity: 1; }
  to { transform: translateY(-100%); opacity: 0; }
}

@keyframes slide-in-down {
  from { transform: translateY(-100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Performance optimizations for smooth transitions */
* {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Instant loading indicators */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.dark .loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

/* Cache status indicators */
.cache-indicator {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Smooth scroll for better UX */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
  
  ::view-transition-old(root),
  ::view-transition-new(root) {
    animation: none !important;
  }
} 