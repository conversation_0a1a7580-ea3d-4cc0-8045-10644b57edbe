import { config, debugLog } from '../config/environment';

/**
 * Error handling utilities for the USMLE Trivia App
 * These functions help standardize error handling across the application
 */

// Error types
export const ErrorTypes = {
  AUTHENTICATION: 'authentication',
  DATABASE: 'database',
  NETWORK: 'network',
  VALIDATION: 'validation',
  UNKNOWN: 'unknown'
};

/**
 * Format an error for display and logging
 * @param {Error} error - Error object
 * @param {string} context - Context where the error occurred
 * @returns {Object} Formatted error
 */
export const formatError = (error, context = '') => {
  // Determine error type
  let type = ErrorTypes.UNKNOWN;
  let message = error.message || 'An unknown error occurred';
  let code = error.code || 'unknown';
  
  // Check for Supabase authentication errors
  if (message.includes('auth') || code.includes('auth')) {
    type = ErrorTypes.AUTHENTICATION;
  } 
  // Check for database errors
  else if (message.includes('database') || message.includes('query') || code.includes('db')) {
    type = ErrorTypes.DATABASE;
  } 
  // Check for network errors
  else if (message.includes('network') || message.includes('fetch') || error.name === 'NetworkError') {
    type = ErrorTypes.NETWORK;
  }
  // Check for validation errors
  else if (message.includes('validation') || message.includes('invalid')) {
    type = ErrorTypes.VALIDATION;
  }
  
  // Format user-friendly message
  let userMessage = message;
  
  switch (type) {
    case ErrorTypes.AUTHENTICATION:
      userMessage = 'Authentication error. Please try signing in again.';
      break;
    case ErrorTypes.DATABASE:
      userMessage = 'Database error. Please try again later.';
      break;
    case ErrorTypes.NETWORK:
      userMessage = 'Network error. Please check your connection and try again.';
      break;
    case ErrorTypes.VALIDATION:
      userMessage = message; // Keep validation messages as they are usually user-friendly
      break;
    default:
      userMessage = 'An unexpected error occurred. Please try again.';
  }
  
  // Log error in development
  if (config.debug) {
    console.error(`Error in ${context}:`, error);
  }
  
  return {
    type,
    message: userMessage,
    originalMessage: message,
    code,
    context
  };
};

/**
 * Handle an error with standardized logging and formatting
 * @param {Error} error - Error object
 * @param {string} context - Context where the error occurred
 * @returns {Object} Formatted error
 */
export const handleError = (error, context = '') => {
  const formattedError = formatError(error, context);
  
  // Log error to monitoring service in production
  if (!config.debug) {
    // This would be replaced with actual error reporting service
    // Example: Sentry.captureException(error);
    debugLog('Would report to error monitoring service:', formattedError);
  }
  
  return formattedError;
};

/**
 * Create a safe async function that catches errors
 * @param {Function} fn - Async function to wrap
 * @param {string} context - Context for error handling
 * @returns {Function} Wrapped function that returns [result, error]
 */
export const createSafeAsyncFunction = (fn, context = '') => {
  return async (...args) => {
    try {
      const result = await fn(...args);
      return [result, null];
    } catch (error) {
      const formattedError = handleError(error, context);
      return [null, formattedError];
    }
  };
};