/**
 * Font optimization utilities for the USMLE Trivia App
 * These functions help optimize font loading and rendering
 */

/**
 * Load fonts asynchronously
 * @param {Array} fontFamilies - Array of font family names to load
 * @param {Object} options - Font loading options
 * @returns {Promise} Promise that resolves when fonts are loaded
 */
export const loadFontsAsync = (fontFamilies, options = {}) => {
  // Use the Font Loading API if available
  if ('fonts' in document) {
    const fontPromises = fontFamilies.map(family => {
      return document.fonts.load(`1em ${family}`, options.text || 'A');
    });
    
    return Promise.all(fontPromises);
  }
  
  // Fallback for browsers without Font Loading API
  return new Promise(resolve => {
    // Create a timeout to resolve after a reasonable time
    setTimeout(resolve, 2000);
  });
};

/**
 * Preload critical fonts
 * @param {Array} fontUrls - Array of font URLs to preload
 */
export const preloadCriticalFonts = (fontUrls) => {
  fontUrls.forEach(url => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = url;
    link.as = 'font';
    link.type = 'font/woff2';
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  });
};

/**
 * Apply font display swap to all style elements
 * This ensures text remains visible during font loading
 */
export const applyFontDisplaySwap = () => {
  // Get all style elements
  const styleElements = document.querySelectorAll('style');
  
  // Loop through each style element
  styleElements.forEach(style => {
    // Get the CSS text
    let cssText = style.textContent;
    
    // Add font-display: swap to all @font-face rules
    cssText = cssText.replace(
      /@font-face\s*{([^}]*)}/g,
      (match, fontFaceContent) => {
        if (!fontFaceContent.includes('font-display:')) {
          return `@font-face{${fontFaceContent}font-display: swap;}`;
        }
        return match;
      }
    );
    
    // Update the style element
    style.textContent = cssText;
  });
};

/**
 * Initialize font optimization
 * @param {Object} options - Font optimization options
 */
export const initFontOptimization = (options = {}) => {
  // Apply font display swap
  applyFontDisplaySwap();
  
  // Preload critical fonts if specified
  if (options.criticalFonts) {
    preloadCriticalFonts(options.criticalFonts);
  }
  
  // Load fonts asynchronously if specified
  if (options.fontFamilies) {
    loadFontsAsync(options.fontFamilies, options);
  }
};