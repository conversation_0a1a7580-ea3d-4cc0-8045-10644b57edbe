import { config, debugLog } from '../config/environment';

/**
 * Performance monitoring utilities for the USMLE Trivia App
 * These functions help track and optimize application performance
 */

// Store performance marks
const performanceMarks = {};

/**
 * Start timing a performance mark
 * @param {string} markName - Name of the performance mark
 */
export const startPerformanceMark = (markName) => {
  if (!config.debug) return;
  
  performanceMarks[markName] = {
    start: performance.now(),
    name: markName
  };
};

/**
 * End timing a performance mark and log the result
 * @param {string} markName - Name of the performance mark
 * @param {boolean} logResult - Whether to log the result
 * @returns {number|null} Duration in milliseconds or null if mark not found
 */
export const endPerformanceMark = (markName, logResult = true) => {
  if (!config.debug) return null;
  
  const mark = performanceMarks[markName];
  if (!mark) {
    debugLog(`Performance mark "${markName}" not found`);
    return null;
  }
  
  const duration = performance.now() - mark.start;
  
  if (logResult) {
    debugLog(`${markName}: ${duration.toFixed(2)}ms`);
  }
  
  delete performanceMarks[markName];
  return duration;
};

/**
 * Measure the execution time of a function
 * @param {Function} fn - Function to measure
 * @param {string} name - Name for the measurement
 * @returns {any} Result of the function
 */
export const measureExecutionTime = async (fn, name) => {
  if (!config.debug) return fn();
  
  const start = performance.now();
  try {
    const result = await fn();
    const duration = performance.now() - start;
    debugLog(`Execution time for ${name}: ${duration.toFixed(2)}ms`);
    return result;
  } catch (error) {
    const duration = performance.now() - start;
    debugLog(`Error in ${name} after ${duration.toFixed(2)}ms:`, error);
    throw error;
  }
};

/**
 * Create a debounced function
 * @param {Function} fn - Function to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (fn, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };
};

/**
 * Create a throttled function
 * @param {Function} fn - Function to throttle
 * @param {number} limit - Limit in milliseconds
 * @returns {Function} Throttled function
 */
export const throttle = (fn, limit) => {
  let lastCall = 0;
  return (...args) => {
    const now = Date.now();
    if (now - lastCall >= limit) {
      lastCall = now;
      fn(...args);
    }
  };
};

/**
 * Memoize a function (cache results)
 * @param {Function} fn - Function to memoize
 * @returns {Function} Memoized function
 */
export const memoize = (fn) => {
  const cache = new Map();
  return (...args) => {
    const key = JSON.stringify(args);
    if (cache.has(key)) {
      return cache.get(key);
    }
    const result = fn(...args);
    cache.set(key, result);
    return result;
  };
};