import { supabase } from '../lib/supabase';
import { debugLog } from '../config/environment';
import { startPerformanceMark, endPerformanceMark } from './performance';

/**
 * Query optimization utilities for the USMLE Trivia App
 * These functions help optimize database queries for better performance
 */

// Cache for query results
const queryCache = new Map();

// Cache configuration
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds
const MAX_CACHE_SIZE = 100; // Maximum number of cached queries

/**
 * Execute a query with caching
 * @param {Function} queryFn - Function that returns a Supabase query
 * @param {string} cacheKey - Cache key for the query
 * @param {Object} options - Query options
 * @returns {Promise<Object>} Query result
 */
export const executeQueryWithCache = async (queryFn, cacheKey, options = {}) => {
  const {
    ttl = CACHE_TTL,
    bypassCache = false,
    logPerformance = true
  } = options;
  
  // Generate a unique cache key if not provided
  const finalCacheKey = cacheKey || JSON.stringify(queryFn.toString());
  
  // Check cache if not bypassing
  if (!bypassCache && queryCache.has(finalCacheKey)) {
    const cachedResult = queryCache.get(finalCacheKey);
    
    // Check if cache is still valid
    if (Date.now() - cachedResult.timestamp < ttl) {
      debugLog(`Cache hit for query: ${finalCacheKey.substring(0, 50)}...`);
      return cachedResult.data;
    } else {
      // Cache expired, remove it
      queryCache.delete(finalCacheKey);
    }
  }
  
  // Execute query with performance tracking
  if (logPerformance) {
    startPerformanceMark(`query-${finalCacheKey.substring(0, 20)}`);
  }
  
  try {
    // Execute the query
    const query = queryFn();
    const result = await query;
    
    if (logPerformance) {
      endPerformanceMark(`query-${finalCacheKey.substring(0, 20)}`);
    }
    
    // Cache the result if no error
    if (!result.error) {
      // Manage cache size
      if (queryCache.size >= MAX_CACHE_SIZE) {
        // Remove oldest entry
        const oldestKey = queryCache.keys().next().value;
        queryCache.delete(oldestKey);
      }
      
      // Add to cache
      queryCache.set(finalCacheKey, {
        data: result,
        timestamp: Date.now()
      });
    }
    
    return result;
  } catch (error) {
    if (logPerformance) {
      endPerformanceMark(`query-${finalCacheKey.substring(0, 20)}`);
    }
    throw error;
  }
};

/**
 * Clear the query cache
 * @param {string} cacheKey - Optional specific cache key to clear
 */
export const clearQueryCache = (cacheKey = null) => {
  if (cacheKey) {
    queryCache.delete(cacheKey);
    debugLog(`Cleared cache for key: ${cacheKey}`);
  } else {
    queryCache.clear();
    debugLog('Cleared entire query cache');
  }
};

/**
 * Optimize a query by selecting only needed columns
 * @param {Object} query - Supabase query
 * @param {Array} columns - Columns to select
 * @returns {Object} Optimized query
 */
export const optimizeQueryColumns = (query, columns) => {
  if (!columns || columns.length === 0) {
    return query;
  }
  
  return query.select(columns.join(','));
};

/**
 * Paginate a query result
 * @param {Object} query - Supabase query
 * @param {number} page - Page number (1-based)
 * @param {number} pageSize - Page size
 * @returns {Object} Paginated query
 */
export const paginateQuery = (query, page = 1, pageSize = 20) => {
  const start = (page - 1) * pageSize;
  const end = start + pageSize - 1;
  
  return query.range(start, end);
};

/**
 * Execute a query with pagination and return total count
 * @param {Function} queryFn - Function that returns a Supabase query
 * @param {number} page - Page number (1-based)
 * @param {number} pageSize - Page size
 * @returns {Promise<Object>} Query result with pagination info
 */
export const executeQueryWithPagination = async (queryFn, page = 1, pageSize = 20) => {
  // Create count query
  const countQuery = queryFn().select('id', { count: 'exact' });
  
  // Create data query with pagination
  const dataQuery = paginateQuery(queryFn(), page, pageSize);
  
  // Execute both queries
  const [countResult, dataResult] = await Promise.all([
    countQuery,
    dataQuery
  ]);
  
  // Check for errors
  if (countResult.error) {
    throw countResult.error;
  }
  
  if (dataResult.error) {
    throw dataResult.error;
  }
  
  // Calculate pagination info
  const totalCount = countResult.count || 0;
  const totalPages = Math.ceil(totalCount / pageSize);
  
  return {
    data: dataResult.data,
    pagination: {
      page,
      pageSize,
      totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1
    }
  };
};