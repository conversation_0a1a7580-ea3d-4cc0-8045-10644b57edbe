-- USMLE Trivia App - Production Database Schema
-- This script sets up the necessary tables, views, and security policies for the production app

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- PROFILES TABLE ENHANCEMENTS
-- =============================================

-- Enhance profiles table with additional fields for statistics
ALTER TABLE public.profiles 
  ADD COLUMN IF NOT EXISTS total_questions INTEGER DEFAULT 0,
  ADD COLUMN IF NOT EXISTS correct_answers INTEGER DEFAULT 0,
  ADD COLUMN IF NOT EXISTS quiz_completed INTEGER DEFAULT 0,
  ADD COLUMN IF NOT EXISTS streak_days INTEGER DEFAULT 0,
  ADD COLUMN IF NOT EXISTS last_active TIMESTAMP WITH TIME ZONE,
  ADD COLUMN IF NOT EXISTS achievements JSONB DEFAULT '[]'::jsonb,
  ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{}'::jsonb,
  ADD COLUMN IF NOT EXISTS country TEXT;

-- =============================================
-- USER STATISTICS TABLE
-- =============================================

-- Create user_statistics table for detailed stats
CREATE TABLE IF NOT EXISTS public.user_statistics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  category_id UUID REFERENCES public.tags(id),
  questions_attempted INTEGER DEFAULT 0,
  questions_correct INTEGER DEFAULT 0,
  time_spent INTEGER DEFAULT 0, -- in seconds
  last_attempted TIMESTAMP WITH TIME ZONE,
  
  -- Unique constraint to ensure one record per user per category
  UNIQUE(user_id, category_id)
);

-- Create RLS policies for user_statistics
ALTER TABLE public.user_statistics ENABLE ROW LEVEL SECURITY;

-- Users can only read their own statistics
CREATE POLICY "Users can read their own statistics" 
  ON public.user_statistics FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can only insert their own statistics
CREATE POLICY "Users can insert their own statistics" 
  ON public.user_statistics FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Users can only update their own statistics
CREATE POLICY "Users can update their own statistics" 
  ON public.user_statistics FOR UPDATE 
  USING (auth.uid() = user_id);

-- =============================================
-- USER ACHIEVEMENTS TABLE
-- =============================================

-- Create user_achievements table
CREATE TABLE IF NOT EXISTS public.user_achievements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id TEXT NOT NULL,
  earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Unique constraint to ensure one achievement per user
  UNIQUE(user_id, achievement_id)
);

-- Create RLS policies for user_achievements
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;

-- Users can read their own achievements
CREATE POLICY "Users can read their own achievements" 
  ON public.user_achievements FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can insert their own achievements
CREATE POLICY "Users can insert their own achievements" 
  ON public.user_achievements FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- =============================================
-- USER SEEN QUESTIONS TABLE
-- =============================================

-- Create user_seen_questions table to track which questions a user has seen
CREATE TABLE IF NOT EXISTS public.user_seen_questions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  question_id UUID NOT NULL REFERENCES public.questions(id) ON DELETE CASCADE,
  seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Unique constraint to ensure one record per user per question
  UNIQUE(user_id, question_id)
);

-- Create RLS policies for user_seen_questions
ALTER TABLE public.user_seen_questions ENABLE ROW LEVEL SECURITY;

-- Users can only read their own seen questions
CREATE POLICY "Users can read their own seen questions" 
  ON public.user_seen_questions FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can only insert their own seen questions
CREATE POLICY "Users can insert their own seen questions" 
  ON public.user_seen_questions FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- =============================================
-- LEADERBOARD TABLE
-- =============================================

-- Create leaderboard table
CREATE TABLE IF NOT EXISTS public.leaderboard (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  quiz_type TEXT NOT NULL CHECK (quiz_type IN ('quick', 'timed', 'custom')),
  score INTEGER NOT NULL,
  max_score INTEGER NOT NULL,
  accuracy DECIMAL(5,2) NOT NULL,
  time_taken INTEGER NOT NULL, -- in seconds
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Additional fields for filtering/sorting
  category_id UUID REFERENCES public.tags(id),
  difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard', 'mixed')),
  
  -- For custom quizzes
  question_count INTEGER,
  
  CONSTRAINT valid_score CHECK (score <= max_score)
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS leaderboard_user_id_idx ON public.leaderboard(user_id);
CREATE INDEX IF NOT EXISTS leaderboard_quiz_type_idx ON public.leaderboard(quiz_type);
CREATE INDEX IF NOT EXISTS leaderboard_score_idx ON public.leaderboard(score);
CREATE INDEX IF NOT EXISTS leaderboard_created_at_idx ON public.leaderboard(created_at);

-- Create RLS policies for leaderboard
ALTER TABLE public.leaderboard ENABLE ROW LEVEL SECURITY;

-- Anyone can read leaderboard entries
CREATE POLICY "Anyone can read leaderboard entries" 
  ON public.leaderboard FOR SELECT USING (true);

-- Users can only insert their own entries
CREATE POLICY "Users can insert their own entries" 
  ON public.leaderboard FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Users can only update their own entries
CREATE POLICY "Users can update their own entries" 
  ON public.leaderboard FOR UPDATE 
  USING (auth.uid() = user_id);

-- =============================================
-- USER ACTIVITY LOG TABLE
-- =============================================

-- Create user_activity_log table for real-time activity tracking
CREATE TABLE IF NOT EXISTS public.user_activity_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type TEXT NOT NULL,
  activity_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS user_activity_log_user_id_idx ON public.user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS user_activity_log_created_at_idx ON public.user_activity_log(created_at);

-- Create RLS policies for user_activity_log
ALTER TABLE public.user_activity_log ENABLE ROW LEVEL SECURITY;

-- Users can only read their own activity
CREATE POLICY "Users can read their own activity" 
  ON public.user_activity_log FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can only insert their own activity
CREATE POLICY "Users can insert their own activity" 
  ON public.user_activity_log FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- =============================================
-- VIEWS
-- =============================================

-- Create view for leaderboard with user details
CREATE OR REPLACE VIEW public.leaderboard_with_users AS
SELECT 
  l.id,
  l.user_id,
  l.quiz_type,
  l.score,
  l.max_score,
  l.accuracy,
  l.time_taken,
  l.created_at,
  l.category_id,
  l.difficulty,
  l.question_count,
  p.username,
  p.avatar_url,
  p.country
FROM 
  public.leaderboard l
JOIN 
  public.profiles p ON l.user_id = p.id;

-- Create view for tag relationships
CREATE OR REPLACE VIEW public.tag_relationships AS
SELECT 
  t1.id AS tag_id,
  t1.name AS tag_name,
  t1.type AS tag_type,
  t2.id AS related_tag_id,
  t2.name AS related_tag_name,
  t2.type AS related_tag_type,
  COUNT(DISTINCT q.id) AS shared_questions
FROM 
  public.tags t1
JOIN 
  public.question_tags qt1 ON t1.id = qt1.tag_id
JOIN 
  public.questions q ON qt1.question_id = q.id
JOIN 
  public.question_tags qt2 ON q.id = qt2.question_id
JOIN 
  public.tags t2 ON qt2.tag_id = t2.id
WHERE 
  t1.id != t2.id
GROUP BY 
  t1.id, t1.name, t1.type, t2.id, t2.name, t2.type
HAVING 
  COUNT(DISTINCT q.id) > 0;

-- Create view for tag hierarchy
CREATE OR REPLACE VIEW public.tag_hierarchy AS
SELECT 
  subject.id AS subject_id,
  subject.name AS subject_name,
  system.id AS system_id,
  system.name AS system_name,
  topic.id AS topic_id,
  topic.name AS topic_name,
  COUNT(DISTINCT q.id) AS question_count
FROM 
  public.tags subject
LEFT JOIN 
  public.tag_relationships sr ON subject.id = sr.tag_id AND sr.related_tag_type = 'system'
LEFT JOIN 
  public.tags system ON sr.related_tag_id = system.id
LEFT JOIN 
  public.tag_relationships st ON system.id = st.tag_id AND st.related_tag_type = 'topic'
LEFT JOIN 
  public.tags topic ON st.related_tag_id = topic.id
LEFT JOIN 
  public.question_tags qt ON 
    (subject.id = qt.tag_id) OR 
    (system.id = qt.tag_id) OR 
    (topic.id = qt.tag_id)
LEFT JOIN 
  public.questions q ON qt.question_id = q.id
WHERE 
  subject.type = 'subject'
GROUP BY 
  subject.id, subject.name, system.id, system.name, topic.id, topic.name
ORDER BY 
  subject.name, system.name, topic.name;

-- =============================================
-- INDEXES FOR EXISTING TABLES
-- =============================================

-- Add indexes to questions table
CREATE INDEX IF NOT EXISTS questions_difficulty_idx ON public.questions(difficulty);

-- Add indexes to question_tags table
CREATE INDEX IF NOT EXISTS question_tags_tag_id_idx ON public.question_tags(tag_id);
CREATE INDEX IF NOT EXISTS question_tags_question_id_idx ON public.question_tags(question_id);

-- Add indexes to tags table
CREATE INDEX IF NOT EXISTS tags_type_idx ON public.tags(type);
CREATE INDEX IF NOT EXISTS tags_name_idx ON public.tags(name);

-- =============================================
-- FUNCTIONS
-- =============================================

-- Function to update user streak
CREATE OR REPLACE FUNCTION public.update_user_streak()
RETURNS TRIGGER AS $$
DECLARE
  last_active_date DATE;
  current_date DATE := CURRENT_DATE;
BEGIN
  -- Get the last active date for the user
  SELECT DATE(last_active) INTO last_active_date
  FROM public.profiles
  WHERE id = NEW.user_id;
  
  -- If last_active is NULL or more than 1 day ago, reset streak
  IF last_active_date IS NULL OR current_date - last_active_date > 1 THEN
    UPDATE public.profiles
    SET streak_days = 1, last_active = NOW()
    WHERE id = NEW.user_id;
  -- If last active was yesterday, increment streak
  ELSIF current_date - last_active_date = 1 THEN
    UPDATE public.profiles
    SET streak_days = streak_days + 1, last_active = NOW()
    WHERE id = NEW.user_id;
  -- If last active was today, just update last_active
  ELSIF current_date = last_active_date THEN
    UPDATE public.profiles
    SET last_active = NOW()
    WHERE id = NEW.user_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update streak when user completes a quiz
CREATE TRIGGER update_streak_on_quiz_completion
AFTER INSERT ON public.leaderboard
FOR EACH ROW
EXECUTE FUNCTION public.update_user_streak();

-- Function to check for achievements
CREATE OR REPLACE FUNCTION public.check_achievements()
RETURNS TRIGGER AS $$
DECLARE
  total_questions INTEGER;
  correct_answers INTEGER;
  quiz_completed INTEGER;
  accuracy DECIMAL;
BEGIN
  -- Get user statistics
  SELECT 
    p.total_questions, 
    p.correct_answers, 
    p.quiz_completed
  INTO 
    total_questions, 
    correct_answers, 
    quiz_completed
  FROM 
    public.profiles p
  WHERE 
    p.id = NEW.user_id;
  
  -- Calculate accuracy
  IF total_questions > 0 THEN
    accuracy := (correct_answers::DECIMAL / total_questions) * 100;
  ELSE
    accuracy := 0;
  END IF;
  
  -- First quiz completed
  IF quiz_completed = 1 THEN
    INSERT INTO public.user_achievements (user_id, achievement_id)
    VALUES (NEW.user_id, 'first_quiz_completed')
    ON CONFLICT (user_id, achievement_id) DO NOTHING;
  END IF;
  
  -- 10 quizzes completed
  IF quiz_completed = 10 THEN
    INSERT INTO public.user_achievements (user_id, achievement_id)
    VALUES (NEW.user_id, 'ten_quizzes_completed')
    ON CONFLICT (user_id, achievement_id) DO NOTHING;
  END IF;
  
  -- 100 questions answered
  IF total_questions >= 100 AND total_questions - NEW.question_count < 100 THEN
    INSERT INTO public.user_achievements (user_id, achievement_id)
    VALUES (NEW.user_id, 'hundred_questions')
    ON CONFLICT (user_id, achievement_id) DO NOTHING;
  END IF;
  
  -- Perfect score (100% accuracy)
  IF NEW.accuracy = 100 AND NEW.question_count >= 5 THEN
    INSERT INTO public.user_achievements (user_id, achievement_id)
    VALUES (NEW.user_id, 'perfect_score')
    ON CONFLICT (user_id, achievement_id) DO NOTHING;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to check achievements when user completes a quiz
CREATE TRIGGER check_achievements_on_quiz_completion
AFTER INSERT ON public.leaderboard
FOR EACH ROW
EXECUTE FUNCTION public.check_achievements();

-- =============================================
-- UPDATE PROFILE STATISTICS FUNCTION
-- =============================================

-- Function to update profile statistics when a quiz is completed
CREATE OR REPLACE FUNCTION public.update_profile_statistics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update profile statistics
  UPDATE public.profiles
  SET 
    total_questions = total_questions + NEW.question_count,
    correct_answers = correct_answers + NEW.score,
    quiz_completed = quiz_completed + 1
  WHERE id = NEW.user_id;
  
  -- Update category-specific statistics if category is provided
  IF NEW.category_id IS NOT NULL THEN
    INSERT INTO public.user_statistics (
      user_id, 
      category_id, 
      questions_attempted, 
      questions_correct, 
      time_spent, 
      last_attempted
    )
    VALUES (
      NEW.user_id,
      NEW.category_id,
      NEW.question_count,
      NEW.score,
      NEW.time_taken,
      NOW()
    )
    ON CONFLICT (user_id, category_id) DO UPDATE
    SET 
      questions_attempted = user_statistics.questions_attempted + NEW.question_count,
      questions_correct = user_statistics.questions_correct + NEW.score,
      time_spent = user_statistics.time_spent + NEW.time_taken,
      last_attempted = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update profile statistics when a quiz is completed
CREATE TRIGGER update_statistics_on_quiz_completion
AFTER INSERT ON public.leaderboard
FOR EACH ROW
EXECUTE FUNCTION public.update_profile_statistics();