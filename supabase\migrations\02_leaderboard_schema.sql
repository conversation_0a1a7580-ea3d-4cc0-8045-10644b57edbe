-- USMLE Trivia App - Leaderboard Schema
-- This script sets up the leaderboard table and related views

-- =============================================
-- LEADERBOARD TABLE
-- =============================================

-- Create leaderboard table if not exists
CREATE TABLE IF NOT EXISTS public.leaderboard (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  quiz_type TEXT NOT NULL CHECK (quiz_type IN ('quick', 'timed', 'custom')),
  score INTEGER NOT NULL,
  max_score INTEGER NOT NULL,
  accuracy DECIMAL(5,2) NOT NULL,
  time_taken INTEGER NOT NULL, -- in seconds
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Additional fields for filtering/sorting
  category_id UUID REFERENCES public.tags(id),
  difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard', 'mixed')),
  
  -- For custom quizzes
  question_count INTEGER,
  
  CONSTRAINT valid_score CHECK (score <= max_score)
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS leaderboard_user_id_idx ON public.leaderboard(user_id);
CREATE INDEX IF NOT EXISTS leaderboard_quiz_type_idx ON public.leaderboard(quiz_type);
CREATE INDEX IF NOT EXISTS leaderboard_score_idx ON public.leaderboard(score);
CREATE INDEX IF NOT EXISTS leaderboard_created_at_idx ON public.leaderboard(created_at);

-- Create RLS policies for leaderboard
ALTER TABLE public.leaderboard ENABLE ROW LEVEL SECURITY;

-- Anyone can read leaderboard entries
CREATE POLICY "Anyone can read leaderboard entries" 
  ON public.leaderboard FOR SELECT USING (true);

-- Users can only insert their own entries
CREATE POLICY "Users can insert their own entries" 
  ON public.leaderboard FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Users can only update their own entries
CREATE POLICY "Users can update their own entries" 
  ON public.leaderboard FOR UPDATE 
  USING (auth.uid() = user_id);

-- =============================================
-- LEADERBOARD VIEWS
-- =============================================

-- Create view for leaderboard with user details
CREATE OR REPLACE VIEW public.leaderboard_with_users AS
SELECT 
  l.id,
  l.user_id,
  l.quiz_type,
  l.score,
  l.max_score,
  l.accuracy,
  l.time_taken,
  l.created_at,
  l.category_id,
  l.difficulty,
  l.question_count,
  p.username,
  p.avatar_url,
  p.country
FROM 
  public.leaderboard l
JOIN 
  public.profiles p ON l.user_id = p.id;

-- Create view for user rankings
CREATE OR REPLACE VIEW public.user_rankings AS
WITH ranked_scores AS (
  SELECT
    user_id,
    quiz_type,
    difficulty,
    category_id,
    MAX(score) as best_score,
    MAX(accuracy) as best_accuracy,
    COUNT(*) as attempts,
    ROW_NUMBER() OVER (
      PARTITION BY quiz_type, difficulty, category_id
      ORDER BY MAX(score) DESC, MAX(accuracy) DESC
    ) as rank
  FROM
    public.leaderboard
  GROUP BY
    user_id, quiz_type, difficulty, category_id
)
SELECT
  r.user_id,
  p.username,
  p.avatar_url,
  p.country,
  r.quiz_type,
  r.difficulty,
  r.category_id,
  t.name as category_name,
  r.best_score,
  r.best_accuracy,
  r.attempts,
  r.rank
FROM
  ranked_scores r
JOIN
  public.profiles p ON r.user_id = p.id
LEFT JOIN
  public.tags t ON r.category_id = t.id;

-- Create view for daily leaderboard
CREATE OR REPLACE VIEW public.daily_leaderboard AS
SELECT
  l.id,
  l.user_id,
  p.username,
  p.avatar_url,
  p.country,
  l.quiz_type,
  l.score,
  l.max_score,
  l.accuracy,
  l.time_taken,
  l.created_at,
  l.category_id,
  t.name as category_name,
  l.difficulty,
  l.question_count,
  ROW_NUMBER() OVER (
    ORDER BY l.score DESC, l.accuracy DESC
  ) as rank
FROM
  public.leaderboard l
JOIN
  public.profiles p ON l.user_id = p.id
LEFT JOIN
  public.tags t ON l.category_id = t.id
WHERE
  l.created_at >= CURRENT_DATE;

-- Create view for weekly leaderboard
CREATE OR REPLACE VIEW public.weekly_leaderboard AS
SELECT
  l.id,
  l.user_id,
  p.username,
  p.avatar_url,
  p.country,
  l.quiz_type,
  l.score,
  l.max_score,
  l.accuracy,
  l.time_taken,
  l.created_at,
  l.category_id,
  t.name as category_name,
  l.difficulty,
  l.question_count,
  ROW_NUMBER() OVER (
    ORDER BY l.score DESC, l.accuracy DESC
  ) as rank
FROM
  public.leaderboard l
JOIN
  public.profiles p ON l.user_id = p.id
LEFT JOIN
  public.tags t ON l.category_id = t.id
WHERE
  l.created_at >= CURRENT_DATE - INTERVAL '7 days';

-- Create view for monthly leaderboard
CREATE OR REPLACE VIEW public.monthly_leaderboard AS
SELECT
  l.id,
  l.user_id,
  p.username,
  p.avatar_url,
  p.country,
  l.quiz_type,
  l.score,
  l.max_score,
  l.accuracy,
  l.time_taken,
  l.created_at,
  l.category_id,
  t.name as category_name,
  l.difficulty,
  l.question_count,
  ROW_NUMBER() OVER (
    ORDER BY l.score DESC, l.accuracy DESC
  ) as rank
FROM
  public.leaderboard l
JOIN
  public.profiles p ON l.user_id = p.id
LEFT JOIN
  public.tags t ON l.category_id = t.id
WHERE
  l.created_at >= CURRENT_DATE - INTERVAL '30 days';

-- =============================================
-- LEADERBOARD FUNCTIONS
-- =============================================

-- Function to get user rank
CREATE OR REPLACE FUNCTION public.get_user_rank(
  p_user_id UUID,
  p_quiz_type TEXT DEFAULT NULL,
  p_difficulty TEXT DEFAULT NULL,
  p_category_id UUID DEFAULT NULL,
  p_time_frame TEXT DEFAULT 'all'
)
RETURNS INTEGER AS $$
DECLARE
  v_rank INTEGER;
BEGIN
  -- Get user rank based on filters
  IF p_time_frame = 'day' THEN
    SELECT rank INTO v_rank
    FROM public.daily_leaderboard
    WHERE user_id = p_user_id
    AND (p_quiz_type IS NULL OR quiz_type = p_quiz_type)
    AND (p_difficulty IS NULL OR difficulty = p_difficulty)
    AND (p_category_id IS NULL OR category_id = p_category_id)
    LIMIT 1;
  ELSIF p_time_frame = 'week' THEN
    SELECT rank INTO v_rank
    FROM public.weekly_leaderboard
    WHERE user_id = p_user_id
    AND (p_quiz_type IS NULL OR quiz_type = p_quiz_type)
    AND (p_difficulty IS NULL OR difficulty = p_difficulty)
    AND (p_category_id IS NULL OR category_id = p_category_id)
    LIMIT 1;
  ELSIF p_time_frame = 'month' THEN
    SELECT rank INTO v_rank
    FROM public.monthly_leaderboard
    WHERE user_id = p_user_id
    AND (p_quiz_type IS NULL OR quiz_type = p_quiz_type)
    AND (p_difficulty IS NULL OR difficulty = p_difficulty)
    AND (p_category_id IS NULL OR category_id = p_category_id)
    LIMIT 1;
  ELSE
    SELECT rank INTO v_rank
    FROM public.user_rankings
    WHERE user_id = p_user_id
    AND (p_quiz_type IS NULL OR quiz_type = p_quiz_type)
    AND (p_difficulty IS NULL OR difficulty = p_difficulty)
    AND (p_category_id IS NULL OR category_id = p_category_id)
    LIMIT 1;
  END IF;
  
  RETURN v_rank;
END;
$$ LANGUAGE plpgsql;