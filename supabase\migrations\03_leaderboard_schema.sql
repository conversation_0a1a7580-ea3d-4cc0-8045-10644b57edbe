-- Migration for leaderboard and related tables
-- This migration adds tables for leaderboard, user statistics, and achievements

-- Create leaderboard table if not exists
CREATE TABLE IF NOT EXISTS public.leaderboard (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  quiz_type TEXT NOT NULL CHECK (quiz_type IN ('quick', 'timed', 'custom')),
  score INTEGER NOT NULL,
  max_score INTEGER NOT NULL,
  accuracy DECIMAL(5,2) NOT NULL,
  time_taken INTEGER NOT NULL, -- in seconds
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Additional fields for filtering/sorting
  category_id UUID REFERENCES public.tags(id),
  difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard', 'mixed')),
  question_count INTEGER,
  questions UUID[] DEFAULT '{}',
  
  CONSTRAINT valid_score CHECK (score <= max_score)
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS leaderboard_user_id_idx ON public.leaderboard(user_id);
CREATE INDEX IF NOT EXISTS leaderboard_quiz_type_idx ON public.leaderboard(quiz_type);
CREATE INDEX IF NOT EXISTS leaderboard_score_idx ON public.leaderboard(score);
CREATE INDEX IF NOT EXISTS leaderboard_created_at_idx ON public.leaderboard(created_at);
CREATE INDEX IF NOT EXISTS leaderboard_category_id_idx ON public.leaderboard(category_id);

-- Create user_statistics table for detailed stats
CREATE TABLE IF NOT EXISTS public.user_statistics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  category_id UUID REFERENCES public.tags(id),
  questions_attempted INTEGER DEFAULT 0,
  questions_correct INTEGER DEFAULT 0,
  time_spent INTEGER DEFAULT 0, -- in seconds
  last_attempted TIMESTAMP WITH TIME ZONE,
  
  -- Unique constraint to ensure one record per user per category
  UNIQUE(user_id, category_id)
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS user_statistics_user_id_idx ON public.user_statistics(user_id);
CREATE INDEX IF NOT EXISTS user_statistics_category_id_idx ON public.user_statistics(category_id);

-- Create user_achievements table
CREATE TABLE IF NOT EXISTS public.user_achievements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id TEXT NOT NULL,
  earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Unique constraint to ensure one achievement per user
  UNIQUE(user_id, achievement_id)
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS user_achievements_user_id_idx ON public.user_achievements(user_id);

-- Create user_activity_log table for tracking user activity
CREATE TABLE IF NOT EXISTS public.user_activity_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type TEXT NOT NULL,
  activity_data JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS user_activity_log_user_id_idx ON public.user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS user_activity_log_activity_type_idx ON public.user_activity_log(activity_type);
CREATE INDEX IF NOT EXISTS user_activity_log_created_at_idx ON public.user_activity_log(created_at);

-- Create user_preferences table
CREATE TABLE IF NOT EXISTS public.user_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  quiz_preferences JSONB DEFAULT '{}'::jsonb,
  ui_preferences JSONB DEFAULT '{}'::jsonb,
  study_goals JSONB DEFAULT '[]'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS user_preferences_user_id_idx ON public.user_preferences(user_id);

-- Create view for leaderboard with user details
CREATE OR REPLACE VIEW public.leaderboard_with_users AS
SELECT 
  l.id,
  l.user_id,
  l.quiz_type,
  l.score,
  l.max_score,
  l.accuracy,
  l.time_taken,
  l.created_at,
  l.category_id,
  l.difficulty,
  l.question_count,
  p.username,
  p.avatar_url,
  p.country,
  t.name as category_name
FROM 
  public.leaderboard l
JOIN 
  public.profiles p ON l.user_id = p.id
LEFT JOIN
  public.tags t ON l.category_id = t.id;

-- Create daily leaderboard view
CREATE OR REPLACE VIEW public.daily_leaderboard AS
SELECT * FROM public.leaderboard_with_users
WHERE created_at >= NOW() - INTERVAL '1 day';

-- Create weekly leaderboard view
CREATE OR REPLACE VIEW public.weekly_leaderboard AS
SELECT * FROM public.leaderboard_with_users
WHERE created_at >= NOW() - INTERVAL '7 days';

-- Create monthly leaderboard view
CREATE OR REPLACE VIEW public.monthly_leaderboard AS
SELECT * FROM public.leaderboard_with_users
WHERE created_at >= NOW() - INTERVAL '30 days';

-- Create performance trends view
CREATE OR REPLACE VIEW public.user_performance_trends AS
WITH user_quiz_results AS (
  SELECT
    l.user_id,
    l.quiz_type,
    l.category_id,
    l.difficulty,
    l.accuracy,
    l.created_at,
    DATE_TRUNC('day', l.created_at) AS quiz_date
  FROM
    public.leaderboard l
),
daily_averages AS (
  SELECT
    user_id,
    quiz_date,
    category_id,
    AVG(accuracy) AS avg_accuracy,
    COUNT(*) AS quiz_count
  FROM
    user_quiz_results
  GROUP BY
    user_id, quiz_date, category_id
)
SELECT
  user_id,
  quiz_date,
  category_id,
  avg_accuracy,
  quiz_count,
  AVG(avg_accuracy) OVER (
    PARTITION BY user_id, category_id
    ORDER BY quiz_date
    ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
  ) AS seven_day_trend,
  CASE
    WHEN LAG(avg_accuracy) OVER (PARTITION BY user_id, category_id ORDER BY quiz_date) IS NULL THEN 0
    ELSE avg_accuracy - LAG(avg_accuracy) OVER (PARTITION BY user_id, category_id ORDER BY quiz_date)
  END AS daily_change
FROM
  daily_averages
ORDER BY
  user_id, category_id, quiz_date;

-- Create function to get user rank
CREATE OR REPLACE FUNCTION public.get_user_rank(
  p_user_id UUID,
  p_quiz_type TEXT DEFAULT NULL,
  p_difficulty TEXT DEFAULT NULL,
  p_category_id UUID DEFAULT NULL,
  p_time_frame TEXT DEFAULT 'all'
) RETURNS INTEGER AS $$
DECLARE
  v_rank INTEGER;
  v_query TEXT;
  v_time_condition TEXT := '';
BEGIN
  -- Set time condition based on time frame
  IF p_time_frame = 'day' THEN
    v_time_condition := ' AND created_at >= NOW() - INTERVAL ''1 day''';
  ELSIF p_time_frame = 'week' THEN
    v_time_condition := ' AND created_at >= NOW() - INTERVAL ''7 days''';
  ELSIF p_time_frame = 'month' THEN
    v_time_condition := ' AND created_at >= NOW() - INTERVAL ''30 days''';
  END IF;
  
  -- Build dynamic query
  v_query := 'SELECT COUNT(*) + 1 FROM public.leaderboard WHERE score > (
    SELECT MAX(score) FROM public.leaderboard 
    WHERE user_id = $1';
  
  -- Add filters
  IF p_quiz_type IS NOT NULL THEN
    v_query := v_query || ' AND quiz_type = $2';
  END IF;
  
  IF p_difficulty IS NOT NULL THEN
    v_query := v_query || ' AND difficulty = $3';
  END IF;
  
  IF p_category_id IS NOT NULL THEN
    v_query := v_query || ' AND category_id = $4';
  END IF;
  
  -- Add time condition
  v_query := v_query || v_time_condition || ')';
  
  -- Add same filters to outer query
  IF p_quiz_type IS NOT NULL THEN
    v_query := v_query || ' AND quiz_type = $2';
  END IF;
  
  IF p_difficulty IS NOT NULL THEN
    v_query := v_query || ' AND difficulty = $3';
  END IF;
  
  IF p_category_id IS NOT NULL THEN
    v_query := v_query || ' AND category_id = $4';
  END IF;
  
  -- Add time condition to outer query
  v_query := v_query || v_time_condition;
  
  -- Execute query with parameters
  EXECUTE v_query INTO v_rank USING 
    p_user_id,
    p_quiz_type,
    p_difficulty,
    p_category_id;
  
  RETURN v_rank;
END;
$$ LANGUAGE plpgsql;

-- Set up RLS policies

-- Leaderboard policies
ALTER TABLE public.leaderboard ENABLE ROW LEVEL SECURITY;

-- Anyone can read leaderboard entries
CREATE POLICY "Anyone can read leaderboard entries" 
  ON public.leaderboard FOR SELECT USING (true);

-- Users can only insert their own entries
CREATE POLICY "Users can insert their own entries" 
  ON public.leaderboard FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Users can only update their own entries
CREATE POLICY "Users can update their own entries" 
  ON public.leaderboard FOR UPDATE 
  USING (auth.uid() = user_id);

-- User statistics policies
ALTER TABLE public.user_statistics ENABLE ROW LEVEL SECURITY;

-- Users can only read their own statistics
CREATE POLICY "Users can read their own statistics" 
  ON public.user_statistics FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can only insert their own statistics
CREATE POLICY "Users can insert their own statistics" 
  ON public.user_statistics FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Users can only update their own statistics
CREATE POLICY "Users can update their own statistics" 
  ON public.user_statistics FOR UPDATE 
  USING (auth.uid() = user_id);

-- User achievements policies
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;

-- Users can read their own achievements
CREATE POLICY "Users can read their own achievements" 
  ON public.user_achievements FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can insert their own achievements
CREATE POLICY "Users can insert their own achievements" 
  ON public.user_achievements FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- User activity log policies
ALTER TABLE public.user_activity_log ENABLE ROW LEVEL SECURITY;

-- Users can only read their own activity
CREATE POLICY "Users can read their own activity" 
  ON public.user_activity_log FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can only insert their own activity
CREATE POLICY "Users can insert their own activity" 
  ON public.user_activity_log FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- User preferences policies
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Users can only read their own preferences
CREATE POLICY "Users can read their own preferences" 
  ON public.user_preferences FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can only insert their own preferences
CREATE POLICY "Users can insert their own preferences" 
  ON public.user_preferences FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Users can only update their own preferences
CREATE POLICY "Users can update their own preferences" 
  ON public.user_preferences FOR UPDATE 
  USING (auth.uid() = user_id);

-- Add trigger to update user statistics when a quiz is completed
CREATE OR REPLACE FUNCTION public.update_user_statistics()
RETURNS TRIGGER AS $$
DECLARE
  v_category_id UUID;
BEGIN
  -- Update overall statistics in profiles table
  UPDATE public.profiles
  SET 
    total_questions = COALESCE(total_questions, 0) + NEW.question_count,
    correct_answers = COALESCE(correct_answers, 0) + NEW.score,
    quiz_completed = COALESCE(quiz_completed, 0) + 1,
    last_active = NOW()
  WHERE id = NEW.user_id;
  
  -- Update category-specific statistics if category is provided
  IF NEW.category_id IS NOT NULL THEN
    v_category_id := NEW.category_id;
    
    -- Insert or update user statistics for this category
    INSERT INTO public.user_statistics (
      user_id,
      category_id,
      questions_attempted,
      questions_correct,
      time_spent,
      last_attempted
    ) VALUES (
      NEW.user_id,
      v_category_id,
      NEW.question_count,
      NEW.score,
      NEW.time_taken,
      NOW()
    )
    ON CONFLICT (user_id, category_id) DO UPDATE SET
      questions_attempted = public.user_statistics.questions_attempted + NEW.question_count,
      questions_correct = public.user_statistics.questions_correct + NEW.score,
      time_spent = public.user_statistics.time_spent + NEW.time_taken,
      last_attempted = NOW();
  END IF;
  
  -- Check for achievements
  -- Perfect score achievement
  IF NEW.score = NEW.max_score AND NEW.max_score >= 5 THEN
    INSERT INTO public.user_achievements (user_id, achievement_id)
    VALUES (NEW.user_id, 'perfect_score')
    ON CONFLICT (user_id, achievement_id) DO NOTHING;
  END IF;
  
  -- First quiz completed achievement
  INSERT INTO public.user_achievements (user_id, achievement_id)
  VALUES (NEW.user_id, 'first_quiz_completed')
  ON CONFLICT (user_id, achievement_id) DO NOTHING;
  
  -- Check for ten quizzes completed achievement
  IF (SELECT COUNT(*) FROM public.leaderboard WHERE user_id = NEW.user_id) >= 10 THEN
    INSERT INTO public.user_achievements (user_id, achievement_id)
    VALUES (NEW.user_id, 'ten_quizzes_completed')
    ON CONFLICT (user_id, achievement_id) DO NOTHING;
  END IF;
  
  -- Check for hundred questions achievement
  IF (SELECT COALESCE(total_questions, 0) FROM public.profiles WHERE id = NEW.user_id) >= 100 THEN
    INSERT INTO public.user_achievements (user_id, achievement_id)
    VALUES (NEW.user_id, 'hundred_questions')
    ON CONFLICT (user_id, achievement_id) DO NOTHING;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for leaderboard inserts
CREATE TRIGGER update_statistics_on_quiz_completion
AFTER INSERT ON public.leaderboard
FOR EACH ROW
EXECUTE FUNCTION public.update_user_statistics();

-- Add streak tracking
CREATE OR REPLACE FUNCTION public.update_user_streak()
RETURNS TRIGGER AS $$
DECLARE
  v_last_active TIMESTAMP WITH TIME ZONE;
  v_current_streak INTEGER;
BEGIN
  -- Get user's last active date and current streak
  SELECT last_active, COALESCE(streak_days, 0)
  INTO v_last_active, v_current_streak
  FROM public.profiles
  WHERE id = NEW.user_id;
  
  -- If this is the first activity or last activity was more than 48 hours ago, reset streak
  IF v_last_active IS NULL OR NOW() - v_last_active > INTERVAL '48 hours' THEN
    UPDATE public.profiles
    SET streak_days = 1
    WHERE id = NEW.user_id;
  -- If last activity was between 20-48 hours ago (different day), increment streak
  ELSIF NOW() - v_last_active > INTERVAL '20 hours' AND NOW() - v_last_active <= INTERVAL '48 hours' THEN
    UPDATE public.profiles
    SET streak_days = v_current_streak + 1
    WHERE id = NEW.user_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for streak tracking
CREATE TRIGGER update_streak_on_quiz_completion
AFTER INSERT ON public.leaderboard
FOR EACH ROW
EXECUTE FUNCTION public.update_user_streak();