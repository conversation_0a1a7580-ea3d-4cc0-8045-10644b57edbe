import { createClient } from '@supabase/supabase-js';

// Test using the publishable key with Supabase client
async function testPublishableKey() {
  console.log('Testing Supabase with publishable key...\n');
  
  // Configuration
  const supabaseUrl = 'https://bkuowoowlmwranfoliea.supabase.co';
  const supabasePublishableKey = 'sb_publishable_bZkq0HoXxYxAHoaU3mDk0Q_iWsw7zCC';
  
  console.log('Supabase URL:', supabaseUrl);
  console.log('Publishable Key:', supabasePublishableKey ? 'Present (starts with ' + supabasePublishableKey.substring(0, 15) + '...)' : 'Missing');
  
  try {
    // Create Supabase client with publishable key
    const supabase = createClient(supabaseUrl, supabasePublishableKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
      }
    });
    
    console.log('\n1. Testing basic connection...');
    const { data: tagsData, error: tagsError } = await supabase
      .from('tags')
      .select('id, name, type')
      .limit(3);
    
    if (tagsError) {
      console.error('❌ Tags query failed:', tagsError.message);
    } else {
      console.log('✅ Tags query successful!');
      console.log('Sample tags:', tagsData);
    }
    
    console.log('\n2. Testing questions table...');
    const { data: questionsData, error: questionsError } = await supabase
      .from('questions')
      .select('id, question_text')
      .limit(2);
    
    if (questionsError) {
      console.error('❌ Questions query failed:', questionsError.message);
    } else {
      console.log('✅ Questions query successful!');
      console.log('Sample questions:', questionsData);
    }
    
    console.log('\n3. Testing authentication capabilities...');
    // Note: With publishable key, we can only use public authentication methods
    // like signUp, signIn, etc. but not admin functions
    const authMethods = [
      'signUp', 'signIn', 'signOut', 'resetPasswordForEmail',
      'onAuthStateChange', 'getSession', 'getUser'
    ];
    console.log('Available auth methods with publishable key:', authMethods.join(', '));
    
    // Get current session (will be null if not logged in)
    const { data: sessionData } = await supabase.auth.getSession();
    console.log('Current session:', sessionData?.session ? 'Active' : 'None');
    
    return {
      success: !tagsError && !questionsError,
      message: 'Supabase client with publishable key is working correctly!'
    };
    
  } catch (error) {
    console.error('❌ Test failed with exception:', error.message);
    return {
      success: false,
      message: `Test failed: ${error.message}`
    };
  }
}

// Run the test
testPublishableKey().then(result => {
  console.log('\n=== TEST SUMMARY ===');
  console.log(result.success ? '✅ SUCCESS' : '❌ FAILED');
  console.log(result.message);
});