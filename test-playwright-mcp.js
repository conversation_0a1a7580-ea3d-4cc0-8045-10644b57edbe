import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function testPlaywrightMCP() {
  console.log('Testing Playwright MCP server...\n');
  
  const transport = new StdioClientTransport({
    command: 'npx',
    args: ['-y', 'playwright-mcp'],
    shell: true,
    env: process.env,
  });

  const client = new Client({
    name: 'playwright-test-client',
    version: '1.0.0',
  });

  try {
    await client.connect(transport);
    
    // Discover available tools
    console.log('1. Discovering Playwright tools...');
    const tools = await client.listTools();
    console.log('Available tools:');
    tools.tools.forEach(tool => {
      console.log(`- ${tool.name}: ${tool.description}`);
    });
    
    console.log('\n2. Testing your USMLE Trivia App...');
    
    // Test 1: Navigate to your app
    const navigateResult = await client.callTool({
      name: 'playwright_navigate',
      arguments: {
        url: 'http://localhost:5173'
      }
    });
    console.log('Navigation result:', navigateResult.content[0].text);
    
    // Test 2: Take a screenshot of the homepage
    const screenshotResult = await client.callTool({
      name: 'playwright_screenshot',
      arguments: {
        filename: 'usmle-app-homepage.png'
      }
    });
    console.log('Screenshot taken:', screenshotResult.content[0].text);
    
    // Test 3: Get visible text content
    const textResult = await client.callTool({
      name: 'playwright_get_visible_text',
      arguments: {}
    });
    console.log('Page content preview:', textResult.content[0].text.substring(0, 200) + '...');
    
    // Test 4: Try to click on a quiz button (if it exists)
    try {
      const clickResult = await client.callTool({
        name: 'playwright_click',
        arguments: {
          selector: 'button:has-text("Start Quiz"), button:has-text("Practice"), [data-testid="start-quiz"]'
        }
      });
      console.log('Quiz button clicked:', clickResult.content[0].text);
    } catch (error) {
      console.log('No quiz button found or clickable');
    }
    
    // Test 5: Take another screenshot after interaction
    const finalScreenshot = await client.callTool({
      name: 'playwright_screenshot',
      arguments: {
        filename: 'usmle-app-after-interaction.png'
      }
    });
    console.log('Final screenshot:', finalScreenshot.content[0].text);
    
  } catch (error) {
    console.error('Error testing Playwright MCP:', error);
  } finally {
    await client.close();
  }
}

testPlaywrightMCP();