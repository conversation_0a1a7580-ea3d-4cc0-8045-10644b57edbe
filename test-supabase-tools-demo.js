import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function testSupabaseTools() {
  console.log('Testing various Supabase MCP tools...\n');
  
  const transport = new StdioClientTransport({
    command: 'npx',
    args: ['-y', '@supabase/mcp-server-supabase@latest', '--project-ref=bkuowoowlmwranfoliea', '--read-only'],
    shell: true,
    env: {
      SUPABASE_ACCESS_TOKEN: '********************************************',
    },
  });

  const client = new Client({
    name: 'supabase-tools-demo',
    version: '1.0.0',
  });

  try {
    await client.connect(transport);
    
    // Test 1: List tables
    console.log('1. Listing tables...');
    const tables = await client.callTool({
      name: 'list_tables',
      arguments: { schemas: ['public'] }
    });
    console.log('Tables:', JSON.stringify(tables.content, null, 2));
    
    // Test 2: Execute SQL query
    console.log('\n2. Executing SQL query...');
    const sqlResult = await client.callTool({
      name: 'execute_sql',
      arguments: { query: 'SELECT COUNT(*) as total_questions FROM questions;' }
    });
    console.log('Query result:', JSON.stringify(sqlResult.content, null, 2));
    
    // Test 3: Get project URL
    console.log('\n3. Getting project URL...');
    const projectUrl = await client.callTool({
      name: 'get_project_url',
      arguments: {}
    });
    console.log('Project URL:', JSON.stringify(projectUrl.content, null, 2));
    
    // Test 4: Get anon key
    console.log('\n4. Getting anon key...');
    const anonKey = await client.callTool({
      name: 'get_anon_key',
      arguments: {}
    });
    console.log('Anon key:', JSON.stringify(anonKey.content, null, 2));
    
  } catch (error) {
    console.error('Error testing tools:', error);
  } finally {
    await client.close();
  }
}

testSupabaseTools();