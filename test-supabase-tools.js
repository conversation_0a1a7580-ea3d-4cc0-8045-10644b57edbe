import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function discoverSupabaseTools() {
  console.log('Discovering Supabase MCP tools...\n');
  
  const transport = new StdioClientTransport({
    command: 'npx',
    args: ['-y', '@supabase/mcp-server-supabase@latest', '--project-ref=bkuowoowlmwranfoliea', '--read-only'],
    shell: true,
    env: {
      SUPABASE_ACCESS_TOKEN: process.env.SUPABASE_TOKEN || '********************************************',
    },
  });

  const client = new Client({
    name: 'supabase-tool-discovery',
    version: '1.0.0',
  });

  try {
    await client.connect(transport);
    
    // List available tools
    const tools = await client.listTools();
    console.log('Available tools:');
    console.log(JSON.stringify(tools, null, 2));
    
    // List available resources
    const resources = await client.listResources();
    console.log('\nAvailable resources:');
    console.log(JSON.stringify(resources, null, 2));
    
  } catch (error) {
    console.error('Error discovering tools:', error);
  } finally {
    await client.close();
  }
}

discoverSupabaseTools();