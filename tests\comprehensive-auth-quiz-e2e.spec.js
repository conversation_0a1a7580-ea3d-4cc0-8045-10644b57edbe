import { test, expect } from '@playwright/test';

/**
 * Comprehensive Authentication and Quiz Flow E2E Tests
 * Tests complete user journeys from authentication through quiz completion
 */

test.describe('Authentication E2E and Quiz Flow Tests', () => {
  // Test configuration
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    fullName: 'Test User'
  };

  const REAL_USER = {
    email: '<EMAIL>',
    password: 'Jimkali90#'
  };

  // Helper function to login
  async function loginUser(page, user = REAL_USER) {
    console.log(`🔐 Logging in user: ${user.email}`);
    
    await page.goto('/login', { timeout: 30000 });
    await page.waitForLoadState('networkidle');
    
    // Fill login form
    await page.fill('input[type="email"]', user.email);
    await page.fill('input[type="password"]', user.password);
    
    // Wait for submit button to be enabled
    await page.waitForFunction(() => {
      const button = document.querySelector('button[type="submit"]');
      return button && !button.disabled;
    }, { timeout: 10000 });
    
    // Submit form
    await page.click('button[type="submit"]');
    await page.waitForURL('/', { timeout: 15000 });
    
    console.log('✅ Login successful');
    return true;
  }

  // Helper function to logout
  async function logoutUser(page) {
    console.log('🚪 Logging out user');
    
    // Look for logout/profile menu
    const profileButton = page.locator('[data-testid="profile-menu"], .user-menu, button:has-text("Profile")').first();
    if (await profileButton.isVisible()) {
      await profileButton.click();
      await page.waitForTimeout(1000);
      
      const logoutButton = page.locator('button:has-text("Logout"), button:has-text("Sign Out")').first();
      if (await logoutButton.isVisible()) {
        await logoutButton.click();
        await page.waitForURL('/login', { timeout: 10000 });
        console.log('✅ Logout successful');
        return true;
      }
    }
    
    // Alternative: direct navigation to logout
    await page.goto('/login');
    console.log('✅ Navigated to login page');
    return true;
  }

  // Helper function to start quiz
  async function startQuiz(page, quizType = 'quick-quiz') {
    console.log(`🎯 Starting ${quizType}`);
    
    await page.goto(`/${quizType}`, { timeout: 30000 });
    await page.waitForLoadState('networkidle');
    
    // Wait for quiz to load
    await page.waitForTimeout(5000);
    
    // Check if quiz loaded successfully
    const hasQuestion = await page.locator('.question, [data-testid="question"]').count() > 0;
    const hasOptions = await page.locator('button:has-text("A)"), button:has-text("B)"), .option-button').count() > 0;
    
    console.log(`Quiz loaded - Question: ${hasQuestion}, Options: ${hasOptions}`);
    return { hasQuestion, hasOptions };
  }

  test.beforeEach(async ({ page }) => {
    // Set up console logging
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ Console Error:', msg.text());
      }
    });

    // Set up error handling
    page.on('pageerror', error => {
      console.log('💥 Page Error:', error.message);
    });
  });

  test('Complete Authentication Flow - Login and Logout', async ({ page }) => {
    console.log('🧪 Testing complete authentication flow');

    // Test login
    await loginUser(page);
    
    // Verify user is logged in (check for authenticated UI elements)
    const isAuthenticated = await page.locator('button:has-text("Profile"), .user-menu, [data-testid="user-avatar"]').count() > 0;
    expect(isAuthenticated).toBeTruthy();
    
    // Take screenshot of authenticated state
    await page.screenshot({ path: 'auth-test-logged-in.png', fullPage: true });
    
    // Test logout
    await logoutUser(page);
    
    // Verify user is logged out
    await page.waitForTimeout(2000);
    const hasLoginButton = await page.locator('button:has-text("Sign In"), a:has-text("Login")').count() > 0;
    expect(hasLoginButton).toBeTruthy();
    
    console.log('✅ Authentication flow test completed');
  });

  test('Quick Quiz Complete Flow', async ({ page }) => {
    console.log('🧪 Testing Quick Quiz complete flow');

    // Login first
    await loginUser(page);
    
    // Start quick quiz
    const quizState = await startQuiz(page, 'quick-quiz');
    
    if (quizState.hasQuestion && quizState.hasOptions) {
      console.log('📝 Quiz loaded successfully, testing interaction');
      
      // Answer a few questions
      for (let i = 0; i < 3; i++) {
        console.log(`Answering question ${i + 1}`);
        
        // Find and click an answer option
        const options = page.locator('button:has-text("A)"), button:has-text("B)"), .option-button');
        const optionCount = await options.count();
        
        if (optionCount > 0) {
          await options.first().click();
          await page.waitForTimeout(2000);
          
          // Check for auto-advance or next button
          const nextButton = page.locator('button:has-text("Next"), button:has-text("Continue")');
          if (await nextButton.isVisible()) {
            await nextButton.click();
          }
          
          await page.waitForTimeout(1000);
        } else {
          console.log('⚠️ No answer options found');
          break;
        }
      }
      
      // Take screenshot of quiz in progress
      await page.screenshot({ path: 'quick-quiz-in-progress.png', fullPage: true });
      
    } else {
      console.log('⚠️ Quiz did not load properly');
    }
    
    console.log('✅ Quick Quiz flow test completed');
  });

  test('Timed Test Complete Flow', async ({ page }) => {
    console.log('🧪 Testing Timed Test complete flow');

    // Login first
    await loginUser(page);
    
    // Start timed test
    const quizState = await startQuiz(page, 'quiz/timed-test');
    
    if (quizState.hasQuestion && quizState.hasOptions) {
      console.log('⏱️ Timed test loaded successfully');
      
      // Check for timer
      const hasTimer = await page.locator('.timer, [data-testid="timer"]').count() > 0;
      console.log(`Timer present: ${hasTimer}`);
      
      // Answer one question
      const options = page.locator('button:has-text("A)"), button:has-text("B)"), .option-button');
      if (await options.count() > 0) {
        await options.first().click();
        await page.waitForTimeout(2000);
      }
      
      // Take screenshot
      await page.screenshot({ path: 'timed-test-in-progress.png', fullPage: true });
      
    } else {
      console.log('⚠️ Timed test did not load properly');
    }
    
    console.log('✅ Timed Test flow test completed');
  });

  test('Custom Quiz Configuration and Flow', async ({ page }) => {
    console.log('🧪 Testing Custom Quiz configuration and flow');

    // Login first
    await loginUser(page);
    
    // Navigate to custom quiz setup
    await page.goto('/custom-quiz', { timeout: 30000 });
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Take screenshot of custom quiz setup
    await page.screenshot({ path: 'custom-quiz-setup.png', fullPage: true });
    
    // Look for configuration options
    const hasCategories = await page.locator('select, .category-selector, button:has-text("Category")').count() > 0;
    const hasQuestionCount = await page.locator('input[type="number"], .question-count').count() > 0;
    const hasStartButton = await page.locator('button:has-text("Start"), button:has-text("Begin")').count() > 0;
    
    console.log(`Custom Quiz Config - Categories: ${hasCategories}, Question Count: ${hasQuestionCount}, Start Button: ${hasStartButton}`);
    
    if (hasStartButton) {
      // Try to start custom quiz
      await page.locator('button:has-text("Start"), button:has-text("Begin")').first().click();
      await page.waitForTimeout(5000);
      
      // Check if quiz started
      const quizStarted = await page.locator('.question, [data-testid="question"]').count() > 0;
      console.log(`Custom quiz started: ${quizStarted}`);
      
      if (quizStarted) {
        await page.screenshot({ path: 'custom-quiz-started.png', fullPage: true });
      }
    }
    
    console.log('✅ Custom Quiz flow test completed');
  });

  test('Error Handling and Edge Cases', async ({ page }) => {
    console.log('🧪 Testing error handling and edge cases');

    // Test invalid login
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');
    
    // Wait for error message
    await page.waitForTimeout(3000);
    const hasError = await page.locator('.error, [data-testid="error"], .text-red').count() > 0;
    console.log(`Login error handling: ${hasError}`);
    
    // Test direct quiz access without login
    await page.goto('/quick-quiz');
    await page.waitForTimeout(3000);
    
    // Should either redirect to login or show auth prompt
    const currentUrl = page.url();
    const isRedirectedToLogin = currentUrl.includes('/login');
    const hasAuthPrompt = await page.locator('button:has-text("Sign In"), .auth-required').count() > 0;
    
    console.log(`Quiz protection - Redirected: ${isRedirectedToLogin}, Auth Prompt: ${hasAuthPrompt}`);
    
    await page.screenshot({ path: 'error-handling-test.png', fullPage: true });
    
    console.log('✅ Error handling test completed');
  });

  test('Cross-Feature Integration Test', async ({ page }) => {
    console.log('🧪 Testing cross-feature integration');

    // Complete authentication and quiz flow
    await loginUser(page);
    
    // Test navigation between different quiz types
    const quizTypes = ['quick-quiz', 'quiz/timed-test', 'custom-quiz'];
    
    for (const quizType of quizTypes) {
      console.log(`Testing navigation to ${quizType}`);
      
      await page.goto(`/${quizType}`, { timeout: 30000 });
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      // Take screenshot
      await page.screenshot({ path: `integration-${quizType.replace('/', '-')}.png`, fullPage: true });
      
      // Check if page loaded without errors
      const hasContent = await page.locator('body').count() > 0;
      expect(hasContent).toBeTruthy();
    }
    
    // Test profile/settings access
    await page.goto('/profile', { timeout: 30000 });
    await page.waitForTimeout(3000);
    await page.screenshot({ path: 'integration-profile.png', fullPage: true });
    
    console.log('✅ Integration test completed');
  });
});
