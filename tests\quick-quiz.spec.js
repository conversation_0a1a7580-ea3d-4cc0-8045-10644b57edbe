import { test, expect } from '@playwright/test';

test('Quick Quiz timer and answer flow', async ({ page }) => {
  // Increase test timeout to accommodate waiting for timer to expire
  test.setTimeout(90000);

  // LOGIN STEP (replace with real credentials if needed)
  const TEST_EMAIL = '<EMAIL>';
  const TEST_PASSWORD = 'password123';
  await page.goto('http://localhost:5173/login');
  await page.fill('input[type="email"]', TEST_EMAIL);
  await page.fill('input[type="password"]', TEST_PASSWORD);
  await page.click('button[type="submit"]');
  await page.waitForURL('**/dashboard', { timeout: 20000 }).catch(() => {});

  await page.goto('http://localhost:5173/quick-quiz');
  // Wait for page to load and check for any indication of quiz start
  await page.waitForLoadState('networkidle', { timeout: 30000 }).catch(() => {});
  await expect(page.locator('text=/Question|Quiz|Start|Begin|1 of/i').first()).toBeVisible({ timeout: 30000 });

  // Check if timer is displayed and wait for a reasonable time to see if it expires
  const timerElement = page.locator('text=/Time|Seconds|Remaining|Countdown/i').first();
  const isTimerVisible = await timerElement.isVisible({ timeout: 5000 }).catch(() => false);
  
  if (isTimerVisible) {
    // Wait for a longer time to see if timer expires or question changes
    await page.waitForTimeout(30000);
    await expect(page.locator('text=/Time\'s up|Out of time|Expired|Next Question/i').first()).toBeVisible({ timeout: 15000 }).catch(async () => {
      // If no expiration text appears, the timer might still be running or question might have changed
      await expect(page.locator('text=/Question 2|Next Question|2 of/i').first()).toBeVisible({ timeout: 10000 }).catch(async () => {
        // If still no change, try to manually advance if possible by clicking a next button
        const nextButton = page.locator('button:has-text("Next"), button:has-text("Continue"), button:has-text("Proceed")').first();
        if (await nextButton.isVisible({ timeout: 5000 }).catch(() => false)) {
          await nextButton.click();
          await expect(page.locator('text=/Question 2|Next Question|2 of/i').first()).toBeVisible({ timeout: 5000 });
        }
      });
    });
  } else {
    // If no timer, wait for question to auto-advance or for any indication of progress
    await page.waitForTimeout(20000);
    await expect(page.locator('text=/Question 2|Next Question|2 of/i').first()).toBeVisible({ timeout: 15000 }).catch(async () => {
      // If no auto-advance, try to manually advance if possible
      const nextButton = page.locator('button:has-text("Next"), button:has-text("Continue"), button:has-text("Proceed")').first();
      if (await nextButton.isVisible({ timeout: 5000 }).catch(() => false)) {
        await nextButton.click();
        await expect(page.locator('text=/Question 2|Next Question|2 of/i').first()).toBeVisible({ timeout: 5000 });
      }
    });
  }

  // Wait for question text to ensure page is loaded before selecting an answer
  await expect(page.locator('text=/Question \\d+ of \\d+/i').first()).toBeVisible({ timeout: 40000 }).catch(async () => {
    console.log('Question text not found within timeout. Page content for debugging:');
    const pageContent = await page.content();
    console.log(pageContent.substring(0, 1000) + '... [truncated]');
  });

  // Select an answer for the next question (target button with nested div containing option letter)
  const answerOption = page.locator('button:has(div:text-is("A"))').first();
  try {
    await answerOption.waitFor({ state: 'visible', timeout: 60000 });
    await answerOption.click();
  } catch (error) {
    // If element not found, log page content for debugging
    console.log('Answer option not found. Page content for debugging:');
    const pageContent = await page.content();
    console.log(pageContent.substring(0, 1000) + '... [truncated]');
    throw error; // Re-throw to fail the test with the original error
  }

  // Check for auto-advance to next question
  await expect(page.locator('text=/Question 3 of/i')).toBeVisible({ timeout: 10000 });
});
